import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/user_model.dart';
import '../../../themes/app_colors.dart';
import '../controller/user_controller.dart';
import 'user_form.dart';

class UserView extends GetView<UserController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
        tooltip: 'إضافة مستخدم',
        onPressed: () =>
            Get.to(
                  () => UserForm(),
              transition: Transition.rightToLeft,
            ),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          if (scrollInfo.metrics.axis == Axis.vertical) {
            // Hide search when scrolling down
            if (scrollInfo.metrics.pixels > 10 &&
                controller.isSearchVisible.value) {
              controller.isSearchVisible.value = false;
            }
          }
          return false;
        },
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              backgroundColor: AppColors.primary,
              expandedHeight: 200.0,
              // Increased height
              floating: true,
              pinned: true,
              snap: false,
              stretch: true,
              elevation: 0,
              title: const Text(
                'إدارة المستخدمين',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
              actions: [
                // Hidable search icon
                Obx(() =>
                    IconButton(
                      icon: Icon(
                        controller.isSearchVisible.value ? Icons.close : Icons
                            .search,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        controller.isSearchVisible.value =
                        !controller.isSearchVisible.value;
                        if (!controller.isSearchVisible.value) {
                          controller.searchQuery.value = '';
                        }
                      },
                    )),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColors.primary,
                        AppColors.primary.withOpacity(0.8),
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.only(top: kToolbarHeight + 16),
                      child: SingleChildScrollView(
                        child: Obx(() {
                          return    controller.isSearchVisible.value
                              ? Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(15),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: TextField(
                                onChanged: (value) {
                                  controller.searchQuery.value = value;
                                },
                                decoration: InputDecoration(
                                  hintText: 'بحث عن مستخدم...',
                                  prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                                  suffixIcon: controller.searchQuery.value.isNotEmpty
                                      ? IconButton(
                                    icon: const Icon(Icons.clear, color: AppColors.primary),
                                    onPressed: () {
                                      controller.searchQuery.value = '';
                                    },
                                  )
                                      : null,
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                ),
                              ),
                            ),
                          ):
                           Column(
                            children: [
                              // Stats Container
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20.0, vertical: 10.0),
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment
                                        .spaceBetween,
                                    children: [
                                      _buildInfoColumn(
                                        'إجمالي المستخدمين',
                                        '${controller.users.length}',
                                        Icons.people,
                                      ),
                                      Container(
                                        width: 1,
                                        height: 40,
                                        color: Colors.white.withOpacity(0.2),
                                      ),
                                      _buildInfoColumn(
                                        'نشط',
                                        '${controller.users
                                            .where((user) =>
                                        user.status == UserStatus.active)
                                            .length}',
                                        Icons.check_circle,
                                      ),
                                      Container(
                                        width: 1,
                                        height: 40,
                                        color: Colors.white.withOpacity(0.2),
                                      ),
                                      _buildInfoColumn(
                                        'معطل',
                                        '${controller.users
                                            .where((user) =>
                                        user.status == UserStatus.inactive)
                                            .length}',
                                        Icons.block,
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // Sea
                              // rch Bar inside AppBar

                            ],
                          );
                        }),
                      ),
                    ),
                  ),
                ),
              ),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(30),
                ),
              ),
            ),
            // Filters
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 16, vertical: 16),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Icon(
                                  Icons.filter_list,
                                  color: AppColors.primary,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'تصفية',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            icon: const Icon(
                                Icons.refresh, color: AppColors.primary),
                            onPressed: () {
                              controller.selectedRole.value = null;
                              controller.selectedStatus.value = null;
                              controller.searchQuery.value = '';
                            },
                            tooltip: 'إعادة تعيين التصفية',
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<UserRole>(
                              value: controller.selectedRole.value,
                              decoration: InputDecoration(
                                labelText: 'الدور',
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              items: [
                                const DropdownMenuItem<UserRole>(
                                  value: null,
                                  child: Text('جميع الأدوار'),
                                ),
                                ...UserRole.values.map((role) =>
                                    DropdownMenuItem<UserRole>(
                                      value: role,
                                      child: Text(_getRoleText(role)),
                                    )),
                              ],
                              onChanged: (value) =>
                              controller.selectedRole.value = value,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<UserStatus>(
                              value: controller.selectedStatus.value,
                              decoration: InputDecoration(
                                labelText: 'الحالة',
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                              items: [
                                const DropdownMenuItem<UserStatus>(
                                  value: null,
                                  child: Text('جميع الحالات'),
                                ),
                                ...UserStatus.values.map((status) =>
                                    DropdownMenuItem<UserStatus>(
                                      value: status,
                                      child: Text(_getStatusText(status)),
                                    )),
                              ],
                              onChanged: (value) =>
                              controller.selectedStatus.value = value,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Users List
            Obx(() {
              if (controller.isLoading.value) {
                return const SliverFillRemaining(
                  child: Center(child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary),
                  )),
                );
              }

              final users = controller.filteredUsers;

              // With SliverAppBar, the visibility is handled automatically
              // But we'll still update our controller state for consistency
              controller.isFilterVisible.value = true;

              if (users.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.people_outline,
                            size: 64,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'لا يوجد مستخدمين',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'حاول تعديل عوامل التصفية أو إضافة مستخدم جديد',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة مستخدم'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: () => Get.to(() => UserForm()),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return SliverList(
                delegate: SliverChildBuilderDelegate(
                      (context, index) {
                    final user = users[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16),
                        leading: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _getStatusColor(user.status).withOpacity(
                                0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.person,
                            color: _getStatusColor(user.status),
                          ),
                        ),
                        title: Text(
                          user.name.isEmpty ? user.username : user.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text(
                              _getRoleText(user.role),
                              style: TextStyle(
                                color: AppColors.textSecondary,
                              ),
                            ),
                            Text(
                              _getStatusText(user.status),
                              style: TextStyle(
                                color: _getStatusColor(user.status),
                              ),
                            ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              color: AppColors.primary,
                              onPressed: () =>
                                  Get.to(() => UserForm(user: user)),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete),
                              color: Colors.red,
                              onPressed: () {
                                // Show confirmation dialog before deleting
                                Get.defaultDialog(
                                  title: 'تأكيد الحذف',
                                  titleStyle: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                  middleText: 'هل أنت متأكد من حذف هذا المستخدم؟ سيتم حذفه من قاعدة البيانات وحساب Firebase Auth.',
                                  textConfirm: 'حذف',
                                  textCancel: 'إلغاء',
                                  confirmTextColor: Colors.white,
                                  cancelTextColor: AppColors.primary,
                                  buttonColor: Colors.red,
                                  barrierDismissible: false,
                                  radius: 10,
                                  onConfirm: () {
                                    Get.back(); // Close the dialog
                                    controller.deleteUser(user.id);
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  childCount: users.length,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير';
      case UserRole.manager:
        return 'مدير قطاع';
      case UserRole.supervisor:
        return 'مدير ورشة';
      case UserRole.technician:
        return 'فني';
      default:
        return 'غير معروف';
    }
  }

  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      default:
        return 'غير معروف';
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return AppColors.success;
      case UserStatus.inactive:
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }
}
