import 'package:flutter/material.dart';
import 'car_part.dart';

/// Factory class to create van parts for different views
class VanPartsFactory {
  /// Creates all parts for the van
  static List<CarPart> createAllParts() {
    return [
      ...createFrontParts(),
      ...createBackParts(),
      ...createLeftSideParts(),
      ...createRightSideParts(),
      ...createTopParts(),
    ];
  }
  
  /// Creates parts for the front view
  static List<CarPart> createFrontParts() {
    return [
      // Windshield
      CarPart(
        id: 'front_windshield',
        name: 'front_windshield',
        view: CarView.front,
        path: _createFrontWindshieldPath(),
      ),
      // Hood
      <PERSON>Part(
        id: 'front_hood',
        name: 'hood',
        view: CarView.front,
        path: _createFrontHoodPath(),
      ),
      // Grill
      CarPart(
        id: 'front_grill',
        name: 'grill',
        view: CarView.front,
        path: _createFrontGrillPath(),
      ),
      // Left Headlight
      CarPart(
        id: 'front_headlight_left',
        name: 'left_headlight',
        view: CarView.front,
        path: _createFrontLeftHeadlightPath(),
      ),
      // Right Headlight
      CarPart(
        id: 'front_headlight_right',
        name: 'right_headlight',
        view: CarView.front,
        path: _createFrontRightHeadlightPath(),
      ),
      // Bumper
      CarPart(
        id: 'front_bumper',
        name: 'front_bumper',
        view: CarView.front,
        path: _createFrontBumperPath(),
      ),
    ];
  }
  
  /// Creates parts for the back view
  static List<CarPart> createBackParts() {
    return [
      // Rear Window
      CarPart(
        id: 'back_window',
        name: 'rear_window',
        view: CarView.back,
        path: _createBackWindowPath(),
      ),
      // Rear Door
      CarPart(
        id: 'back_door',
        name: 'rear_door',
        view: CarView.back,
        path: _createBackDoorPath(),
      ),
      // Left Taillight
      CarPart(
        id: 'back_taillight_left',
        name: 'left_taillight',
        view: CarView.back,
        path: _createBackLeftTaillightPath(),
      ),
      // Right Taillight
      CarPart(
        id: 'back_taillight_right',
        name: 'right_taillight',
        view: CarView.back,
        path: _createBackRightTaillightPath(),
      ),
      // Rear Bumper
      CarPart(
        id: 'back_bumper',
        name: 'rear_bumper',
        view: CarView.back,
        path: _createBackBumperPath(),
      ),
    ];
  }
  
  /// Creates parts for the left side view
  static List<CarPart> createLeftSideParts() {
    return [
      // Left Side Body
      CarPart(
        id: 'left_side_body',
        name: 'left_side_body',
        view: CarView.leftSide,
        path: _createLeftSideBodyPath(),
      ),
      // Left Front Door
      CarPart(
        id: 'left_front_door',
        name: 'left_front_door',
        view: CarView.leftSide,
        path: _createLeftFrontDoorPath(),
      ),
      // Left Sliding Door
      CarPart(
        id: 'left_sliding_door',
        name: 'left_sliding_door',
        view: CarView.leftSide,
        path: _createLeftSlidingDoorPath(),
      ),
      // Left Front Wheel
      CarPart(
        id: 'left_front_wheel',
        name: 'left_front_wheel',
        view: CarView.leftSide,
        path: _createLeftFrontWheelPath(),
      ),
      // Left Rear Wheel
      CarPart(
        id: 'left_rear_wheel',
        name: 'left_rear_wheel',
        view: CarView.leftSide,
        path: _createLeftRearWheelPath(),
      ),
      // Left Side Window
      CarPart(
        id: 'left_side_window',
        name: 'left_side_window',
        view: CarView.leftSide,
        path: _createLeftSideWindowPath(),
      ),
    ];
  }
  
  /// Creates parts for the right side view
  static List<CarPart> createRightSideParts() {
    return [
      // Right Side Body
      CarPart(
        id: 'right_side_body',
        name: 'right_side_body',
        view: CarView.rightSide,
        path: _createRightSideBodyPath(),
      ),
      // Right Front Door
      CarPart(
        id: 'right_front_door',
        name: 'right_front_door',
        view: CarView.rightSide,
        path: _createRightFrontDoorPath(),
      ),
      // Right Sliding Door
      CarPart(
        id: 'right_sliding_door',
        name: 'right_sliding_door',
        view: CarView.rightSide,
        path: _createRightSlidingDoorPath(),
      ),
      // Right Front Wheel
      CarPart(
        id: 'right_front_wheel',
        name: 'right_front_wheel',
        view: CarView.rightSide,
        path: _createRightFrontWheelPath(),
      ),
      // Right Rear Wheel
      CarPart(
        id: 'right_rear_wheel',
        name: 'right_rear_wheel',
        view: CarView.rightSide,
        path: _createRightRearWheelPath(),
      ),
      // Right Side Window
      CarPart(
        id: 'right_side_window',
        name: 'right_side_window',
        view: CarView.rightSide,
        path: _createRightSideWindowPath(),
      ),
    ];
  }
  
  /// Creates parts for the top view
  static List<CarPart> createTopParts() {
    return [
      // Roof
      CarPart(
        id: 'top_roof',
        name: 'roof',
        view: CarView.top,
        path: _createTopRoofPath(),
      ),
    ];
  }
  
  // Path creation methods for front view
  static Path _createFrontWindshieldPath() {
    final path = Path();
    // Define the windshield shape
    path.moveTo(20, 30);
    path.lineTo(80, 30);
    path.lineTo(75, 60);
    path.lineTo(25, 60);
    path.close();
    return path;
  }
  
  static Path _createFrontHoodPath() {
    final path = Path();
    // Define the hood shape
    path.moveTo(25, 60);
    path.lineTo(75, 60);
    path.lineTo(80, 80);
    path.lineTo(20, 80);
    path.close();
    return path;
  }
  
  static Path _createFrontGrillPath() {
    final path = Path();
    // Define the grill shape
    path.moveTo(30, 80);
    path.lineTo(70, 80);
    path.lineTo(70, 90);
    path.lineTo(30, 90);
    path.close();
    return path;
  }
  
  static Path _createFrontLeftHeadlightPath() {
    final path = Path();
    // Define the left headlight shape
    path.moveTo(25, 75);
    path.lineTo(35, 75);
    path.lineTo(35, 85);
    path.lineTo(25, 85);
    path.close();
    return path;
  }
  
  static Path _createFrontRightHeadlightPath() {
    final path = Path();
    // Define the right headlight shape
    path.moveTo(65, 75);
    path.lineTo(75, 75);
    path.lineTo(75, 85);
    path.lineTo(65, 85);
    path.close();
    return path;
  }
  
  static Path _createFrontBumperPath() {
    final path = Path();
    // Define the bumper shape
    path.moveTo(15, 90);
    path.lineTo(85, 90);
    path.lineTo(85, 100);
    path.lineTo(15, 100);
    path.close();
    return path;
  }
  
  // Path creation methods for back view
  static Path _createBackWindowPath() {
    final path = Path();
    // Define the back window shape
    path.moveTo(25, 30);
    path.lineTo(75, 30);
    path.lineTo(75, 60);
    path.lineTo(25, 60);
    path.close();
    return path;
  }
  
  static Path _createBackDoorPath() {
    final path = Path();
    // Define the back door shape
    path.moveTo(20, 60);
    path.lineTo(80, 60);
    path.lineTo(80, 90);
    path.lineTo(20, 90);
    path.close();
    return path;
  }
  
  static Path _createBackLeftTaillightPath() {
    final path = Path();
    // Define the left taillight shape
    path.moveTo(20, 75);
    path.lineTo(30, 75);
    path.lineTo(30, 85);
    path.lineTo(20, 85);
    path.close();
    return path;
  }
  
  static Path _createBackRightTaillightPath() {
    final path = Path();
    // Define the right taillight shape
    path.moveTo(70, 75);
    path.lineTo(80, 75);
    path.lineTo(80, 85);
    path.lineTo(70, 85);
    path.close();
    return path;
  }
  
  static Path _createBackBumperPath() {
    final path = Path();
    // Define the back bumper shape
    path.moveTo(15, 90);
    path.lineTo(85, 90);
    path.lineTo(85, 100);
    path.lineTo(15, 100);
    path.close();
    return path;
  }
  
  // Path creation methods for left side view
  static Path _createLeftSideBodyPath() {
    final path = Path();
    // Define the left side body shape
    path.moveTo(10, 50);
    path.lineTo(90, 50);
    path.lineTo(90, 80);
    path.lineTo(10, 80);
    path.close();
    return path;
  }
  
  static Path _createLeftFrontDoorPath() {
    final path = Path();
    // Define the left front door shape
    path.moveTo(20, 30);
    path.lineTo(40, 30);
    path.lineTo(40, 80);
    path.lineTo(20, 80);
    path.close();
    return path;
  }
  
  static Path _createLeftSlidingDoorPath() {
    final path = Path();
    // Define the left sliding door shape
    path.moveTo(45, 40);
    path.lineTo(75, 40);
    path.lineTo(75, 80);
    path.lineTo(45, 80);
    path.close();
    return path;
  }
  
  static Path _createLeftFrontWheelPath() {
    final path = Path();
    // Define the left front wheel shape
    path.addOval(Rect.fromCircle(center: const Offset(30, 90), radius: 10));
    return path;
  }
  
  static Path _createLeftRearWheelPath() {
    final path = Path();
    // Define the left rear wheel shape
    path.addOval(Rect.fromCircle(center: const Offset(70, 90), radius: 10));
    return path;
  }
  
  static Path _createLeftSideWindowPath() {
    final path = Path();
    // Define the left side window shape
    path.moveTo(45, 40);
    path.lineTo(75, 40);
    path.lineTo(75, 60);
    path.lineTo(45, 60);
    path.close();
    return path;
  }
  
  // Path creation methods for right side view
  static Path _createRightSideBodyPath() {
    final path = Path();
    // Define the right side body shape
    path.moveTo(10, 50);
    path.lineTo(90, 50);
    path.lineTo(90, 80);
    path.lineTo(10, 80);
    path.close();
    return path;
  }
  
  static Path _createRightFrontDoorPath() {
    final path = Path();
    // Define the right front door shape
    path.moveTo(20, 30);
    path.lineTo(40, 30);
    path.lineTo(40, 80);
    path.lineTo(20, 80);
    path.close();
    return path;
  }
  
  static Path _createRightSlidingDoorPath() {
    final path = Path();
    // Define the right sliding door shape
    path.moveTo(45, 40);
    path.lineTo(75, 40);
    path.lineTo(75, 80);
    path.lineTo(45, 80);
    path.close();
    return path;
  }
  
  static Path _createRightFrontWheelPath() {
    final path = Path();
    // Define the right front wheel shape
    path.addOval(Rect.fromCircle(center: const Offset(30, 90), radius: 10));
    return path;
  }
  
  static Path _createRightRearWheelPath() {
    final path = Path();
    // Define the right rear wheel shape
    path.addOval(Rect.fromCircle(center: const Offset(70, 90), radius: 10));
    return path;
  }
  
  static Path _createRightSideWindowPath() {
    final path = Path();
    // Define the right side window shape
    path.moveTo(45, 40);
    path.lineTo(75, 40);
    path.lineTo(75, 60);
    path.lineTo(45, 60);
    path.close();
    return path;
  }
  
  // Path creation methods for top view
  static Path _createTopRoofPath() {
    final path = Path();
    // Define the roof shape
    path.moveTo(20, 20);
    path.lineTo(80, 20);
    path.lineTo(80, 80);
    path.lineTo(20, 80);
    path.close();
    return path;
  }
}
