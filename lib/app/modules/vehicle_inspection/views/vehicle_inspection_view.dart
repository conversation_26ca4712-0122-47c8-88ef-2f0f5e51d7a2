import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/inspection_status.dart';
import '../../../themes/app_colors.dart';
import '../../../translations/vehicle_inspection_translations.dart';
import '../../maintenance/views/maintenance_checklist_view.dart';
import '../controllers/car_parts_selector_controller.dart';
import '../controllers/vehicle_inspection_controller.dart';
import '../widgets/car_selector_parts_widget.dart';

class VehicleInspectionView extends GetView<VehicleInspectionController> {
  const VehicleInspectionView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: const Text(
          'فحص المركبة',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Cairo',
          ),
        ),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(onPressed: () {
                          Get.back();
                        }, icon:Icon( Icons.arrow_back)),
          Obx(() => controller.isLoading.value
              ? const Center(
                  child: CircularProgressIndicator(color: Colors.white))
              : IconButton(
                  icon: const Icon(Icons.save, color: Colors.white),
                  onPressed: controller.saveInspection,
                )),
        ],
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header with curved bottom
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(40),
                    bottomRight: Radius.circular(40),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.primary,
                          child: Icon(
                            Icons.directions_car,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'تقرير فحص المركبة',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        DateTime.now().toString().split('.')[0],
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    _buildNotesCard(),
                    const SizedBox(height: 20),
                    _meterReading(),
                    const SizedBox(height: 20),
                    _buildInspectionItems(),
                    // Expanded(child: VanPartSelecto())
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotesCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.note_add,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'الملاحظات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            maxLines: 3,
            controller: controller.noteController,
            textDirection: TextDirection.rtl,
            style: const TextStyle(fontFamily: 'Cairo'),
            decoration: InputDecoration(
              hintText: 'أضف ملاحظات الفحص هنا...',
              hintStyle: const TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // TODO: Add notes handling in controller
            },
          ),
        ],
      ),
    );
  }

  Widget _meterReading() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            maxLines: 1,
            controller: controller.meterReadingController,
            textDirection: TextDirection.rtl,
            style: const TextStyle(fontFamily: 'Cairo'),
            textInputAction: TextInputAction.next,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'قراءة العداد',
              hintStyle: const TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // TODO: Add notes handling in controller
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInspectionItems() {
    // Create a car parts selector controller
    final carPartsController = Get.put(CarPartsSelectorController());

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Inspection header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.checklist,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'عناصر الفحص',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Maintenance checklist
          const Text(
            'قائمة الصيانة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 300,
            child: MaintenanceChecklistView(),
          ),

          const SizedBox(height: 24),

          // Car parts selector
          const Text(
            'حدد الأجزاء التالفة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 400,
            child: CarSelectorPartsWidget(
              controller: carPartsController,
              onPartSelected: (part) {
                // Handle part selection
                print('Selected part: ${part.name}');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: const Text(
              'العنصر',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
          ...InspectionStatus.values.map((status) => Expanded(
                child: Center(
                  child: Text(
                    _getStatusArabicText(status),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildInspectionItem(String key, InspectionStatus currentStatus) {
    final translation = VehicleInspectionTranslations
        .translations[Get.locale?.languageCode ?? 'ar']?[key];

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: SelectableText(
              translation ?? key,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
          ),
          ...InspectionStatus.values.map((status) => Expanded(
                child: Center(
                  child: Radio<InspectionStatus>(
                    value: status,
                    groupValue: currentStatus,
                    onChanged: (InspectionStatus? value) {
                      if (value != null) {
                        controller.inspection.update((val) {
                          if (val != null) {
                            val.items[key] = value;
                          }
                        });
                      }
                    },
                    activeColor: _getStatusColor(status),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  String _getStatusArabicText(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.repair:
        return 'إصلاح';
      case InspectionStatus.checkOk:
        return 'جيد';
      case InspectionStatus.safe:
        return 'آمن';
    }
  }

  Color _getStatusColor(InspectionStatus status) {
    switch (status) {
      case InspectionStatus.repair:
        return AppColors.error;
      case InspectionStatus.checkOk:
        return AppColors.success;
      case InspectionStatus.safe:
        return AppColors.info;
    }
  }
}
