import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';
import '../cards/report_card.dart';

class RecordsTab extends StatelessWidget {
  final ReportsController controller;

  const RecordsTab({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildLoadingState();
      }

      if (controller.filteredEntries.isEmpty) {
        return _buildEmptyState();
      }

      return _buildRecordsList();
    });
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 100,
            height: 100,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              strokeWidth: 6,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جاري تحميل البيانات...'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.network(
            'https://assets9.lottiefiles.com/packages/lf20_wnqlfojb.json',
            width: 200,
            height: 200,
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد سجلات متاحة'.tr,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'حاول تغيير معايير التصفية للعثور على سجلات'.tr,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.clearFilters(),
            icon: const Icon(Icons.refresh),
            label: Text('إعادة ضبط الفلاتر'.tr),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                'عدد السجلات: ${controller.filteredEntries.length}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              _buildSortDropdown(),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: controller.filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = controller.filteredEntries[index];
              return ReportCard(entry: entry);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSortDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Obx(() => DropdownButton<SortOption>(
        value: controller.sortOption.value,
        underline: const SizedBox(),
        icon: const Icon(Icons.sort),
        borderRadius: BorderRadius.circular(8),
        items: [
          DropdownMenuItem(
            value: SortOption.dateDesc,
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text('الأحدث أولاً'.tr),
              ],
            ),
          ),
          DropdownMenuItem(
            value: SortOption.dateAsc,
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text('الأقدم أولاً'.tr),
              ],
            ),
          ),
          DropdownMenuItem(
            value: SortOption.statusAsc,
            child: Row(
              children: [
                const Icon(Icons.flag, size: 16),
                const SizedBox(width: 8),
                Text('حسب الحالة (تصاعدي)'.tr),
              ],
            ),
          ),
          DropdownMenuItem(
            value: SortOption.statusDesc,
            child: Row(
              children: [
                const Icon(Icons.flag, size: 16),
                const SizedBox(width: 8),
                Text('حسب الحالة (تنازلي)'.tr),
              ],
            ),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            controller.sortOption.value = value;
            controller.sortEntries();
          }
        },
      )),
    );
  }
}
