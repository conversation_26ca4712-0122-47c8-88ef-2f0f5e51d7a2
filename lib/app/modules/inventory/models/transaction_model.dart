
import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType { usage, purchase, sale, adjustment }

class TransactionModel {
  final String? id;
  final String productId;
  final String productName; // Denormalized
  final TransactionType type;
  final int quantityChange; // Negative for usage/sale/adjust-down, Positive for purchase/adjust-up
  final int quantityBefore;
  final int quantityAfter;
  final Timestamp timestamp;
  final String userId;
  final String? relatedJobId;
  final String? notes;

  TransactionModel({
    this.id,
    required this.productId,
    required this.productName,
    required this.type,
    required this.quantityChange,
    required this.quantityBefore,
    required this.quantityAfter,
    required this.timestamp,
    required this.userId,
    this.relatedJobId,
    this.notes,
  });

  factory TransactionModel.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map<String, dynamic>;
    return TransactionModel(
      id: doc.id,
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      type: TransactionType.values.firstWhere(
              (e) => e.toString() == 'TransactionType.${data['type']}',
          orElse: () => TransactionType.usage),
      // Handle potential errors
      quantityChange: data['quantityChange'] ?? 0,
      quantityBefore: data['quantityBefore'] ?? 0,
      quantityAfter: data['quantityAfter'] ?? 0,
      timestamp: data['timestamp'] ?? Timestamp.now(),
      userId: data['userId'] ?? '',
      relatedJobId: data['relatedJobId'],
      notes: data['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'type': type.name, // Store enum name as string
      'quantityChange': quantityChange,
      'quantityBefore': quantityBefore,
      'quantityAfter': quantityAfter,
      'timestamp': timestamp, // Should be set by caller or provider
      'userId': userId,
      'relatedJobId': relatedJobId,
      'notes': notes,
    };
  }
}