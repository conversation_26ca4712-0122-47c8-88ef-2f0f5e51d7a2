import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // ============ FONT FAMILIES ============
  static const String primaryFont = 'Roboto';
  static const String arabicFont = 'Cairo';

  // ============ LIGHT THEME TEXT STYLES ============
  
  static TextTheme lightTextTheme = TextTheme(
    // Display styles
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),

    // Headline styles
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),

    // Title styles
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.15,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.1,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),

    // Body styles
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      color: AppColors.lightTextSecondary,
      fontFamily: primaryFont,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      color: AppColors.lightTextTertiary,
      fontFamily: primaryFont,
    ),

    // Label styles
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      color: AppColors.lightTextPrimary,
      fontFamily: primaryFont,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      color: AppColors.lightTextSecondary,
      fontFamily: primaryFont,
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      color: AppColors.lightTextTertiary,
      fontFamily: primaryFont,
    ),
  );

  // ============ DARK THEME TEXT STYLES ============
  
  static TextTheme darkTextTheme = TextTheme(
    // Display styles
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),

    // Headline styles
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),

    // Title styles
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.15,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.1,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),

    // Body styles
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      color: AppColors.darkTextSecondary,
      fontFamily: primaryFont,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      color: AppColors.darkTextTertiary,
      fontFamily: primaryFont,
    ),

    // Label styles
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      color: AppColors.darkTextPrimary,
      fontFamily: primaryFont,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      color: AppColors.darkTextSecondary,
      fontFamily: primaryFont,
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      color: AppColors.darkTextTertiary,
      fontFamily: primaryFont,
    ),
  );

  // ============ ARABIC TEXT STYLES ============
  
  static TextTheme getArabicTextTheme(bool isDark) {
    final baseTheme = isDark ? darkTextTheme : lightTextTheme;
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(fontFamily: arabicFont),
      displayMedium: baseTheme.displayMedium?.copyWith(fontFamily: arabicFont),
      displaySmall: baseTheme.displaySmall?.copyWith(fontFamily: arabicFont),
      headlineLarge: baseTheme.headlineLarge?.copyWith(fontFamily: arabicFont),
      headlineMedium: baseTheme.headlineMedium?.copyWith(fontFamily: arabicFont),
      headlineSmall: baseTheme.headlineSmall?.copyWith(fontFamily: arabicFont),
      titleLarge: baseTheme.titleLarge?.copyWith(fontFamily: arabicFont),
      titleMedium: baseTheme.titleMedium?.copyWith(fontFamily: arabicFont),
      titleSmall: baseTheme.titleSmall?.copyWith(fontFamily: arabicFont),
      bodyLarge: baseTheme.bodyLarge?.copyWith(fontFamily: arabicFont),
      bodyMedium: baseTheme.bodyMedium?.copyWith(fontFamily: arabicFont),
      bodySmall: baseTheme.bodySmall?.copyWith(fontFamily: arabicFont),
      labelLarge: baseTheme.labelLarge?.copyWith(fontFamily: arabicFont),
      labelMedium: baseTheme.labelMedium?.copyWith(fontFamily: arabicFont),
      labelSmall: baseTheme.labelSmall?.copyWith(fontFamily: arabicFont),
    );
  }

  // ============ CUSTOM TEXT STYLES ============
  
  static TextStyle button = const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    fontFamily: primaryFont,
  );

  static TextStyle caption = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    fontFamily: primaryFont,
  );

  static TextStyle overline = const TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    letterSpacing: 1.5,
    fontFamily: primaryFont,
  );
}
