import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../data/models/user_model.dart';
import '../../../themes/app_colors.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class UserTile extends StatelessWidget {
  final UserModel user;
  final Function()? onTap;
  final Function()? onEdit;
  final Function()? onDelete;
  final Function()? onActivate;
  final Function()? onDeactivate;
  final bool isRTL;

  const UserTile({
    Key? key,
    required this.user,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onActivate,
    this.onDeactivate,
    this.isRTL = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(user.id),
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        dismissible: DismissiblePane(onDismissed: () {}),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppColors.info,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'edit'.tr,
            ),
          if (user.status == UserStatus.active && onDeactivate != null)
            SlidableAction(
              onPressed: (_) => onDeactivate!(),
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
              icon: Icons.block,
              label: 'deactivate'.tr,
            ),
          if (user.status != UserStatus.active && onActivate != null)
            SlidableAction(
              onPressed: (_) => onActivate!(),
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              icon: Icons.check_circle,
              label: 'activate'.tr,
            ),
        ],
      ),
      endActionPane: onDelete != null
          ? ActionPane(
              motion: const DrawerMotion(),
              children: [
                SlidableAction(
                  onPressed: (_) => onDelete!(),
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                  icon: Icons.delete,
                  label: 'delete'.tr,
                ),
              ],
            )
          : null,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
            children: [
              _buildAvatar(),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: isRTL
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  children: [
                    Row(
                      textDirection:
                          isRTL ? TextDirection.rtl : TextDirection.ltr,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            user.name,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildRoleBadge(),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user.email??'',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      textDirection:
                          isRTL ? TextDirection.rtl : TextDirection.ltr,
                      children: [
                        _buildStatusIndicator(),
                        const SizedBox(width: 8),
                        Text(
                          user.status==UserStatus.active ? 'active'.tr : 'inactive'.tr,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color:
                                user.status==UserStatus.active ? AppColors.success : AppColors.error,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 24,
      backgroundColor: AppColors.primary.withOpacity(0.1),
      child: Text(
              user.name[0].toUpperCase(),
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
    );
  }

  Widget _buildRoleBadge() {
    Color badgeColor;
    String roleText;

    switch (user.role) {
      case UserRole.admin:
        badgeColor = AppColors.primary;
        roleText = 'admin'.tr;
        break;
      case UserRole.supervisor:
        badgeColor = AppColors.success;
        roleText = 'driver'.tr;
        break;
      case UserRole.user:
        badgeColor = AppColors.info;
        roleText = 'viewer'.tr;
        break;
      default:
        badgeColor = Colors.grey;
        roleText = 'user'.tr;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        roleText,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: user.status==UserStatus.active ? AppColors.success : AppColors.error,
      ),
    );
  }
}
