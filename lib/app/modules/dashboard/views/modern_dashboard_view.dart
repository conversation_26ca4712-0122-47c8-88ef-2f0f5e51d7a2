import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:lottie/lottie.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../data/models/car_model.dart';
import '../../../data/language_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../../../routes/app_pages.dart';
import '../../../themes/app_colors.dart';

class DashboardView extends GetView<DashboardController> {
  const DashboardView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return LayoutBuilder(
      builder: (context, constraints) {
        bool isDesktop = constraints.maxWidth > 900;
        bool isWide = constraints.maxWidth > 700;

        // final drawerWidget = _buildDrawer(context);

        return Scaffold(
          // AppBar is hidden as requested
          backgroundColor: AppColors.surface,
          // drawer: !isDesktop ? drawerWidget : null,
          body: isDesktop
              ? Row(
            children: [
              // SizedBox(
              //   width: 240,
              //   child: drawerWidget,
              // ),
              // const VerticalDivider(width: 1),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Obx(() {
                    if (controller.isLoading.value) {
                      return const Center(
                          child: CircularProgressIndicator());
                    }
                    return SingleChildScrollView(
                      child: Directionality(
                        textDirection:
                        isRTL ? TextDirection.rtl : TextDirection.ltr,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildFilters(context, isWide),
                            const SizedBox(height: 32),
                            _buildStatCards(context, isWide),
                            const SizedBox(height: 32),
                            _buildCharts(context, isWide),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ],
          )
              : Padding(
            padding: const EdgeInsets.all(16.0),
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              return SingleChildScrollView(
                child: Directionality(
                  textDirection:
                  isRTL ? TextDirection.rtl : TextDirection.ltr,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildFilters(context, isWide),
                      const SizedBox(height: 24),
                      _buildStatCards(context, isWide),
                      const SizedBox(height: 24),
                      _buildCharts(context, isWide),
                    ],
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        color: AppColors.surface,
        child: Column(
          children: [
            // Header with gradient background
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withOpacity(0.8),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(32),
                  bottomRight: Radius.circular(32),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              width: double.infinity,
              child: Column(
                children: [
                  // Profile avatar with border
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.white,
                      child: Icon(Icons.person,
                          color: AppColors.primary, size: 50),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Dashboard title
                  Text(
                    'dashboard'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // User email
                  Obx(() => Text(
                    controller.userEmail.value.isEmpty
                        ? '<EMAIL>'
                        : controller.userEmail.value,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.85),
                      fontSize: 14,
                    ),
                  )),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Menu section title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.menu, color: AppColors.primary, size: 18),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'القائمة الرئيسية',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 16, thickness: 1, indent: 16, endIndent: 16),

            // Main menu items
            _buildDrawerItem(
              context: context,
              icon: Icons.dashboard_rounded,
              title: 'dashboard'.tr,
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.HOME);
              },
              isActive: true,
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.directions_car_rounded,
              title: 'cars'.tr,
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.CARS);
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.business,
              title: 'sectors'.tr,
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.SECTORS);
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.engineering,
              title: 'technicians'.tr,
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.TECHNICIAN);
              },
            ),

            // Reports section title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.analytics,
                        color: AppColors.primary, size: 18),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'التقارير والإحصائيات',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 16, thickness: 1, indent: 16, endIndent: 16),

            // Reports menu items
            _buildDrawerItem(
              context: context,
              icon: Icons.bar_chart,
              title: 'reports'.tr,
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.REPORTS);
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.tag,
              title: 'إدارة المشاكل الشائعة',
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.PROBLEM_TAGS_MANAGEMENT);
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.analytics,
              title: 'إحصائيات المشاكل',
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.PROBLEM_STATISTICS);
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.report_problem,
              title: 'تقارير المشاكل',
              onTap: () {
                Navigator.pop(context);
                Get.toNamed(Routes.PROBLEM_REPORTS);
              },
            ),

            // Settings section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.settings,
                        color: AppColors.primary, size: 18),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الإعدادات',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 16, thickness: 1, indent: 16, endIndent: 16),

            // Settings menu items
            _buildDrawerItem(
              context: context,
              icon: Icons.settings_rounded,
              title: 'settings'.tr,
              onTap: () {
                Navigator.pop(context);
                // Navigate to settings
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.language,
              title: 'language'.tr,
              onTap: () {
                Navigator.pop(context);
                // Show language selection dialog
                Get.find<LanguageController>().changeLanguage();
              },
            ),
            _buildDrawerItem(
              context: context,
              icon: Icons.logout,
              title: 'logout'.tr,
              onTap: () {
                Navigator.pop(context);
                // Implement logout
                Get.offAllNamed(Routes.LOGIN);
              },
              textColor: Colors.red,
              iconColor: Colors.red,
            ),

            const Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Text(
                '© 2024 Company',
                style: TextStyle(
                  color: AppColors.textSecondary.withOpacity(0.5),
                  fontSize: 13,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isActive = false,
    Color? textColor,
    Color? iconColor,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      decoration: BoxDecoration(
        color:
        isActive ? AppColors.primary.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: iconColor ??
              (isActive ? AppColors.primary : AppColors.textSecondary),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
            color: textColor ??
                (isActive ? AppColors.primary : AppColors.textSecondary),
          ),
        ),
        onTap: onTap,
        dense: true,
        visualDensity: const VisualDensity(horizontal: -1, vertical: -1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildFilters(BuildContext context, bool isWide) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: isDark ? 0 : 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      color: colorScheme.surface,
      shadowColor: colorScheme.shadow.withOpacity(isDark ? 0.05 : 0.15),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 28, horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.filter_alt_rounded,
                        color: colorScheme.primary, size: 26),
                    const SizedBox(width: 8),
                    Text(
                      'filters'.tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: controller.resetFilters,
                  icon: Icon(Icons.refresh, color: colorScheme.primary),
                  label: Text('reset'.tr,
                      style: TextStyle(color: colorScheme.primary)),
                  style: TextButton.styleFrom(
                    backgroundColor: colorScheme.primary.withOpacity(0.09),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 32, thickness: 1),
            Wrap(
              spacing: 20,
              runSpacing: 18,
              alignment: isRTL ? WrapAlignment.end : WrapAlignment.start,
              children: [
                SizedBox(
                  width: isWide ? 240 : 170,
                  child: Obx(() => DropdownButtonFormField<String>(
                    value: controller.selectedSector.value.isEmpty
                        ? null
                        : controller.selectedSector.value,
                    decoration: InputDecoration(
                      labelText: 'sector'.tr,
                      filled: true,
                      fillColor: colorScheme.surfaceVariant
                          .withOpacity(isDark ? 0.25 : 0.7),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14),
                        borderSide: BorderSide(
                          color: colorScheme.primary.withOpacity(0.2),
                          width: 1.2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 14, horizontal: 16),
                      labelStyle: TextStyle(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    icon: Icon(Icons.arrow_drop_down,
                        color: colorScheme.primary),
                    dropdownColor: colorScheme.surface,
                    items: controller.sectors
                        .map((sector) => DropdownMenuItem(
                      value: sector.id,
                      child: Text(
                        sector.name,
                        style: TextStyle(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ))
                        .toList(),
                    onChanged: (value) =>
                        controller.applyFilters(sector: value),
                  )),
                ),
                SizedBox(
                  width: isWide ? 240 : 170,
                  child: Obx(() => DropdownButtonFormField<String>(
                    value: controller.selectedStatus.value.isEmpty
                        ? null
                        : controller.selectedStatus.value,
                    decoration: InputDecoration(
                      labelText: 'status'.tr,
                      filled: true,
                      fillColor: colorScheme.surfaceVariant
                          .withOpacity(isDark ? 0.25 : 0.7),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(14),
                        borderSide: BorderSide(
                          color: colorScheme.primary.withOpacity(0.2),
                          width: 1.2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 14, horizontal: 16),
                      labelStyle: TextStyle(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    icon: Icon(Icons.arrow_drop_down,
                        color: colorScheme.primary),
                    dropdownColor: colorScheme.surface,
                    items: [
                      'all',
                      'active',
                      'done',
                      'inactive',
                      'callToWorkshop',
                      'pending'
                    ]
                        .map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(
                        status.tr,
                        style: TextStyle(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ))
                        .toList(),
                    onChanged: (value) =>
                        controller.applyFilters(status: value),
                  )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCards(BuildContext context, bool isWide) {
    Logger().d(controller.carStats);
    final cards = [
      _statCard(context, 'total_cars'.tr, controller.carStats['total'] ?? 0,
          Icons.directions_car, Colors.blue),
      _statCard(
          context,
          'طلبات الصيانة'.tr,
          controller.carStats['sentRequest'] ?? 0,
          Icons.build_circle,
          Colors.deepPurpleAccent),
      _statCard(context, 'عند الفني', controller.carStats['pending'] ?? 0,
          Icons.engineering, Colors.orange),
      _statCard(
          context,
          'عند الدعم الفني',
          controller.carStats['sendToLogisticsSupport'] ?? 0,
          Icons.support_agent,
          Colors.teal),
      _statCard(context, 'ارسال فريق'.tr, controller.carStats['sendGroup'] ?? 0,
          Icons.groups, Colors.indigo),
      _statCard(
          context,
          'استدعاء للورشة'.tr,
          controller.carStats['callToWorkshop'] ?? 0,
          Icons.home_repair_service,
          Colors.amber.shade800),
      _statCard(context, 'تم الأستلام'.tr, controller.carStats['receipt'] ?? 0,
          Icons.receipt_long, Colors.brown),
      _statCard(context, 'جاهزة من عند الفني'.tr,
          controller.carStats['done'] ?? 0, Icons.car_repair, Colors.blueGrey),
      _statCard(context, 'تم موافقة القطاع'.tr,
          controller.carStats['agreeDeliveryToWorkShop'] ?? 0, Icons.car_repair, Colors.blueGrey),
      _statCard(context, 'بانتظار الوصول للقطاع'.tr,
          controller.carStats['deliveryToSector'] ?? 0, Icons.car_repair, Colors.blueGrey),
      _statCard(context, 'active_cars'.tr, controller.carStats['active'] ?? 0,
          Icons.check_circle, AppColors.success),
    ];

    int cardsPerRow = isWide ? 3 : 1;
    double spacing = 16;
    double totalSpacing = spacing * (cardsPerRow );
    double cardWidth =
        (MediaQuery.of(context).size.width - 32 - totalSpacing) / cardsPerRow;

    return Wrap(
      spacing: spacing,
      runSpacing: spacing,
      children: cards
          .map((card) => SizedBox(
        width: cardWidth,
        child: card,
      ))
          .toList(),
    );
  }

  Widget _statCard(BuildContext context, String title, int value, IconData icon,
      Color color) {
    // Extract status key from title (remove .tr part)
    String statusKey = title;
    if (title.endsWith('.tr')) {
      statusKey = title.substring(0, title.length - 3);
    }
    
    // Convert display titles to actual status values in the database
    String statusValue = '';
    if (statusKey == 'total_cars') {
      statusValue = 'all';
    } else if (statusKey == 'active_cars') {
      statusValue = 'active';
    } else if (statusKey == 'طلبات الصيانة') {
      statusValue = 'sendRequest';
    } else if (statusKey == 'عند الفني') {
      statusValue = 'pending';
    } else if (statusKey == 'عند الدعم الفني') {
      statusValue = 'sendToLogisticsSupport';
    } else if (statusKey == 'ارسال فريق') {
      statusValue = 'sendGroup';
    } else if (statusKey == 'استدعاء للورشة') {
      statusValue = 'callToWorkshop';
    } else if (statusKey == 'تم الأستلام') {
      statusValue = 'receipt';
    } else if (statusKey == 'جاهزة من عند الفني') {
      statusValue = 'done';
    } else {
      statusValue = statusKey;
    }
    
    return InkWell(
      onTap: () {
        // Only show dialog if there are cars with this status
        if (value > 0) {
          _showCarsStatusDialog(context, title, statusValue);
        }
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white, // Solid card background
          borderRadius: BorderRadius.circular(20),
          border: Border(
            left: BorderSide(
              color: color,
              width: 6,
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: color.withOpacity(0.12),
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(14),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 18),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          value.toString(),
                          style: Theme.of(context).textTheme.displaySmall?.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                            fontSize: 30,
                          ),
                        ),
                        if (value > 0) const SizedBox(width: 8),
                        if (value > 0)
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 14,
                            color: color.withOpacity(0.6),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  void _showCarsStatusDialog(BuildContext context, String title, String status) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;
    
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Directionality(
            textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with title and close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const Divider(height: 24),
                  // Car list with shimmer loading effect
                  FutureBuilder<List<Car>>(
                    future: status == 'all' 
                        ? controller.getCarsByStatus('') 
                        : controller.getCarsByStatus(status),
                    builder: (context, snapshot) {
                      // Show loading state
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container(
                          height: 300,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 80,
                                  width: 80,
                                  child: Lottie.asset(
                                    'assets/animations/car_loading.json',
                                    fit: BoxFit.cover,
                                    frameRate: FrameRate.max,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Loading cars...',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      
                      // Show error state
                      if (snapshot.hasError) {
                        return Container(
                          height: 200,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.red,
                                  size: 48,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Error loading cars',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '${snapshot.error}',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      
                      final cars = snapshot.data ?? [];
                      print('Cars loaded: ${cars.length}');
                      
                      // Show empty state
                      if (cars.isEmpty) {
                        return Container(
                          height: 200,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 80,
                                  width: 80,
                                  child: Lottie.asset(
                                    'assets/animations/empty_box.json',
                                    fit: BoxFit.cover,
                                    frameRate: FrameRate.max,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No cars found with this status',
                                  style: TextStyle(
                                    color: AppColors.textSecondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      
                      // Show car list
                      return Container(
                        constraints: BoxConstraints(
                          maxHeight: MediaQuery.of(context).size.height * 0.6,
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: cars.length,
                          itemBuilder: (context, index) {
                            final car = cars[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              elevation: 2,
                              shadowColor: AppColors.primary.withOpacity(0.1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: ListTile(
                                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                leading: CircleAvatar(
                                  backgroundColor: AppColors.primary.withOpacity(0.1),
                                  child: const Icon(Icons.directions_car, color: AppColors.primary),
                                ),
                                title: Text(
                                  '${car.plateCharacters} - ${car.plateNumber}',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 4),
                                    Text('Model: ${car.carModel}'),
                                    Text('Type: ${car.carType}'),
                                    if (car.encoding.isNotEmpty)
                                      Text('Encoding: ${car.encoding}'),
                                  ],
                                ),
                                trailing: Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: IconButton(
                                    icon: const Icon(Icons.arrow_forward_ios, size: 16),
                                    color: AppColors.primary,
                                    onPressed: () {
                                      Navigator.pop(context);
                                      // Navigate to car details
                                      Get.toNamed('/cars/${car.id}');
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCharts(BuildContext context, bool isWide) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return isWide
        ? Row(
      children: [
        Expanded(
          child: _syncfusionPieChartCard(
            context,
            'car_status_distribution'.tr,
            controller.carStatusLabels,
            controller.carStatusData,
            isRTL,
            isDoughnut: false,
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: _syncfusionPieChartCard(
            context,
            'cars_by_sector'.tr,
            controller.sectorLabels,
            controller.sectorCarData,
            isRTL,
            isDoughnut: true,
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: _syncfusionBarChartCard(
            context,
            'cars_by_status_bar'.tr,
            controller.carStatusLabels,
            controller.carStatusData,
            isRTL,
          ),
        ),
      ],
    )
        : Column(
      children: [
        _syncfusionPieChartCard(
          context,
          'car_status_distribution'.tr,
          controller.carStatusLabels,
          controller.carStatusData,
          isRTL,
          isDoughnut: false,
        ),
        const SizedBox(height: 24),
        _syncfusionPieChartCard(
          context,
          'cars_by_sector'.tr,
          controller.sectorLabels,
          controller.sectorCarData,
          isRTL,
          isDoughnut: true,
        ),
        const SizedBox(height: 24),
        _syncfusionBarChartCard(
          context,
          'cars_by_status_bar'.tr,
          controller.carStatusLabels,
          controller.carStatusData,
          isRTL,
        ),
      ],
    );
  }

  Widget _syncfusionPieChartCard(
      BuildContext context,
      String title,
      List<String> labels,
      List<double> values,
      bool isRTL, {
        bool isDoughnut = false,
      }) {
    final List<Color> sectionColors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.accent,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
    ];

    List<_ChartData> data = [];
    for (int i = 0; i < labels.length; i++) {
      if (values.length > i && values[i] > 0) {
        data.add(_ChartData(
            labels[i], values[i], sectionColors[i % sectionColors.length]));
      }
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            colors: [
              AppColors.surface,
              AppColors.surface.withOpacity(0.85),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min, // Take only as much space as needed
                children: [
                  Icon(isDoughnut ? Icons.donut_large : Icons.pie_chart_rounded,
                      color: AppColors.primary, size: 28),
                  const SizedBox(width: 10),
                  Flexible( // Add this to allow text to shrink if needed
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                      overflow: TextOverflow.ellipsis, // Add this to truncate with ... if too long
                      maxLines: 1, // Limit to one line
                    ),
                  ),
                ],
              ),              const SizedBox(height: 24),
              AspectRatio(
                aspectRatio: 1.5,
                child: data.isEmpty
                    ? Center(
                  child: Text(
                    'no_data'
                        .tr, // Make sure to add 'no_data' to your translations
                    style:
                    Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
                    : isDoughnut
                    ? SfCircularChart(
                  legend: Legend(isVisible: false),
                  series: <DoughnutSeries<_ChartData, String>>[
                    DoughnutSeries<_ChartData, String>(
                      dataSource: data,
                      xValueMapper: (_ChartData d, _) => d.label,
                      yValueMapper: (_ChartData d, _) => d.value,
                      pointColorMapper: (_ChartData d, _) => d.color,
                      dataLabelMapper: (_ChartData d, _) =>
                      '${d.label}: ${d.value.toInt()}',
                      dataLabelSettings: DataLabelSettings(
                          isVisible: true,
                          labelPosition: ChartDataLabelPosition.outside,
                          connectorLineSettings: ConnectorLineSettings(type: ConnectorType.curve),
                          textStyle: TextStyle(
                              color: Colors.black87,
                              fontWeight: FontWeight.w600,
                              fontSize: 12)),
                      radius: '75%',
                      innerRadius: '60%',
                    ),
                  ],
                )
                    : SfCircularChart(
                  legend: Legend(isVisible: false),
                  series: <PieSeries<_ChartData, String>>[
                    PieSeries<_ChartData, String>(
                      dataSource: data,
                      xValueMapper: (_ChartData d, _) => d.label,
                      yValueMapper: (_ChartData d, _) => d.value,
                      pointColorMapper: (_ChartData d, _) => d.color,
                      dataLabelMapper: (_ChartData d, _) =>
                      '${d.label}: ${d.value.toInt()}',
                      // Fix for the rendering error - use inside position for data labels
                      dataLabelSettings: DataLabelSettings(
                          isVisible: true,
                          labelPosition: ChartDataLabelPosition.inside,
                          textStyle: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 12)),
                      enableTooltip: true,
                      radius: '75%',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 18),
              data.isEmpty
                  ? const SizedBox.shrink()
                  : Wrap(
                spacing: 18,
                runSpacing: 10,
                alignment:
                isRTL ? WrapAlignment.end : WrapAlignment.start,
                children: data.map((d) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 6),
                    margin: const EdgeInsets.only(bottom: 4),
                    decoration: BoxDecoration(
                      color: d.color.withOpacity(0.12),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 14,
                          height: 14,
                          decoration: BoxDecoration(
                            color: d.color,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          d.label,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '(${d.value.toInt()})',
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                            color: AppColors.textSecondary
                                .withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _syncfusionBarChartCard(
      BuildContext context,
      String title,
      List<String> labels,
      List<double> values,
      bool isRTL,
      ) {
    final List<Color> sectionColors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.accent,
      AppColors.success,
      AppColors.warning,
      AppColors.error,
      AppColors.info,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
    ];

    List<_ChartData> data = [];
    for (int i = 0; i < labels.length; i++) {
      if (values[i] > 0) {
        data.add(_ChartData(
            labels[i], values[i], sectionColors[i % sectionColors.length]));
      }
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: LinearGradient(
            colors: [
              AppColors.surface,
              AppColors.surface.withOpacity(0.85),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    Icon(Icons.bar_chart_rounded,
                        color: AppColors.primary, size: 28),
                    const SizedBox(width: 10),
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                height: 220,
                child: SfCartesianChart(
                  primaryXAxis: CategoryAxis(
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                  ),
                  primaryYAxis: NumericAxis(
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                  ),
                  series: [
                    ColumnSeries<_ChartData, String>(
                      dataSource: data,
                      xValueMapper: (_ChartData d, _) => d.label,
                      yValueMapper: (_ChartData d, _) => d.value,
                      pointColorMapper: (_ChartData d, _) => d.color,
                      dataLabelSettings:
                      const DataLabelSettings(isVisible: true),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _chartCard(BuildContext context, String title) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            // Replace this Container with your actual chart widget
            Container(
              height: 180,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text('Chart goes here'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ChartData {
  final String label;
  final double value;
  final Color color;
  _ChartData(this.label, this.value, this.color);
}
