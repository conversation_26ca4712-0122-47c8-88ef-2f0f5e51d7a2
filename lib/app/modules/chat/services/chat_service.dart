import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/chat_message.dart';
import '../models/chat_room.dart';

class ChatService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final _storage = GetStorage();

  // Initialize service
  Future<ChatService> init() async {
    final userId = _storage.read('userId');
    if (userId != null) {
      await initPushNotifications(userId);
    }
    return this;
  }

  // Collection references
  CollectionReference get _chatRooms => _firestore.collection('chatRooms');
  CollectionReference get _messages => _firestore.collection('messages');

  // Stream of chat rooms for a user
  Stream<List<ChatRoom>> getChatRooms(String userId) {
    return _chatRooms
        .where('participants', arrayContains: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ChatRoom.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    });
  }

  // Stream of messages for a specific chat room
  Stream<List<ChatMessage>> getMessages(String chatRoomId) {
    return _messages
        .where('chatRoomId', isEqualTo: chatRoomId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => ChatMessage.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    });
  }

  // Send a new message
  Future<void> sendMessage(ChatMessage message, String chatRoomId) async {
    final batch = _firestore.batch();

    // Add the message
    final messageRef = _messages.doc();
    batch.set(messageRef, {
      ...message.toJson(),
      'id': messageRef.id,
      'chatRoomId': chatRoomId,
    });

    // Update chat room's last message
    final chatRoomRef = _chatRooms.doc(chatRoomId);
    batch.set(chatRoomRef, {
      'lastMessage': message.content,
      'lastMessageTime': Timestamp.fromDate(message.timestamp),
      'unreadCount.${message.receiverId}': FieldValue.increment(1),
    });

    await batch.commit();
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatRoomId, String userId) async {
    final batch = _firestore.batch();

    // Get unread messages
    final unreadMessages = await _messages
        .where('chatRoomId', isEqualTo: chatRoomId)
        .where('receiverId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .get();

    // Mark each message as read
    for (var doc in unreadMessages.docs) {
      batch.update(doc.reference, {'isRead': true, 'status': MessageStatus.read.toString()});
    }

    // Reset unread count for the user
    batch.update(_chatRooms.doc(chatRoomId), {
      'unreadCount.$userId': 0,
    });

    await batch.commit();
  }

  // Update typing status
  Future<void> updateTypingStatus(String chatRoomId, String userId, bool isTyping) async {
    await _chatRooms.doc(chatRoomId).update({
      'typing.$userId': isTyping,
    });
  }

  // Update online status
  Future<void> updateOnlineStatus(String chatRoomId, String userId, bool isOnline) async {
    final isDriver = await _chatRooms
        .doc(chatRoomId)
        .get()
        .then((doc) => doc.get('driverId') == userId);

    await _chatRooms.doc(chatRoomId).update({
      isDriver ? 'isDriverOnline' : 'isCustomerOnline': isOnline,
    });
  }

  // Initialize push notifications
  Future<void> initPushNotifications(String userId) async {
    final settings = await _messaging.requestPermission();
    if (AuthorizationStatus.authorized == settings.announcement) {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection('users').doc(userId).update({
          'fcm_token': token,
        });
      }
    }
  }
}
