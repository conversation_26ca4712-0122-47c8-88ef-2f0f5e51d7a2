import 'package:get/get.dart';
import 'en_US.dart';
import 'ar_SA.dart';
import 'car_form_translations.dart';
import 'car_rotation_translations.dart';
import 'inventory_translations.dart';
import 'login_translations.dart';
import 'maintenance_translations.dart';
import 'modern_dashboard_translations.dart';
import 'supervisor_translations.dart';
import 'vehicle_inspection_translations.dart';
import 'vehicle_parts_translations.dart';
import 'workshop_translations.dart';
import 'notifications_translations.dart';
import 'technician_translations.dart';

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        'en_US': {
          ...enUS,
          ...CarRotationTranslations.translations['en']!,
          ...InventoryTranslations.translations['en']!,
          ...LoginTranslations.translations['en']!,
          ...MaintenanceTranslations.translations['en']!,
          ...SupervisorTranslations.translations['en']!,
          ...VehicleInspectionTranslations.translations['en']!,
          ...VehiclePartsTranslations.translations['en']!,
          ...WorkshopTranslations.translations['en']!,
          ...NotificationsTranslations.translations['en']!,
          ...TechnicianTranslations.translations['en']!,
          ...ModernDashboardTranslations().keys['en']!,
          'notifications': 'Notifications',
          'no_notifications': 'No notifications yet',
          'mark_all_read': 'Mark all as read',
          'car_status': 'Car Status',
          'new_message': 'New Message',
          'system': 'System',
          'maintenance': 'Maintenance',
          'other': 'Other',
          'error_loading_notifications': 'Error loading notifications',
          'error_marking_read': 'Error marking notification as read',
          'error_marking_all_read': 'Error marking all notifications as read',
          'error_deleting_notification': 'Error deleting notification',
          'notification_deleted': 'Notification deleted',
          'success': 'Success',
          'error': 'Error',
          'temporary_rotation': 'Temporary Rotation',
          'permanent_rotation': 'Permanent Rotation',
          'car_info': 'Car Information',
          'from_sector': 'From Sector',
          'to_sector': 'To Sector',
          'pending': 'Pending',
          'accepted': 'Accepted',
          'rejected': 'Rejected',
          'accept': 'Accept',
          'reject': 'Reject',
          'rotation_completed': 'Rotation Completed',
          'rotation_rejected': 'Rotation Rejected',
          'waiting_for_approval': 'Waiting for Approval',
          'error_creating_rotation': 'Error creating rotation',
          'error_updating_rotation': 'Error updating rotation',
          'rotation_success': 'Rotation successful',
          'rotation_request_sent': 'Rotation request sent successfully',
          'rotation_summary': 'Rotation Summary',
          'no_rotations_found': 'No rotations found',
          'error_loading_rotations': 'Error loading rotations',
          'pending_rotations': 'Pending',
          'completed_rotations': 'Completed',
          'rejected_rotations': 'Rejected',
        },
        'ar_SA': {
          ...arSA,
          ...LoginTranslations.translations['ar']!,
          ...CarRotationTranslations.translations['ar']!,
          ...InventoryTranslations.translations['ar']!,
          ...MaintenanceTranslations.translations['ar']!,
          ...SupervisorTranslations.translations['ar']!,
          ...VehicleInspectionTranslations.translations['ar']!,
          ...VehiclePartsTranslations.translations['ar']!,
          ...WorkshopTranslations.translations['ar']!,
          ...NotificationsTranslations.translations['ar']!,
          ...TechnicianTranslations.translations['ar']!,
          ...ModernDashboardTranslations().keys['ar']!,
          'notifications': 'الإشعارات',
          'no_notifications': 'لا توجد إشعارات',
          'mark_all_read': 'تحديد الكل كمقروء',
          'car_status': 'حالة السيارة',
          'new_message': 'رسالة جديدة',
          'system': 'النظام',
          'maintenance': 'الصيانة',
          'other': 'أخرى',
          'error_loading_notifications': 'خطأ في تحميل الإشعارات',
          'error_marking_read': 'خطأ في تحديد الإشعار كمقروء',
          'error_marking_all_read': 'خطأ في تحديد جميع الإشعارات كمقروءة',
          'error_deleting_notification': 'خطأ في حذف الإشعار',
          'notification_deleted': 'تم حذف الإشعار',
          'success': 'نجاح',
          'error': 'خطأ',
          'temporary_rotation': 'نقل مؤقت',
          'permanent_rotation': 'نقل دائم',
          'car_info': 'معلومات السيارة',
          'from_sector': 'من القطاع',
          'to_sector': 'إلى القطاع',
          'pending': 'قيد الانتظار',
          'accepted': 'مقبول',
          'rejected': 'مرفوض',
          'accept': 'قبول',
          'reject': 'رفض',
          'rotation_completed': 'اكتمل النقل',
          'rotation_rejected': 'تم رفض النقل',
          'waiting_for_approval': 'في انتظار الموافقة',
          'error_creating_rotation': 'خطأ في إنشاء طلب النقل',
          'error_updating_rotation': 'خطأ في تحديث حالة النقل',
          'rotation_success': 'تم النقل بنجاح',
          'rotation_request_sent': 'تم إرسال طلب النقل بنجاح',
          'rotation_summary': 'ملخص النقل',
          'no_rotations_found': 'لا توجد عمليات نقل',
          'error_loading_rotations': 'خطأ في تحميل عمليات النقل',
          'pending_rotations': 'قيد الانتظار',
          'completed_rotations': 'مكتمل',
          'rejected_rotations': 'مرفوض',
        },
      };
}
