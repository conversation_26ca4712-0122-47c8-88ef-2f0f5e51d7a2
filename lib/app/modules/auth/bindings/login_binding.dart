import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../controllers/login_controller.dart';

class LoginBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure AuthController is available globally
    Get.put(AuthController(), permanent: true);

    // Login controller is only needed for the login page
    Get.lazyPut<LoginController>(
      () => LoginController(),
      fenix: true
    );
  }
}
