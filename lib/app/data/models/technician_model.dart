class Technician {
  final String id;
  final String name;
  final String role;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  Technician({
    required this.id,
    required this.name,
    required this.role,
    required this.isActive,
    this.metadata,
  });

  factory Technician.fromJson(Map<String, dynamic> json) {
    return Technician(
      id: json['id'] as String,
      name: json['name'] as String,
      role: json['role'] as String,
      isActive: json['isActive'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'role': role,
      'isActive': isActive,
      if (metadata != null) 'metadata': metadata,
    };
  }
}
