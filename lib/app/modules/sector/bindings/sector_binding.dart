import 'package:get/get.dart';

import '../controller/sector_controller.dart';
import '../controller/sector_manager_controller.dart';
import '../../user/controller/user_controller.dart';

class SectorBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(SectorController());
    Get.lazyPut<UserController>(() => UserController(), fenix: true);
    Get.lazyPut<SectorManagerController>(
      () => SectorManagerController(),
    );
  }
}