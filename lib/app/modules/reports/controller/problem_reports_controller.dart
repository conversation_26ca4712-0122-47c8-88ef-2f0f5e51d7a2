import 'package:get/get.dart';
import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cars_app/app/data/models/problem_tag_model.dart';
import 'package:cars_app/app/services/car_service.dart';
import 'package:cars_app/app/services/problem_tag_service.dart';
import 'package:cars_app/app/routes/app_pages.dart';

class ProblemReportsController extends GetxController {
  final CarService _carService = Get.find<CarService>();
  final ProblemTagService _tagService = Get.find<ProblemTagService>();

  final RxBool isLoading = true.obs;
  final RxBool isSearching = false.obs;

  final RxList<Car> allCars = <Car>[].obs;
  final RxList<Car> filteredCars = <Car>[].obs;
  final RxList<ProblemTag> allTags = <ProblemTag>[].obs;
  final RxList<String> categories = <String>[].obs;

  final RxString selectedCategory = ''.obs;
  final Rxn<ProblemTag> selectedTag = Rxn<ProblemTag>();

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  Future<void> loadData() async {
    isLoading.value = true;

    try {
      // Load tags if not already loaded
      if (_tagService.tags.isEmpty) {
        await _tagService.loadTags();
      }

      // Load all cars
      final cars = await _carService.getCars();

      // Update local data
      allTags.value = List.from(_tagService.tags);
      allCars.value = cars;

      // Extract categories
      final Set<String> categorySet = {};
      for (final tag in allTags) {
        if (tag.category.isNotEmpty) {
          categorySet.add(tag.category);
        }
      }
      categories.value = categorySet.toList()..sort();

      // Clear filters
      selectedCategory.value = '';
      selectedTag.value = null;
      filteredCars.clear();
    } catch (e) {
      print('Error loading data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void selectCategory(String category) {
    selectedCategory.value = category;
    // Clear selected tag when category changes
    selectedTag.value = null;
    filteredCars.clear();
  }

  void selectTag(ProblemTag? tag) {
    selectedTag.value = tag;
    if (tag != null) {
      searchCarsByProblemTag(tag.id);
    } else {
      filteredCars.clear();
    }
  }

  List<ProblemTag> getTagsForCategory(String category) {
    return allTags.where((tag) => tag.category == category).toList();
  }

  Future<void> searchCarsByProblemTag(String tagId) async {
    if (tagId.isEmpty) {
      filteredCars.clear();
      return;
    }

    isSearching.value = true;

    try {
      // Filter cars that have the selected problem tag
      final List<Car> matchingCars = [];

      for (final car in allCars) {
        // Check each workshop entry for the problem tag
        for (final entry in car.workshopHistory) {
          if (entry.problemTagIds != null &&
              entry.problemTagIds!.contains(tagId)) {
            matchingCars.add(car);
            break; // Found a match, no need to check other entries
          }
        }
      }

      filteredCars.value = matchingCars;
    } catch (e) {
      print('Error searching cars: $e');
    } finally {
      isSearching.value = false;
    }
  }

  void viewCarDetails(Car car) {
    // Navigate to the car view with the car ID
    Get.toNamed(Routes.CARS, arguments: {'carId': car.id});
  }

  void refreshData() {
    loadData();
  }
}
