import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:universal_html/html.dart' as html;
import '../../data/models/car_model.dart';
import '../../modules/reports/controllers/reports_controller.dart';
import 'csv_export_service.dart';
import 'excel_export_service.dart';
import 'pdf_export_service.dart';

/// Service for exporting files in different formats
class FileExportService extends GetxService {
  final PdfExportService _pdfExportService = PdfExportService();
  final ExcelExportService _excelExportService = ExcelExportService();
  final CsvExportService _csvExportService = CsvExportService();

  /// Download a file in web
  void _downloadWeb(Uint8List bytes, String fileName) {
    // Create a Blob from the bytes
    final blob = html.Blob([bytes]);

    // Create a URL for the Blob
    final url = html.Url.createObjectUrlFromBlob(blob);

    // Create an anchor element
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..style.display = 'none';

    // Add the anchor to the document body
    html.document.body?.children.add(anchor);

    // Trigger the download
    anchor.click();

    // Clean up
    html.document.body?.children.remove(anchor);
    html.Url.revokeObjectUrl(url);
  }

  /// Save a file on mobile/desktop
  Future<void> _saveFile(Uint8List bytes, String fileName) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName');
      await file.writeAsBytes(bytes);
      await OpenFile.open(file.path);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to save file: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Export data to a file
  Future<void> exportFile(Uint8List bytes, String fileName) async {
    try {
      if (kIsWeb) {
        _downloadWeb(bytes, fileName);
      } else {
        await _saveFile(bytes, fileName);
      }

      Get.snackbar(
        'Success'.tr,
        'File exported successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to export file: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a workshop report PDF
  Future<void> exportWorkshopReportToPdf({
    required List<WorkshopReportEntry> entries,
    required DateTime startDate,
    required DateTime endDate,
    required List<StatusCount> statusDistribution,
    required List<SectorCount> sectorDistribution,
    required String fileName,
  }) async {
    try {
      final pdfBytes = await _pdfExportService.generateWorkshopReport(
        entries: entries,
        startDate: startDate,
        endDate: endDate,
        statusDistribution: statusDistribution,
        sectorDistribution: sectorDistribution,
      );

      await exportFile(pdfBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate PDF report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a workshop report Excel
  Future<void> exportWorkshopReportToExcel({
    required List<WorkshopReportEntry> entries,
    required DateTime startDate,
    required DateTime endDate,
    required List<StatusCount> statusDistribution,
    required List<SectorCount> sectorDistribution,
    required String fileName,
  }) async {
    try {
      final excelBytes = await _excelExportService.generateWorkshopReport(
        entries: entries,
        startDate: startDate,
        endDate: endDate,
        statusDistribution: statusDistribution,
        sectorDistribution: sectorDistribution,
      );

      await exportFile(excelBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a workshop report CSV
  Future<void> exportWorkshopReportToCsv({
    required List<WorkshopReportEntry> entries,
    required String fileName,
  }) async {
    try {
      final csvBytes = _csvExportService.generateWorkshopReport(entries);

      await exportFile(csvBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate CSV report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a car report PDF
  Future<void> exportCarReportToPdf({
    required Car car,
    required List<WorkshopReportEntry> entries,
    required List<StatusCount> statusDistribution,
    required String fileName,
  }) async {
    try {
      final pdfBytes = await _pdfExportService.generateCarReport(
        car: car,
        entries: entries,
        statusDistribution: statusDistribution,
      );

      await exportFile(pdfBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate PDF report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a car report Excel
  Future<void> exportCarReportToExcel({
    required Car car,
    required List<WorkshopReportEntry> entries,
    required List<StatusCount> statusDistribution,
    required String fileName,
  }) async {
    try {
      final excelBytes = await _excelExportService.generateCarReport(
        car: car,
        entries: entries,
        statusDistribution: statusDistribution,
      );

      await exportFile(excelBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a car report CSV
  Future<void> exportCarReportToCsv({
    required List<WorkshopReportEntry> entries,
    required String fileName,
  }) async {
    try {
      final csvBytes = _csvExportService.generateCarReport(entries);

      await exportFile(csvBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate CSV report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a status summary report Excel
  Future<void> exportStatusSummaryReportToExcel({
    required Map<CarStatus, List<Car>> carsByStatus,
    required DateTime startDate,
    required DateTime endDate,
    String? sectorName,
    required String fileName,
  }) async {
    try {
      final excelBytes = await _excelExportService.generateStatusSummaryReport(
        carsByStatus: carsByStatus,
        startDate: startDate,
        endDate: endDate,
        sectorName: sectorName,
      );

      await exportFile(excelBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export a workshop entries report Excel
  Future<void> exportWorkshopEntriesReportToExcel({
    required List<Map<String, dynamic>> entries,
    required DateTime startDate,
    required DateTime endDate,
    String? sectorName,
    required String fileName,
  }) async {
    try {
      final excelBytes = await _excelExportService.generateWorkshopEntriesReport(
        entries: entries,
        startDate: startDate,
        endDate: endDate,
        sectorName: sectorName,
      );

      await exportFile(excelBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// Generate and export an advanced workshop report Excel
  Future<void> exportAdvancedWorkshopReportToExcel({
    required List<Car> cars,
    required DateTime startDate,
    required DateTime endDate,
    required String fileName,
  }) async {
    try {
      final excelBytes = await _excelExportService.generateAdvancedWorkshopReport(
        cars: cars,
        startDate: startDate,
        endDate: endDate,
      );
      await exportFile(excelBytes, fileName);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
