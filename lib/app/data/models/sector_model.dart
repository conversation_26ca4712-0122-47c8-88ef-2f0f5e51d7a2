import 'package:cloud_firestore/cloud_firestore.dart';

class ManagerTransaction {
  final String id;
  final String previousManagerId;
  final String newManagerId;
  final DateTime date;
  final String? reason;
  final String? notes;

  ManagerTransaction({
    required this.id,
    required this.previousManagerId,
    required this.newManagerId,
    required this.date,
    this.reason,
    this.notes,
  });

  factory ManagerTransaction.fromJson(Map<String, dynamic> json) {
    return ManagerTransaction(
      id: json['id'] ?? '',
      previousManagerId: json['previousManagerId'] ?? '',
      newManagerId: json['newManagerId'] ?? '',
      date: (json['date'] as Timestamp).toDate(),
      reason: json['reason'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'previousManagerId': previousManagerId,
      'newManagerId': newManagerId,
      'date': Timestamp.fromDate(date),
      'reason': reason,
      'notes': notes,
    };
  }
}

class Sector {
  final String id;
  final String name;
  final String managerId;
  final DateTime createAt;
  final List<ManagerTransaction> managerHistory;

  Sector({
    required this.id,
    required this.name,
    required this.managerId,
    required this.createAt,
    required this.managerHistory,
  });

  factory Sector.fromJson(Map<String, dynamic> json) {
    return Sector(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      managerId: json['managerId'] ?? '',
      createAt: (json['createAt'] as Timestamp).toDate(),
      managerHistory: (json['managerHistory'] as List<dynamic>? ?? [])
          .map((transaction) => ManagerTransaction.fromJson(transaction))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'managerId': managerId,
      'createAt': Timestamp.fromDate(createAt),
      'managerHistory': managerHistory.map((t) => t.toJson()).toList(),
    };
  }

  Sector copyWith({
    String? id,
    String? name,
    String? managerId,
    DateTime? createAt,
    List<ManagerTransaction>? managerHistory,
  }) {
    return Sector(
      id: id ?? this.id,
      name: name ?? this.name,
      managerId: managerId ?? this.managerId,
      createAt: createAt ?? this.createAt,
      managerHistory: managerHistory ?? this.managerHistory,
    );
  }
}