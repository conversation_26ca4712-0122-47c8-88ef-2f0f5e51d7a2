import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../../data/models/user_model.dart';
import '../../../themes/app_colors.dart';
import '../controller/user_controller.dart';

class UserForm extends GetView<UserController> {
  final UserModel? user;
  
  UserForm({this.user});

  @override
  Widget build(BuildContext context) {
    final _formKey = GlobalKey<FormBuilderState>();
    final TextEditingController usernameController = TextEditingController(text: user?.username);
    final TextEditingController nameController = TextEditingController(text: user?.name);
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController emailController = TextEditingController(text: user?.email);
    final TextEditingController phoneController = TextEditingController(text: user?.phoneNumber);
    
    UserRole selectedRole = user?.role ?? UserRole.user;
    UserStatus selectedStatus = user?.status ?? UserStatus.active; // Default to active for new users

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          user == null ? 'إضافة مستخدم جديد' : 'تعديل مستخدم',
          style: const TextStyle(
            fontFamily: 'Cairo',
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.primary.withOpacity(0.05), AppColors.background],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: FormBuilder(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // User icon header
                Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    margin: const EdgeInsets.only(bottom: 24),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.person,
                      size: 60,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                
                // Form fields in a card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات المستخدم',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const Divider(),
                        const SizedBox(height: 16),
                        
                        // Username field
                        FormBuilderTextField(
                          name: 'username',
                          controller: usernameController,
                          enabled: user == null, // Disable username editing in edit mode
                          decoration: InputDecoration(
                            labelText: 'اسم المستخدم',
                            hintText: 'أدخل اسم المستخدم',
                            prefixIcon: Icon(Icons.account_circle, color: AppColors.primary),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.primary),
                            ),
                            filled: true,
                            fillColor: user != null ? Colors.grey.shade100 : Colors.white,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                            helperText: user != null ? 'لا يمكن تعديل اسم المستخدم' : null,
                            helperStyle: TextStyle(fontSize: 12, color: AppColors.textSecondary),
                          ),
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'يرجى إدخال اسم المستخدم'),
                            FormBuilderValidators.minLength(3, errorText: 'يجب أن يكون اسم المستخدم 3 أحرف على الأقل'),
                          ]),
                          style: const TextStyle(fontFamily: 'Cairo'),
                        ),
                        const SizedBox(height: 16),
                        
                        // Name field
                        FormBuilderTextField(
                          name: 'name',
                          controller: nameController,
                          decoration: InputDecoration(
                            labelText: 'الاسم الكامل',
                            hintText: 'أدخل الاسم الكامل',
                            prefixIcon: Icon(Icons.person, color: AppColors.primary),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.primary),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          ),
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'يرجى إدخال الاسم الكامل'),
                            FormBuilderValidators.minLength(3, errorText: 'يجب أن يكون الاسم 3 أحرف على الأقل'),
                          ]),
                          style: const TextStyle(fontFamily: 'Cairo'),
                        ),
                        const SizedBox(height: 16),
                        
                        // Password field for new users
                        if (user == null)
                          FormBuilderTextField(
                            name: 'password',
                            controller: passwordController,
                            decoration: InputDecoration(
                              labelText: 'كلمة المرور',
                              hintText: 'أدخل كلمة المرور',
                              prefixIcon: Icon(Icons.lock, color: AppColors.primary),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppColors.primary),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                            ),
                            obscureText: true,
                            validator: FormBuilderValidators.compose([
                              FormBuilderValidators.required(errorText: 'يرجى إدخال كلمة المرور'),
                              FormBuilderValidators.minLength(6, errorText: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل'),
                            ]),
                            style: const TextStyle(fontFamily: 'Cairo'),
                          ),
                        if (user == null) const SizedBox(height: 16),
                        
                        // Email field
                        Visibility(
                          visible: false,
                          child: FormBuilderTextField(
                            name: 'email',
                            controller: emailController,
                            decoration: InputDecoration(
                              labelText: 'البريد الإلكتروني',
                              hintText: 'أدخل البريد الإلكتروني',
                              prefixIcon: Icon(Icons.email, color: AppColors.primary),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppColors.primary),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              helperText: 'إذا لم يتم توفير البريد الإلكتروني، سيتم استخدام <EMAIL>',
                              helperStyle: TextStyle(fontSize: 12, color: AppColors.textSecondary),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            // validator: FormBuilderValidators.compose([
                            //   FormBuilderValidators.email(errorText: 'يرجى إدخال بريد إلكتروني صحيح'),
                            // ]),
                            style: const TextStyle(fontFamily: 'Cairo'),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Phone field
                        FormBuilderTextField(
                          name: 'phone',
                          controller: phoneController,
                          decoration: InputDecoration(
                            labelText: 'رقم الهاتف',
                            hintText: 'أدخل رقم الهاتف',
                            prefixIcon: Icon(Icons.phone, color: AppColors.primary),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.primary),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          ),
                          keyboardType: TextInputType.phone,
                          style: const TextStyle(fontFamily: 'Cairo'),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Role and Status Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الصلاحيات والحالة',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const Divider(),
                        const SizedBox(height: 16),
                        
                        // Role dropdown
                        FormBuilderDropdown<UserRole>(
                          name: 'role',
                          initialValue: selectedRole,
                          decoration: InputDecoration(
                            labelText: 'الدور',
                            prefixIcon: Icon(Icons.badge, color: AppColors.primary),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: AppColors.primary),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          ),
                          items: UserRole.values.map((role) => DropdownMenuItem<UserRole>(
                            value: role,
                            child: Text(_getRoleText(role), style: const TextStyle(fontFamily: 'Cairo')),
                          )).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              selectedRole = value;
                            }
                          },
                          validator: FormBuilderValidators.required(errorText: 'يرجى اختيار الدور'),
                        ),
                        const SizedBox(height: 16),
                        
                        // Status dropdown for existing users
                        if (user != null)
                          FormBuilderDropdown<UserStatus>(
                            name: 'status',
                            initialValue: selectedStatus,
                            decoration: InputDecoration(
                              labelText: 'الحالة',
                              prefixIcon: Icon(Icons.verified_user, color: AppColors.primary),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppColors.primary),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                            ),
                            items: UserStatus.values.map((status) => DropdownMenuItem<UserStatus>(
                              value: status,
                              child: Text(_getStatusText(status), style: const TextStyle(fontFamily: 'Cairo')),
                            )).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                selectedStatus = value;
                              }
                            },
                          ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Submit Button
                Container(
                  height: 55,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: AppColors.primaryGradient,
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.saveAndValidate()) {
                        if (user == null) {
                          // Create new user
                          controller.createUser(
                            usernameController.text,
                            nameController.text,
                            passwordController.text,
                            selectedRole,
                            email: emailController.text.isNotEmpty ? emailController.text : null,
                            phoneNumber: phoneController.text.isNotEmpty ? phoneController.text : null,
                          );
                        } else {
                          // Update existing user
                          controller.updateUser(
                            user!.id,
                            username: usernameController.text,
                            name: nameController.text,
                            role: selectedRole,
                            status: selectedStatus,
                            email: emailController.text.isNotEmpty ? emailController.text : null,
                            phoneNumber: phoneController.text.isNotEmpty ? phoneController.text : null,
                          );
                        }
                        Get.back();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      user == null ? 'إنشاء مستخدم' : 'تحديث مستخدم',
                      style: const TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to get Arabic text for user roles
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير النظام';
      case UserRole.manager:
        return 'مدير قطاع';
      case UserRole.supervisor:
        return 'مدير ورشة';
      case UserRole.technician:
        return 'فني';
      case UserRole.logisticsSupport:
        return 'دعم لوجستي';
      case UserRole.user:
        return 'مستخدم';
      default:
        return 'غير معروف';
    }
  }

  // Helper method to get Arabic text for user status
  String _getStatusText(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'معلق';
      case UserStatus.pending:
        return 'قيد الانتظار';
      default:
        return 'غير معروف';
    }
  }
}
