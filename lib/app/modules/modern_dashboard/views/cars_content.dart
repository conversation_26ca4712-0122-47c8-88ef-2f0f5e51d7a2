// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../data/models/car_model.dart';
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/section_header.dart';
// import '../widgets/car_card.dart';
//
// class CarsContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const CarsContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Cars section header
//         SectionHeader(
//           title: 'cars'.tr,
//           subtitle: 'cars_subtitle'.tr,
//           icon: Icons.directions_car_outlined,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 16),
//
//         // Filters
//         Obx(() => FilterBar<CarStatus>(
//           title: 'filter_by_status'.tr,
//           options: controller.getCarStatusOptions(),
//           selectedValue: controller.carStatusFilter.value,
//           onChanged: controller.setCarStatusFilter,
//           isRTL: isRTL,
//         )),
//         const SizedBox(height: 16),
//
//         // Sector filter if there are sectors
//         Obx(() {
//           if (controller.sectors.isEmpty) {
//             return const SizedBox.shrink();
//           }
//           return Column(
//             children: [
//               FilterBar<String>(
//                 title: 'filter_by_sector'.tr,
//                 options: controller.getSectorOptions(),
//                 selectedValue: controller.carSectorFilter.value,
//                 onChanged: controller.setCarSectorFilter,
//                 isRTL: isRTL,
//               ),
//               const SizedBox(height: 16),
//             ],
//           );
//         }),
//
//         // Cars list with loading state
//         Obx(() {
//           if (controller.isCarsLoading.value) {
//             return _buildLoadingList();
//           }
//
//           if (controller.filteredCars.isEmpty) {
//             return _buildEmptyState();
//           }
//
//           return ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: controller.filteredCars.length,
//             itemBuilder: (context, index) {
//               final car = controller.filteredCars[index];
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: CarCard(
//                   car: car,
//                   isRTL: isRTL,
//                   onTap: () => Get.toNamed('/car/${car.id}'),
//                   onEdit: () => Get.toNamed('/car/${car.id}/edit'),
//                   onDelete: () => _showDeleteConfirmation(context, car),
//                   onMaintenance: () => Get.toNamed('/car/${car.id}/maintenance'),
//                   onActivate: car.status != CarStatus.active
//                       ? () => _activateCar(car)
//                       : null,
//                 ),
//               );
//             },
//           );
//         }),
//       ],
//     );
//   }
//
//   // Show delete confirmation dialog
//   void _showDeleteConfirmation(BuildContext context, Car car) {
//     Get.defaultDialog(
//       title: 'confirm_delete'.tr,
//       titleStyle: GoogleFonts.cairo(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//       middleText: 'confirm_delete_car'.tr,
//       middleTextStyle: GoogleFonts.cairo(
//         fontSize: 14,
//         color: Colors.black54,
//       ),
//       textConfirm: 'delete'.tr,
//       textCancel: 'cancel'.tr,
//       confirmTextColor: Colors.white,
//       cancelTextColor: AppColors.primary,
//       buttonColor: AppColors.error,
//       onConfirm: () {
//         // Call delete car service
//         Get.back();
//         Get.snackbar(
//           'success'.tr,
//           'car_deleted'.tr,
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: AppColors.success.withOpacity(0.1),
//           colorText: AppColors.success,
//         );
//       },
//     );
//   }
//
//   // Activate car
//   void _activateCar(Car car) {
//     // Call car service to update status
//     Get.snackbar(
//       'success'.tr,
//       'car_activated'.tr,
//       snackPosition: SnackPosition.BOTTOM,
//       backgroundColor: AppColors.success.withOpacity(0.1),
//       colorText: AppColors.success,
//     );
//   }
//
//   // Build loading state
//   Widget _buildLoadingList() {
//     return ListView.builder(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 5,
//       itemBuilder: (context, index) {
//         return Padding(
//           padding: const EdgeInsets.only(bottom: 8),
//           child: Container(
//             height: 150,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.05),
//                   blurRadius: 10,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Shimmer.fromColors(
//               baseColor: Colors.grey[300]!,
//               highlightColor: Colors.grey[100]!,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     children: [
//                       Expanded(
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Container(
//                               width: 120,
//                               height: 20,
//                               color: Colors.white,
//                             ),
//                             const SizedBox(height: 8),
//                             Container(
//                               width: 80,
//                               height: 16,
//                               color: Colors.white,
//                             ),
//                           ],
//                         ),
//                       ),
//                       Container(
//                         width: 60,
//                         height: 30,
//                         color: Colors.white,
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   Container(
//                     width: double.infinity,
//                     height: 1,
//                     color: Colors.white,
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build empty state
//   Widget _buildEmptyState() {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.directions_car_outlined,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'no_cars_found'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'no_cars_found_description'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 14,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
