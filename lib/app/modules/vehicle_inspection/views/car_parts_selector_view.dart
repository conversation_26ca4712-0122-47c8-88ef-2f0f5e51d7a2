import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/car_parts_selector_controller.dart';
import '../models/car_part.dart';
import '../widgets/car_selector_parts_widget.dart';

class CarPartsSelectorView extends GetView<CarPartsSelectorController> {
  const CarPartsSelectorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('vehicle_inspection'.tr),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'save'.tr,
            onPressed: _saveInspection,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'select_damaged_parts'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CarSelectorPartsWidget(
                controller: controller,
                onPartSelected: _onPartSelected,
              ),
            ),
          ),
          _buildSelectedPartsCounter(),
        ],
      ),
    );
  }
  
  Widget _buildSelectedPartsCounter() {
    return Obx(() {
      final count = controller.selectedPartIds.length;
      if (count == 0) return const SizedBox.shrink();
      
      return Container(
        padding: const EdgeInsets.all(16),
        color: Colors.grey.shade100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'selected_parts'.tr + ': $count',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            ElevatedButton(
              onPressed: _saveInspection,
              child: Text('save_inspection'.tr),
            ),
          ],
        ),
      );
    });
  }
  
  void _onPartSelected(CarPart part) {
    // You can add custom logic here when a part is selected
    print('Selected part: ${part.id} - ${part.name}');
  }
  
  void _saveInspection() {
    // Export the data
    final data = controller.exportData();
    
    // Show a success message
    Get.snackbar(
      'success'.tr,
      'inspection_saved'.tr,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
    
    // Return the data to the previous screen
    Get.back(result: data);
  }
}
