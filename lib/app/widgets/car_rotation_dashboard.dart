import 'package:cars_app/app/widgets/shared_app_bar.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../data/models/car_model.dart';
import '../data/models/car_rotate.dart';
import '../routes/app_pages.dart';
import '../services/car_rotation_service.dart';
import '../themes/app_colors.dart';
import 'car_rotation_card.dart';

class CarRotationDashboard extends StatelessWidget {
   String? sectorId = '';
  final CarRotationService rotationService = Get.find();
  RotateAction? action;
   CarRotationDashboard({
    Key? key,
     this.sectorId,
     this.action
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if(Get.arguments != null && Get.arguments.toString().isNotEmpty){
      Logger().i(Get.arguments);
      sectorId = Get.arguments['sectorId'] ?? '';
    }
    return SafeArea(
      child: Scaffold(
        appBar: SharedAppBar(title: 'rotations'.tr,showBackButton: true,),
        floatingActionButton:Visibility(
          visible: sectorId==null,
          child: FloatingActionButton(
            child: Icon(Icons.add, color: Colors.white,),
            onPressed: () {
            Get.toNamed(Routes.CAR_ROTATION);
          },),
        ),
        body: StreamBuilder<List<CarRotate>>(
          stream: rotationService.getCarRotations(sectorId??'',action),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (snapshot.hasError) {
              print('Error loading rotations: ${snapshot.error}');
              return Center(
                child: Text(
                  'Error loading rotations ${snapshot.error}'.tr,
                  style: const TextStyle(color: Colors.red),
                ),
              );
            }

            final rotations = snapshot.data ?? [];
            final pendingRotations = rotations.where((r) =>
              r.isFromSectorMangerAgree == RotateStatus.pending ||
              r.isToSectorMangerAgree == RotateStatus.pending
            ).toList();
            final completedRotations = rotations.where((r) =>
              r.isFromSectorMangerAgree == RotateStatus.accepted &&
              r.isToSectorMangerAgree == RotateStatus.accepted
            ).toList();
            final rejectedRotations = rotations.where((r) =>
              r.isFromSectorMangerAgree == RotateStatus.rejected ||
              r.isToSectorMangerAgree == RotateStatus.rejected
            ).toList();

            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDashboardSummary(
                    pendingCount: pendingRotations.length,
                    completedCount: completedRotations.length,
                    rejectedCount: rejectedRotations.length,
                  ),
                ),
                Expanded(
                  child: rotations.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.swap_horiz,
                              size: 64,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد طلبات تدوير'.tr,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.only(bottom: 16),
                        itemCount: rotations.length,
                        itemBuilder: (context, index) {
                          final rotation = rotations[index];
                          // Determine if current sector is from or to sector
                          final isFromSectorManager = rotation.fromSectorId == sectorId;
                          final isToSectorManager = rotation.toSectorId == sectorId;
                          final String carId = rotation.carId;
                          return carId == '' ? CarRotationCard(
                            car: null,
                            rotation: rotation,
                            isFromSectorManager: isFromSectorManager,
                            isToSectorManager: isToSectorManager,
                            onStatusUpdate: (rotation, status) {
                              rotationService.updateRotationStatus(
                                rotationId: rotation.id,
                                isFromSector: isFromSectorManager,
                                status: status,
                              );
                            },
                          ) : StreamBuilder<Car>(
                            stream: Get.find<FirebaseFirestore>().collection('cars').doc(carId??'').snapshots().map((snapshot) => Car.fromJson(snapshot.data()!)),
                            builder: (context, snapshot) {
                              if(snapshot.connectionState == ConnectionState.waiting){
                                return const Center(child: CircularProgressIndicator());
                              }
                              Car? car = snapshot.data;
                              return
                                CarRotationCard(
                                car: car,
                                rotation: rotation,
                                isFromSectorManager: isFromSectorManager,
                                isToSectorManager: isToSectorManager,
                                onStatusUpdate: (rotation, status) {
                                  rotationService.updateRotationStatus(
                                    rotationId: rotation.id,
                                    isFromSector: isFromSectorManager,
                                    status: status,
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDashboardSummary({
    required int pendingCount,
    required int completedCount,
    required int rejectedCount,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withOpacity(0.05),
            AppColors.primary.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الطلبات'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  icon: Icons.pending_actions,
                  color: AppColors.warning,
                  title: 'انتظار الموفقة'.tr,
                  count: pendingCount,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                  icon: Icons.check_circle,
                  color: AppColors.success,
                  title: 'تم الانتهاء'.tr,
                  count: completedCount,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildSummaryCard(
                  icon: Icons.cancel,
                  color: Colors.red,
                  title: 'مرفوضة'.tr,
                  count: rejectedCount,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required IconData icon,
    required Color color,
    required String title,
    required int count,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
} 