import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import '../controllers/car_parts_selector_controller.dart';
import '../models/car_part.dart';

class InteractiveSvgSelector extends StatefulWidget {
  final CarPartsSelectorController controller;
  final Function(CarPart)? onPartSelected;
  final String svgAssetPath;

  const InteractiveSvgSelector({
    Key? key,
    required this.controller,
    this.onPartSelected,
    this.svgAssetPath = 'assets/mbulance.svg',
  }) : super(key: key);

  @override
  State<InteractiveSvgSelector> createState() => _InteractiveSvgSelectorState();
}

class _InteractiveSvgSelectorState extends State<InteractiveSvgSelector> {
  // Map of SVG element IDs to their corresponding CarPart objects
  final Map<String, CarPart> _svgIdToPartMap = {};

  // Map to track selected parts
  final RxMap<String, bool> _selectedParts = <String, bool>{}.obs;

  // Current view group ID
  final Rx<String> _currentViewGroupId = 'front_view'.obs;

  @override
  void initState() {
    super.initState();
    _initializeSvgPartMapping();

    // Listen to controller changes
    ever(widget.controller.selectedPartIds, (selectedIds) {
      _updateSelectedPartsFromController(selectedIds);
    });

    // Listen to view changes
    ever(widget.controller.currentView, (view) {
      _updateCurrentViewFromController(view);
    });
  }

  void _initializeSvgPartMapping() {
    // Front view parts
    _addPartMapping('front_windshield', 'Windshield', CarView.front);
    _addPartMapping('front_headlight_left', 'Left Headlight', CarView.front);
    _addPartMapping('front_headlight_right', 'Right Headlight', CarView.front);
    _addPartMapping('front_indicator_left', 'Left Indicator', CarView.front);
    _addPartMapping('front_indicator_right', 'Right Indicator', CarView.front);
    _addPartMapping('front_grille_upper', 'Upper Grille', CarView.front);
    _addPartMapping('front_grille_lower', 'Lower Grille', CarView.front);
    _addPartMapping('front_bumper', 'Front Bumper', CarView.front);
    _addPartMapping('front_mirror_left_body', 'Left Mirror', CarView.front);
    _addPartMapping('front_mirror_right_body', 'Right Mirror', CarView.front);
    _addPartMapping(
        'front_tire_left_visible', 'Left Front Tire', CarView.front);
    _addPartMapping(
        'front_tire_right_visible', 'Right Front Tire', CarView.front);

    // Back view parts
    _addPartMapping('back_window_left', 'Left Rear Window', CarView.back);
    _addPartMapping('back_window_right', 'Right Rear Window', CarView.back);
    _addPartMapping('back_taillight_left_red', 'Left Taillight', CarView.back);
    _addPartMapping(
        'back_taillight_right_red', 'Right Taillight', CarView.back);
    _addPartMapping(
        'back_taillight_left_yellow', 'Left Indicator', CarView.back);
    _addPartMapping(
        'back_taillight_right_yellow', 'Right Indicator', CarView.back);
    _addPartMapping('back_bumper', 'Rear Bumper', CarView.back);
    _addPartMapping('back_door_handle_left', 'Left Door Handle', CarView.back);
    _addPartMapping(
        'back_door_handle_right', 'Right Door Handle', CarView.back);
    _addPartMapping('back_tire_left_visible', 'Left Rear Tire', CarView.back);
    _addPartMapping('back_tire_right_visible', 'Right Rear Tire', CarView.back);

    // Left side view parts
    _addPartMapping('side_left_front_door_window', 'Left Front Door Window',
        CarView.leftSide);
    _addPartMapping(
        'side_left_front_door_panel', 'Left Front Door', CarView.leftSide);
    _addPartMapping(
        'side_left_sliding_door_panel', 'Left Sliding Door', CarView.leftSide);
    _addPartMapping('side_left_rear_panel_window_area', 'Left Rear Window',
        CarView.leftSide);
    _addPartMapping('side_left_front_door_handle', 'Left Front Door Handle',
        CarView.leftSide);
    _addPartMapping('side_left_sliding_door_handle', 'Left Sliding Door Handle',
        CarView.leftSide);
    _addPartMapping('side_left_mirror_body', 'Left Mirror', CarView.leftSide);
    _addPartMapping(
        'side_left_front_wheel_tire', 'Left Front Wheel', CarView.leftSide);
    _addPartMapping(
        'side_left_rear_wheel_tire', 'Left Rear Wheel', CarView.leftSide);
    _addPartMapping(
        'side_left_headlight_visible', 'Left Headlight', CarView.leftSide);
    _addPartMapping(
        'side_left_taillight_visible', 'Left Taillight', CarView.leftSide);

    // Right side view parts
    _addPartMapping('side_right_front_door_window', 'Right Front Door Window',
        CarView.rightSide);
    _addPartMapping(
        'side_right_front_door_panel', 'Right Front Door', CarView.rightSide);
    _addPartMapping('side_right_sliding_door_panel', 'Right Sliding Door',
        CarView.rightSide);
    _addPartMapping('side_right_rear_panel_window_area', 'Right Rear Window',
        CarView.rightSide);
    _addPartMapping('side_right_front_door_handle', 'Right Front Door Handle',
        CarView.rightSide);
    _addPartMapping('side_right_sliding_door_handle',
        'Right Sliding Door Handle', CarView.rightSide);
    _addPartMapping(
        'side_right_mirror_body', 'Right Mirror', CarView.rightSide);
    _addPartMapping(
        'side_right_front_wheel_tire', 'Right Front Wheel', CarView.rightSide);
    _addPartMapping(
        'side_right_rear_wheel_tire', 'Right Rear Wheel', CarView.rightSide);
    _addPartMapping(
        'side_right_headlight_visible', 'Right Headlight', CarView.rightSide);
    _addPartMapping(
        'side_right_taillight_visible', 'Right Taillight', CarView.rightSide);

    // Top view parts
    _addPartMapping('top_roof_panel', 'Roof', CarView.top);
    _addPartMapping('top_windshield_area', 'Windshield', CarView.top);
    _addPartMapping('top_mirror_left_surface', 'Left Mirror', CarView.top);
    _addPartMapping('top_mirror_right_surface', 'Right Mirror', CarView.top);
  }

  void _addPartMapping(String svgId, String name, CarView view) {
    final part = CarPart(
      id: svgId,
      name: name,
      view: view,
      path: Path(), // Not used with SVG
      isSelected: false,
    );

    _svgIdToPartMap[svgId] = part;

    // Add to controller's all parts list if not already there
    if (!widget.controller.allParts.any((p) => p.id == svgId)) {
      widget.controller.allParts.add(part);
    }
  }

  void _updateSelectedPartsFromController(List<String> selectedIds) {
    _selectedParts.clear();
    for (final id in selectedIds) {
      _selectedParts[id] = true;
    }
  }

  void _updateCurrentViewFromController(CarView view) {
    switch (view) {
      case CarView.front:
        _currentViewGroupId.value = 'front_view';
        break;
      case CarView.back:
        _currentViewGroupId.value = 'back_view';
        break;
      case CarView.leftSide:
        _currentViewGroupId.value = 'side_left_view';
        break;
      case CarView.rightSide:
        _currentViewGroupId.value = 'side_right_view';
        break;
      case CarView.top:
        _currentViewGroupId.value = 'top_view';
        break;
    }
  }

  void _onSvgElementTap(String id) {
    if (_svgIdToPartMap.containsKey(id)) {
      final part = _svgIdToPartMap[id]!;

      // Toggle selection in controller
      widget.controller.togglePartSelection(part.id);

      // Call callback if provided
      widget.onPartSelected?.call(part);

      // Show part details dialog
      _showPartDetailsDialog(part);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildViewSelector(),
        const SizedBox(height: 16),
        Expanded(
          child: _buildSvgViewer(),
        ),
        const SizedBox(height: 16),
        _buildSelectedPartsList(),
      ],
    );
  }

  Widget _buildViewSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildViewButton(CarView.front, 'front_view'.tr, Icons.arrow_upward),
        _buildViewButton(CarView.leftSide, 'left_side'.tr, Icons.arrow_back),
        _buildViewButton(
            CarView.rightSide, 'right_side'.tr, Icons.arrow_forward),
        _buildViewButton(CarView.back, 'back_view'.tr, Icons.arrow_downward),
        _buildViewButton(CarView.top, 'top_view'.tr, Icons.arrow_drop_down),
      ],
    );
  }

  Widget _buildViewButton(CarView view, String label, IconData icon) {
    return Obx(() {
      final isSelected = widget.controller.currentView.value == view;
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: () => widget.controller.setView(view),
          icon: Icon(
            icon,
            color: isSelected ? Colors.white : null,
          ),
          label: Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : null,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected ? Theme.of(context).primaryColor : null,
            foregroundColor: isSelected ? Colors.white : null,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildSvgViewer() {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // SVG with interactive elements
              SvgPicture.asset(
                widget.svgAssetPath,
                fit: BoxFit.contain,
              ),

              // Invisible touch handlers for each part
              ..._buildTouchHandlers(),
            ],
          ),
        ),
      );
    });
  }

  List<Widget> _buildTouchHandlers() {
    final handlers = <Widget>[];

    // Only build handlers for the current view
    final currentView = widget.controller.currentView.value;
    final relevantParts =
        _svgIdToPartMap.values.where((part) => part.view == currentView);

    // First, add a layer for the current view group
    handlers.add(
      Positioned.fill(
        child: SvgPicture.asset(
          widget.svgAssetPath,
          fit: BoxFit.contain,
        ),
      ),
    );

    // Then add interactive layers for each part
    for (final part in relevantParts) {
      final isSelected = _selectedParts[part.id] == true;

      // Create a custom clipper for this specific SVG element
      handlers.add(
        Positioned.fill(
          child: GestureDetector(
            onTap: () => _onSvgElementTap(part.id),
            behavior: HitTestBehavior.translucent,
            child: Stack(
              children: [
                // Highlight for selected parts
                if (isSelected)
                  Positioned.fill(
                    child: ColorFiltered(
                      colorFilter: ColorFilter.mode(
                        Colors.red.withOpacity(0.3),
                        BlendMode.srcATop,
                      ),
                      child: SvgPicture.asset(
                        widget.svgAssetPath,
                        fit: BoxFit.contain,
                        colorFilter: ColorFilter.mode(
                          isSelected ? Colors.red : Colors.transparent,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),

                // Hover effect
                Positioned.fill(
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: Container(
                      color: Colors.transparent,
                      child: isSelected
                          ? null
                          : Tooltip(
                              message: part.name.tr,
                              child: Container(
                                color: Colors.transparent,
                              ),
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return handlers;
  }

  Widget _buildSelectedPartsList() {
    return Obx(() {
      final selectedParts = widget.controller.selectedPartIds;

      if (selectedParts.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'selected_parts'.tr,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: selectedParts.map((id) {
                final part = _svgIdToPartMap[id];
                if (part == null) return const SizedBox.shrink();

                return Chip(
                  label: Text(part.name.tr),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () => widget.controller.togglePartSelection(id),
                  backgroundColor: Colors.red.shade100,
                );
              }).toList(),
            ),
          ],
        ),
      );
    });
  }

  void _showPartDetailsDialog(CarPart part) {
    final TextEditingController notesController = TextEditingController(
      text: part.notes,
    );

    Get.dialog(
      AlertDialog(
        title: Text(part.name.tr),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Damage level slider
              Text('damage_level'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              GetX<CarPartsSelectorController>(builder: (_) {
                final damageLevel =
                    widget.controller.damageLevels[part.id] ?? 0;
                return Column(
                  children: [
                    Slider(
                      value: damageLevel.toDouble(),
                      min: 0,
                      max: 5,
                      divisions: 5,
                      label: _getDamageLevelLabel(damageLevel),
                      onChanged: (value) {
                        widget.controller
                            .setDamageLevel(part.id, value.toInt());
                      },
                    ),
                    Text(
                      _getDamageLevelLabel(damageLevel),
                      style: TextStyle(
                        color: DamageLevel.values[damageLevel].color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              }),
              const SizedBox(height: 16),
              // Notes text field
              Text('notes'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              TextField(
                controller: notesController,
                decoration: InputDecoration(
                  hintText: 'enter_notes'.tr,
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (value) {
                  widget.controller.setPartNotes(part.id, value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  String _getDamageLevelLabel(int level) {
    switch (level) {
      case 0:
        return 'none'.tr;
      case 1:
        return 'minor'.tr;
      case 2:
        return 'moderate'.tr;
      case 3:
        return 'major'.tr;
      case 4:
        return 'severe'.tr;
      case 5:
        return 'totaled'.tr;
      default:
        return 'unknown'.tr;
    }
  }
}
