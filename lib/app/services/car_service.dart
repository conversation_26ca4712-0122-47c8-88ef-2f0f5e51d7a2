import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/data/models/user_model.dart';
import 'package:cars_app/app/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/v4.dart';
import 'package:logger/logger.dart';

import '../data/models/car_model.dart';
import '../data/models/vehicle_inspection.dart';
import '../widgets/awesome_snackbar_content.dart';

class CarService extends GetxService {
  static CarService get instance => Get.find();
  final FirebaseFirestore _firestore = Get.find();
  final FirestoreService _service = Get.find();
  final UserModel user = Get.find();

  Future<void> createNewEntry(
      Car car, String carId, WorkshopEntry newEntry) async {
    List<WorkshopEntry> updatedWorkshopHistory =
        List.from(car!.workshopHistory);
    // if(updatedWorkshopHistory.isEmpty){
    //   updatedWorkshopHistory.add(newEntry);
    // }else if(updatedWorkshopHistory.last.statuses.isEmpty){
    //   updatedWorkshopHistory.add(newEntry);
    //
    // }else if(updatedWorkshopHistory.last.statuses.last.status != CarStatus.active){
    //   updatedWorkshopHistory.add(newEntry);
    // }
    updatedWorkshopHistory.add(newEntry);

    await _firestore.collection('cars').doc(car.id).update({
      'workshopEntries':
          updatedWorkshopHistory.map((entry) => entry.toJson()).toList(),
      'lastUpdatedBy': user.id,
      'status': CarStatus.sentRequest.toString().split('.').last,
      'lastUpdateDate': FieldValue.serverTimestamp(),
    });

    showCustomSnackBar(
        title: 'تم بنجاح',
        message: 'تم ارسال الطلب بنجاح',
        contentType: ContentType.success);
    // _service.sendNotificationToUsersByRole(roles: [UserRole.supervisor], title: 'طلب صيانة جديد', body: 'تم ارسال طلب صيانة جديد لمركبة ${car.plateNumber}');

    _service.createNoticiation(UserRole.supervisor.name, [], car, 'newRequest');
    Get.back();
  }

  Future<void> updateCarStatusWithEntry(
      Car car, String carId, CarStatus newStatus, String? notes,
      {String? technicianId, VehicleInspection? inspection}) async {
    try {
      // Create a logger for debugging
      final logger = Logger();
      logger.d(
          "Updating car status with entry: ${car.id}, status: ${newStatus.toString()}");

      // Log meter reading if available
      if (car.meterReading != null) {
        logger.d("Car meter reading: ${car.meterReading}");
      }

      List<WorkshopEntry> updatedWorkshopHistory =
          List.from(car.workshopHistory);
      WorkshopEntry lastEntry = updatedWorkshopHistory.last;
      WorkshopEntryStatus newWorkshopStatus = WorkshopEntryStatus(
        status: newStatus,
        createAt: DateTime.now(),
        senderId: user.id,
        senderName: user.username,
        notes: notes,
      );

      lastEntry.statuses = [
        ...lastEntry.statuses,
        newWorkshopStatus,
      ];
      updatedWorkshopHistory[updatedWorkshopHistory.length - 1] = lastEntry;

      // Create the update data map
      final updateData = {
        'status': newStatus.toString().split('.').last,
        'technicianId': technicianId,
        'workshopEntries':
            updatedWorkshopHistory.map((entry) => entry.toJson()).toList(),
        'lastUpdatedBy': user.id,
        'lastUpdateDate': FieldValue.serverTimestamp(),
      };

      // Add meter reading if available
      if (car.meterReading != null) {
        updateData['meterReading'] = car.meterReading;
        logger.d("Adding meter reading to update: ${car.meterReading}");
      }

      // Add inspection data if available
      if (inspection != null) {
        updateData['inspection'] = inspection.toJson();
        logger.d("Adding inspection data to update");
      }

      // Update the car in Firestore
      await _firestore.collection('cars').doc(carId).update(updateData);
      UserRole? role = getUserRoleByCarStatus(newStatus);

      print('rooooole $role');
      if (role != null) {
        if (role == UserRole.manager) {}

        switch (newStatus) {
          case CarStatus.agreeDeliveryToWorkShop:
            _service.createNoticiation(
                UserRole.supervisor.name, [], car, 'newRequest');
            return;
          default:
            return;
        }
      }
      // _service.sendNotificationToUser('n0gc4wvlLsup8e8GX2gy', 'title', 'body');
      if (newStatus == CarStatus.rejected) {
        createNewEntry(
            car,
            carId,
            WorkshopEntry(
                id: Uuid().v4(),
                carId: carId,
                createAt: DateTime.now(),
                senderId: user.id,
                status: WorkshopStatus.maintenance));
      }
      Get.snackbar(
        'تم بنجاح',
        'تم تحديث حالة المركبة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة المركبة',
        snackPosition: SnackPosition.BOTTOM,
      );
      print('Error updating car status: $e');
    }
  }

  Future<String> getSectorByCarId(String carId) async {
    final carDoc = await _firestore.collection('cars').doc(carId).get();
    return carDoc.data()!['sector'];
  }

  Future<List<String>> getUsersByRole(UserRole role) async {
    return _firestore
        .collection('users')
        .where('role', isEqualTo: role.toString().split('.').last)
        .get()
        .then((value) => value.docs.map((e) => e.id).toList());
  }

  UserRole? getUserRoleByCarStatus(CarStatus status) {
    print(status.name);
    switch (status) {
      case CarStatus.active:
        return null;
      case CarStatus.pending:
        return UserRole.technician;
      case CarStatus.done:
        return UserRole.supervisor;
      case CarStatus.rejected:
        return UserRole.supervisor;
      case CarStatus.sendToLogisticsSupport:
        return UserRole.logisticsSupport;
      case CarStatus.deliveryToSector:
        return UserRole.supervisor;
      case CarStatus.callToWorkshop:
        return null;
      case CarStatus.agreeDeliveryToWorkShop:
        return UserRole.supervisor;
      case CarStatus.sentRequest:
        return UserRole.supervisor;
      default:
    }
    return UserRole.manager;
    if (status == CarStatus.sentRequest) {
      return UserRole.supervisor;
    } else if (status == CarStatus.sendGroup) {
      return null;
    } else if (status == CarStatus.receipt) {
      return null;
    } else if (status == CarStatus.pending) {
      return UserRole.technician;
    } else if (status == CarStatus.done) {
      return UserRole.supervisor;
    } else if (status == CarStatus.sendToLogisticsSupport) {
      return UserRole.logisticsSupport;
    } else if (status == CarStatus.deliveryToSector) {
      return UserRole.supervisor;
    } else if (status == CarStatus.callToWorkshop) {
      return UserRole.supervisor;
    } else if (status == CarStatus.agreeDeliveryToWorkShop) {
      return UserRole.supervisor;
    } else if (status == CarStatus.sentRequest) {
      return UserRole.supervisor;
    } else if (status == CarStatus.active) {
      return null;
    } else {
      return null;
    }
  }

  Future<List<Car>> getCars() async {
    try {
      final snapshot = await _firestore.collection('cars').get();
      return snapshot.docs
          .map((doc) => Car.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to load cars');
    }
  }

  Future<List<Car>> getCarsBySector(String sectorId) async {
    try {
      final snapshot = await _firestore
          .collection('cars')
          .where('sectorId', isEqualTo: sectorId)
          .get();
      return snapshot.docs
          .map((doc) => Car.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to load cars for sector');
    }
  }

  Future<Car?> getCarById(String carId) async {
    try {
      final doc = await _firestore.collection('cars').doc(carId).get();
      if (doc.exists) {
        return Car.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Failed to load car');
    }
  }

  updateCar(Car copyWith) async {
    print(copyWith.toJson());
    await _firestore
        .collection('cars')
        .doc(copyWith.id)
        .update(copyWith.toJson());
  }

  updateCarById(String id, Map<String, dynamic> json) async {
    await _firestore.collection('cars').doc(id).update(json);
  }
}
