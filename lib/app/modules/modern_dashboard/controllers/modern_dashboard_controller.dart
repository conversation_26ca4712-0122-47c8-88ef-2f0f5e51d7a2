// import 'package:get/get.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:get_storage/get_storage.dart';
// import '../../../data/models/car_model.dart';
// import '../../../data/models/user_model.dart';
// import '../../../data/models/sector_model.dart';
// import '../../../data/models/product_model.dart';
// import 'dart:async';
//
// class ModernDashboardController extends GetxController {
//   // Loading states
//   final isLoading = true.obs;
//   final isCarsLoading = true.obs;
//   final isSectorsLoading = true.obs;
//   final isUsersLoading = true.obs;
//   final isProductsLoading = true.obs;
//   final isMaintenanceLoading = true.obs;
//
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//   final GetStorage _storage = GetStorage();
//
//   // Language and RTL support
//   final currentLanguage = 'ar'.obs;
//   final isRTL = true.obs;
//
//   // Current view
//   final currentView = 'dashboard'.obs;
//
//   // Search and filter
//   final searchQuery = ''.obs;
//   final carStatusFilter = Rxn<CarStatus>();
//   final carSectorFilter = Rxn<String>();
//   final userRoleFilter = Rxn<UserRole>();
//   final userStatusFilter = Rxn<bool>();
//   final productCategoryFilter = Rxn<String>();
//   final productStockFilter = Rxn<bool>(); // true for low stock
//
//   // Car stats
//   final totalCars = 0.obs;
//   final activeCars = 0.obs;
//   final maintenanceCars = 0.obs;
//   final pendingCars = 0.obs;
//   final rejectedCars = 0.obs;
//   final archivedCars = 0.obs;
//   final inWorkshopCars = 0.obs;
//   final deliveryToWorkshopCars = 0.obs;
//
//   // Sector stats
//   final totalSectors = 0.obs;
//   final activeSectors = 0.obs;
//
//   // User stats
//   final totalUsers = 0.obs;
//   final activeUsers = 0.obs;
//   final adminUsers = 0.obs;
//   final driverUsers = 0.obs;
//   final viewerUsers = 0.obs;
//
//   // Product stats
//   final totalProducts = 0.obs;
//   final lowStockProducts = 0.obs;
//
//   // Maintenance stats
//   final totalMaintenances = 0.obs;
//   final completedMaintenances = 0.obs;
//   final pendingMaintenances = 0.obs;
//
//   // Data collections
//   final cars = <Car>[].obs;
//   final filteredCars = <Car>[].obs;
//   final sectors = <Sector>[].obs;
//   final filteredSectors = <Sector>[].obs;
//   final users = <UserModel>[].obs;
//   final filteredUsers = <UserModel>[].obs;
//   final products = <Product>[].obs;
//   final filteredProducts = <Product>[].obs;
//   final maintenances = <MaintenanceItem>[].obs;
//   final filteredMaintenances = <MaintenanceItem>[].obs;
//
//   // Car status distribution for charts
//   final carStatusData = <Map<String, dynamic>>[].obs;
//
//   // Car model distribution for charts
//   final carModelData = <Map<String, dynamic>>[].obs;
//
//   // Recent maintenance history
//   final recentMaintenanceHistory = <MaintenanceItem>[].obs;
//
//   // User profile data
//   final currentUser = Rxn<UserModel>();
//
//   // Stream subscriptions
//   StreamSubscription<QuerySnapshot>? _carsSubscription;
//   StreamSubscription<QuerySnapshot>? _sectorsSubscription;
//   StreamSubscription<QuerySnapshot>? _usersSubscription;
//   StreamSubscription<QuerySnapshot>? _productsSubscription;
//
//   // Pagination
//   final carsPage = 0.obs;
//   final sectorsPage = 0.obs;
//   final usersPage = 0.obs;
//   final productsPage = 0.obs;
//   final maintenancesPage = 0.obs;
//   final pageSize = 10.obs;
//
//   // Class to hold maintenance data
//   class MaintenanceItem {
//     final String id;
//     final Car car;
//     final WorkshopEntry maintenance;
//     final DateTime date;
//     final bool isCompleted;
//     final double? cost;
//
//     MaintenanceItem({
//       required this.id,
//       required this.car,
//       required this.maintenance,
//       required this.date,
//       required this.isCompleted,
//       this.cost,
//     });
//   }
//
//   @override
//   void onInit() {
//     super.onInit();
//     // Initialize language from storage
//     _initializeLanguage();
//     // Load all data streams
//     setupStreams();
//     // Apply initial filters
//     applyFilters();
//   }
//
//   @override
//   void onClose() {
//     // Cancel all subscriptions
//     _carsSubscription?.cancel();
//     _sectorsSubscription?.cancel();
//     _usersSubscription?.cancel();
//     _productsSubscription?.cancel();
//     super.onClose();
//   }
//
//   // Initialize language settings from storage
//   void _initializeLanguage() {
//     final savedLanguage = _storage.read<String>('language');
//     if (savedLanguage != null) {
//       currentLanguage.value = savedLanguage;
//       isRTL.value = savedLanguage == 'ar';
//       // Update GetX locale
//       Get.updateLocale(Locale(savedLanguage));
//     }
//   }
//
//   // Change language and update RTL state
//   void changeLanguage(String languageCode) {
//     currentLanguage.value = languageCode;
//     isRTL.value = languageCode == 'ar';
//     // Save to storage
//     _storage.write('language', languageCode);
//     // Update GetX locale
//     Get.updateLocale(Locale(languageCode));
//   }
//
//   // Setup all data streams
//   void setupStreams() {
//     isLoading.value = true;
//     setupCarsStream();
//     setupSectorsStream();
//     setupUsersStream();
//     setupProductsStream();
//     setupMaintenanceData();
//     isLoading.value = false;
//   }
//
//   // Setup cars stream
//   void setupCarsStream() {
//     isCarsLoading.value = true;
//
//     // Listen to the cars collection
//     _carsSubscription = _firestore.collection('cars').snapshots().listen((snapshot) {
//       final carsList = snapshot.docs
//           .map((doc) => Car.fromJson({...doc.data(), 'id': doc.id}))
//           .toList();
//
//       cars.value = carsList;
//
//       // Update car stats
//       totalCars.value = carsList.length;
//       activeCars.value = carsList.where((car) => car.status == CarStatus.active).length;
//       maintenanceCars.value = carsList.where((car) => car.status == CarStatus.maintenance).length;
//       inWorkshopCars.value = carsList.where((car) => car.status == CarStatus.inWorkshop).length;
//       pendingCars.value = carsList.where((car) => car.status == CarStatus.pending).length;
//       rejectedCars.value = carsList.where((car) => car.status == CarStatus.rejected).length;
//       archivedCars.value = carsList.where((car) => car.status == CarStatus.archived).length;
//       deliveryToWorkshopCars.value = carsList.where((car) =>
//           car.status == CarStatus.agreeDeliveryToWorkShop ||
//           car.status == CarStatus.deliveryToWorkShop).length;
//
//       // Update car status distribution for charts
//       updateCarStatusDistribution(carsList);
//
//       // Update car model distribution for charts
//       updateCarModelDistribution(carsList);
//
//       // Apply filters
//       applyCarFilters();
//
//       isCarsLoading.value = false;
//     }, onError: (error) {
//       print('Error loading cars: $error');
//       isCarsLoading.value = false;
//     });
//   }
//
//   // Setup sectors stream
//   void setupSectorsStream() {
//     isSectorsLoading.value = true;
//
//     // Listen to the sectors collection
//     _sectorsSubscription = _firestore.collection('sectors').snapshots().listen((snapshot) {
//       final sectorsList = snapshot.docs
//           .map((doc) => Sector.fromJson({...doc.data(), 'id': doc.id}))
//           .toList();
//
//       sectors.value = sectorsList;
//
//       // Update sector stats
//       totalSectors.value = sectorsList.length;
//       activeSectors.value = sectorsList.where((sector) => sector.isActive).length;
//
//       // Apply filters
//       applySectorFilters();
//
//       isSectorsLoading.value = false;
//     }, onError: (error) {
//       print('Error loading sectors: $error');
//       isSectorsLoading.value = false;
//     });
//   }
//
//   // Setup users stream
//   void setupUsersStream() {
//     isUsersLoading.value = true;
//
//     // Listen to the users collection
//     _usersSubscription = _firestore.collection('users').snapshots().listen((snapshot) {
//       final usersList = snapshot.docs
//           .map((doc) => UserModel.fromJson({...doc.data(), 'id': doc.id}))
//           .toList();
//
//       users.value = usersList;
//
//       // Update user stats
//       totalUsers.value = usersList.length;
//       activeUsers.value = usersList.where((user) => user.isActive).length;
//       adminUsers.value = usersList.where((user) => user.role == UserRole.admin).length;
//       driverUsers.value = usersList.where((user) => user.role == UserRole.driver).length;
//       viewerUsers.value = usersList.where((user) => user.role == UserRole.viewer).length;
//
//       // Apply filters
//       applyUserFilters();
//
//       isUsersLoading.value = false;
//     }, onError: (error) {
//       print('Error loading users: $error');
//       isUsersLoading.value = false;
//     });
//   }
//
//   // Setup products stream
//   void setupProductsStream() {
//     isProductsLoading.value = true;
//
//     // Listen to the products collection
//     _productsSubscription = _firestore.collection('products').snapshots().listen((snapshot) {
//       final productsList = snapshot.docs
//           .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
//           .toList();
//
//       products.value = productsList;
//
//       // Update product stats
//       totalProducts.value = productsList.length;
//       lowStockProducts.value = productsList.where((product) =>
//           product.quantity <= product.minQuantity).length;
//
//       // Apply filters
//       applyProductFilters();
//
//       isProductsLoading.value = false;
//     }, onError: (error) {
//       print('Error loading products: $error');
//       isProductsLoading.value = false;
//     });
//   }
//
//   // Setup maintenance data
//   void setupMaintenanceData() {
//     isMaintenanceLoading.value = true;
//
//     // We'll extract maintenance data from cars with workshop history
//     _carsSubscription?.cancel(); // Cancel existing subscription to avoid duplicates
//
//     _carsSubscription = _firestore.collection('cars').snapshots().listen((snapshot) {
//       final carsList = snapshot.docs
//           .map((doc) => Car.fromJson({...doc.data(), 'id': doc.id}))
//           .toList();
//
//       // Process maintenance data
//       final maintenanceItems = <MaintenanceItem>[];
//
//       for (final car in carsList) {
//         if (car.workshopHistory.isNotEmpty) {
//           for (final entry in car.workshopHistory) {
//             final isCompleted = entry.isCompleted;
//             final latestStatus = entry.statuses.isNotEmpty ? entry.statuses.last : null;
//             final date = latestStatus?.createAt ?? DateTime.now();
//
//             maintenanceItems.add(MaintenanceItem(
//               id: '${car.id}_${entry.id}',
//               car: car,
//               maintenance: entry,
//               date: date,
//               isCompleted: isCompleted,
//               cost: entry.cost,
//             ));
//           }
//         }
//       }
//
//       // Sort by date (most recent first)
//       maintenanceItems.sort((a, b) => b.date.compareTo(a.date));
//
//       // Update maintenance data
//       maintenances.value = maintenanceItems;
//
//       // Update maintenance stats
//       totalMaintenances.value = maintenanceItems.length;
//       completedMaintenances.value = maintenanceItems.where((item) => item.isCompleted).length;
//       pendingMaintenances.value = maintenanceItems.where((item) => !item.isCompleted).length;
//
//       // Update recent maintenance history (most recent 5)
//       recentMaintenanceHistory.value = maintenanceItems.take(5).toList();
//
//       // Apply filters
//       applyMaintenanceFilters();
//
//       isMaintenanceLoading.value = false;
//     }, onError: (error) {
//       print('Error loading maintenance data: $error');
//       isMaintenanceLoading.value = false;
//     });
//   }
//
//   // Apply all filters
//   void applyFilters() {
//     applyCarFilters();
//     applySectorFilters();
//     applyUserFilters();
//     applyProductFilters();
//     applyMaintenanceFilters();
//   }
//
//   // Apply car filters
//   void applyCarFilters() {
//     if (cars.isEmpty) return;
//
//     var filtered = cars.toList();
//
//     // Apply status filter
//     if (carStatusFilter.value != null) {
//       filtered = filtered.where((car) => car.status == carStatusFilter.value).toList();
//     }
//
//     // Apply sector filter
//     if (carSectorFilter.value != null) {
//       filtered = filtered.where((car) => car.sector?.id == carSectorFilter.value).toList();
//     }
//
//     // Apply search filter
//     if (searchQuery.value.isNotEmpty) {
//       final query = searchQuery.value.toLowerCase();
//       filtered = filtered.where((car) =>
//         car.carName.toLowerCase().contains(query) ||
//         car.carModel.toLowerCase().contains(query) ||
//         car.plateNumber.toLowerCase().contains(query)
//       ).toList();
//     }
//
//     // Update filtered cars
//     filteredCars.value = filtered;
//   }
//
//   // Apply sector filters
//   void applySectorFilters() {
//     if (sectors.isEmpty) return;
//
//     var filtered = sectors.toList();
//
//     // Apply search filter
//     if (searchQuery.value.isNotEmpty) {
//       final query = searchQuery.value.toLowerCase();
//       filtered = filtered.where((sector) =>
//         sector.name.toLowerCase().contains(query) ||
//         (sector.description != null && sector.description!.toLowerCase().contains(query)) ||
//         (sector.city != null && sector.city!.toLowerCase().contains(query))
//       ).toList();
//     }
//
//     // Update filtered sectors
//     filteredSectors.value = filtered;
//   }
//
//   // Apply user filters
//   void applyUserFilters() {
//     if (users.isEmpty) return;
//
//     var filtered = users.toList();
//
//     // Apply role filter
//     if (userRoleFilter.value != null) {
//       filtered = filtered.where((user) => user.role == userRoleFilter.value).toList();
//     }
//
//     // Apply status filter
//     if (userStatusFilter.value != null) {
//       filtered = filtered.where((user) => user.isActive == userStatusFilter.value).toList();
//     }
//
//     // Apply search filter
//     if (searchQuery.value.isNotEmpty) {
//       final query = searchQuery.value.toLowerCase();
//       filtered = filtered.where((user) =>
//         user.name.toLowerCase().contains(query) ||
//         user.email.toLowerCase().contains(query) ||
//         (user.phone != null && user.phone!.toLowerCase().contains(query))
//       ).toList();
//     }
//
//     // Update filtered users
//     filteredUsers.value = filtered;
//   }
//
//   // Apply product filters
//   void applyProductFilters() {
//     if (products.isEmpty) return;
//
//     var filtered = products.toList();
//
//     // Apply category filter
//     if (productCategoryFilter.value != null) {
//       filtered = filtered.where((product) =>
//         product.category == productCategoryFilter.value).toList();
//     }
//
//     // Apply stock filter
//     if (productStockFilter.value != null && productStockFilter.value!) {
//       filtered = filtered.where((product) =>
//         product.quantity <= product.minQuantity).toList();
//     }
//
//     // Apply search filter
//     if (searchQuery.value.isNotEmpty) {
//       final query = searchQuery.value.toLowerCase();
//       filtered = filtered.where((product) =>
//         product.name.toLowerCase().contains(query) ||
//         (product.description != null && product.description!.toLowerCase().contains(query)) ||
//         (product.category != null && product.category!.toLowerCase().contains(query))
//       ).toList();
//     }
//
//     // Update filtered products
//     filteredProducts.value = filtered;
//   }
//
//   // Apply maintenance filters
//   void applyMaintenanceFilters() {
//     if (maintenances.isEmpty) return;
//
//     var filtered = maintenances.toList();
//
//     // Apply status filter (completed/pending)
//     if (carStatusFilter.value != null) {
//       if (carStatusFilter.value == CarStatus.maintenance) {
//         filtered = filtered.where((item) => !item.isCompleted).toList();
//       } else if (carStatusFilter.value == CarStatus.active) {
//         filtered = filtered.where((item) => item.isCompleted).toList();
//       }
//     }
//
//     // Apply search filter
//     if (searchQuery.value.isNotEmpty) {
//       final query = searchQuery.value.toLowerCase();
//       filtered = filtered.where((item) =>
//         item.car.carName.toLowerCase().contains(query) ||
//         item.car.plateNumber.toLowerCase().contains(query) ||
//         (item.maintenance.description != null &&
//          item.maintenance.description!.toLowerCase().contains(query))
//       ).toList();
//     }
//     filteredMaintenances.value = filtered;
//   }
//
//   // Update car status distribution for charts
//   void updateCarStatusDistribution(List<Car> carsList) {
//     // Group cars by status
//     final Map<CarStatus, int> statusCounts = {};
//
//     for (var car in carsList) {
//       statusCounts[car.status] = (statusCounts[car.status] ?? 0) + 1;
//     }
//
//     // Convert to list of maps for the chart
//     carStatusData.value = statusCounts.entries.map((entry) {
//       return {
//         'status': entry.key.toString().split('.').last,
//         'count': entry.value,
//       };
//     }).toList();
//   }
//
//   // Update car model distribution for charts
//   void updateCarModelDistribution(List<Car> carsList) {
//     // Group cars by model
//     final Map<String, int> modelCounts = {};
//
//     for (var car in carsList) {
//       modelCounts[car.carModel] = (modelCounts[car.carModel] ?? 0) + 1;
//     }
//
//     // Convert to list of maps for the chart
//     carModelData.value = modelCounts.entries.map((entry) {
//       return {
//         'model': entry.key,
//         'count': entry.value,
//       };
//     }).toList();
//   }
//
//   // Method to refresh all data
//   void refreshDashboard() {
//     isLoading.value = true;
//     setupStreams();
//     isLoading.value = false;
//   }
//
//   // Change current view
//   void changeView(String view) {
//     currentView.value = view;
//     // Reset search and filters when changing views
//     searchQuery.value = '';
//     resetFilters();
//   }
//
//   // Reset all filters
//   void resetFilters() {
//     carStatusFilter.value = null;
//     carSectorFilter.value = null;
//     userRoleFilter.value = null;
//     userStatusFilter.value = null;
//     productCategoryFilter.value = null;
//     productStockFilter.value = null;
//     applyFilters();
//   }
//
//   // Set car status filter
//   void setCarStatusFilter(CarStatus? status) {
//     carStatusFilter.value = status;
//     applyCarFilters();
//     applyMaintenanceFilters();
//   }
//
//   // Set car sector filter
//   void setCarSectorFilter(String? sectorId) {
//     carSectorFilter.value = sectorId;
//     applyCarFilters();
//   }
//
//   // Set user role filter
//   void setUserRoleFilter(UserRole? role) {
//     userRoleFilter.value = role;
//     applyUserFilters();
//   }
//
//   // Set user status filter
//   void setUserStatusFilter(bool? isActive) {
//     userStatusFilter.value = isActive;
//     applyUserFilters();
//   }
//
//   // Set product category filter
//   void setProductCategoryFilter(String? category) {
//     productCategoryFilter.value = category;
//     applyProductFilters();
//   }
//
//   // Set product stock filter
//   void setProductStockFilter(bool? isLowStock) {
//     productStockFilter.value = isLowStock;
//     applyProductFilters();
//   }
//
//   // Set search query and apply to current view
//   void setSearchQuery(String query) {
//     searchQuery.value = query;
//     applyFilters();
//   }
//
//   // Get available car statuses for filter
//   List<FilterOption<CarStatus>> getCarStatusOptions() {
//     return [
//       FilterOption(
//         label: 'active'.tr,
//         value: CarStatus.active,
//         icon: Icons.check_circle_outline,
//       ),
//       FilterOption(
//         label: 'maintenance'.tr,
//         value: CarStatus.maintenance,
//         icon: Icons.build_outlined,
//       ),
//       FilterOption(
//         label: 'workshop'.tr,
//         value: CarStatus.inWorkshop,
//         icon: Icons.home_repair_service_outlined,
//       ),
//       FilterOption(
//         label: 'pending'.tr,
//         value: CarStatus.pending,
//         icon: Icons.pending_outlined,
//       ),
//       FilterOption(
//         label: 'rejected'.tr,
//         value: CarStatus.rejected,
//         icon: Icons.cancel_outlined,
//       ),
//       FilterOption(
//         label: 'archived'.tr,
//         value: CarStatus.archived,
//         icon: Icons.archive_outlined,
//       ),
//     ];
//   }
//
//   // Get available user roles for filter
//   List<FilterOption<UserRole>> getUserRoleOptions() {
//     return [
//       FilterOption(
//         label: 'admin'.tr,
//         value: UserRole.admin,
//         icon: Icons.admin_panel_settings_outlined,
//       ),
//       FilterOption(
//         label: 'driver'.tr,
//         value: UserRole.driver,
//         icon: Icons.drive_eta_outlined,
//       ),
//       FilterOption(
//         label: 'viewer'.tr,
//         value: UserRole.viewer,
//         icon: Icons.visibility_outlined,
//       ),
//     ];
//   }
//
//   // Get available user status options for filter
//   List<FilterOption<bool>> getUserStatusOptions() {
//     return [
//       FilterOption(
//         label: 'active'.tr,
//         value: true,
//         icon: Icons.check_circle_outline,
//       ),
//       FilterOption(
//         label: 'inactive'.tr,
//         value: false,
//         icon: Icons.cancel_outlined,
//       ),
//     ];
//   }
//
//   // Get available product categories for filter
//   List<FilterOption<String>> getProductCategoryOptions() {
//     final categories = <String>{};
//     for (final product in products) {
//       if (product.category != null && product.category!.isNotEmpty) {
//         categories.add(product.category!);
//       }
//     }
//
//     return categories.map((category) => FilterOption(
//       label: category,
//       value: category,
//       icon: Icons.category_outlined,
//     )).toList();
//   }
//
//   // Get available sector options for filter
//   List<FilterOption<String>> getSectorOptions() {
//     return sectors.map((sector) => FilterOption(
//       label: sector.name,
//       value: sector.id,
//       icon: Icons.location_on_outlined,
//     )).toList();
//   }
//
//   // Helper class for filter options
//   class FilterOption<T> {
//     final String label;
//     final T value;
//     final IconData? icon;
//
//     FilterOption({
//       required this.label,
//       required this.value,
//       this.icon,
//     });
//   }
// }
