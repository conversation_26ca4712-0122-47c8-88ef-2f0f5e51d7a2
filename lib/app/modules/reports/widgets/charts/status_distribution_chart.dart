import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../data/models/car_model.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';

class StatusDistribution<PERSON>hart extends StatelessWidget {
  final ReportsController controller;

  const StatusDistributionChart({Key? key, required this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              AppColors.primary.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.pie_chart, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  'توزيع الحالات'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildChartTypeToggle(),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => SizedBox(
                  height: 300,
                  child: controller.chartType.value == ChartType.column
                      ? _buildColumnChart()
                      : _buildPieChart(),
                )),
            const SizedBox(height: 8),
            Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Obx(() => Text(
                      'إجمالي السجلات: ${controller.filteredEntries.length}',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeToggle() {
    return Obx(() => SegmentedButton<ChartType>(
          segments: const [
            ButtonSegment<ChartType>(
              value: ChartType.column,
              icon: Icon(Icons.bar_chart),
              label: Text('أعمدة'),
            ),
            ButtonSegment<ChartType>(
              value: ChartType.pie,
              icon: Icon(Icons.pie_chart),
              label: Text('دائري'),
            ),
          ],
          selected: {controller.chartType.value},
          onSelectionChanged: (Set<ChartType> newSelection) {
            controller.chartType.value = newSelection.first;
          },
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return AppColors.primary;
                }
                return Colors.white;
              },
            ),
            foregroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return Colors.white;
                }
                return AppColors.primary;
              },
            ),
          ),
        ));
  }

  Widget _buildColumnChart() {
    return SfCartesianChart(
      primaryXAxis: const CategoryAxis(
        labelRotation: -45,
        labelIntersectAction: AxisLabelIntersectAction.rotate45,
        labelStyle: TextStyle(fontWeight: FontWeight.bold),
      ),
      primaryYAxis: const NumericAxis(
        labelFormat: '{value}',
        axisLine: AxisLine(width: 0),
        majorTickLines: MajorTickLines(size: 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: [
        ColumnSeries<StatusCount, String>(
          dataSource: controller.statusDistribution,
          xValueMapper: (StatusCount data, _) => _getStatusText(data.status),
          yValueMapper: (StatusCount data, _) => data.count,
          name: 'عدد السجلات',
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelAlignment: ChartDataLabelAlignment.top,
          ),
          pointColorMapper: (StatusCount data, _) =>
              _getStatusColor(data.status),
          borderRadius: BorderRadius.circular(8),
          width: 0.6,
        )
      ],
      palette: const [
        Color(0xFF6B4EFF),
        Color(0xFF4E7DFF),
        Color(0xFF4EAEFF),
        Color(0xFF4EFFEA),
        Color(0xFF4EFF7D),
        Color(0xFFB4FF4E),
        Color(0xFFFFEA4E),
        Color(0xFFFF7D4E),
        Color(0xFFFF4E7D),
      ],
    );
  }

  Widget _buildPieChart() {
    return SfCircularChart(
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        overflowMode: LegendItemOverflowMode.wrap,
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CircularSeries>[
        PieSeries<StatusCount, String>(
          dataSource: controller.statusDistribution,
          xValueMapper: (StatusCount data, _) => _getStatusText(data.status),
          yValueMapper: (StatusCount data, _) => data.count,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.inside,
          ),
          pointColorMapper: (StatusCount data, _) =>
              _getStatusColor(data.status),
          explode: true,
          explodeIndex: 0,
          explodeOffset: '10%',
          radius: '90%',
        )
      ],
      palette: const [
        Color(0xFF6B4EFF),
        Color(0xFF4E7DFF),
        Color(0xFF4EAEFF),
        Color(0xFF4EFFEA),
        Color(0xFF4EFF7D),
        Color(0xFFB4FF4E),
        Color(0xFFFFEA4E),
        Color(0xFFFF7D4E),
        Color(0xFFFF4E7D),
      ],
    );
  }

  String _getStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'نشط'.tr;
      case CarStatus.pending:
        return 'قيد المعالجة'.tr;
      case CarStatus.done:
        return 'مكتمل'.tr;
      case CarStatus.rejected:
        return 'مرفوض'.tr;
      case CarStatus.sendToLogisticsSupport:
        return 'تم الإرسال للدعم اللوجستي'.tr;
      case CarStatus.deliveryToSector:
        return 'تم التسليم للقطاع'.tr;
      case CarStatus.callToWorkshop:
        return 'تم الاستدعاء للورشة'.tr;
      case CarStatus.agreeDeliveryToWorkShop:
        return 'تمت الموافقة على التسليم للورشة'.tr;
      case CarStatus.sentRequest:
        return 'تم إرسال الطلب'.tr;
      default:
        return status.name.tr;
    }
  }

  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return Colors.green;
      case CarStatus.pending:
        return Colors.orange;
      case CarStatus.done:
        return Colors.blue;
      case CarStatus.rejected:
        return Colors.red;
      case CarStatus.sendToLogisticsSupport:
        return Colors.purple;
      case CarStatus.deliveryToSector:
        return Colors.teal;
      case CarStatus.callToWorkshop:
        return Colors.amber;
      case CarStatus.agreeDeliveryToWorkShop:
        return Colors.indigo;
      case CarStatus.sentRequest:
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }
}
