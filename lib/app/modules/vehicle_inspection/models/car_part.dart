import 'package:flutter/material.dart';

/// Represents a selectable part of a vehicle
class CarPart {
  /// Unique identifier for the part
  final String id;
  
  /// Display name for the part (will be translated)
  final String name;
  
  /// Path that defines the shape of the part
  final Path path;
  
  /// The view this part belongs to (front, side, back, top)
  final CarView view;
  
  /// Whether this part is currently selected
  bool isSelected;
  
  /// Optional notes about this part
  String? notes;
  
  /// Optional damage level (0-5)
  int? damageLevel;
  
  CarPart({
    required this.id,
    required this.name,
    required this.path,
    required this.view,
    this.isSelected = false,
    this.notes,
    this.damageLevel,
  });
  
  /// Creates a copy of this part with the given fields replaced
  CarPart copyWith({
    String? id,
    String? name,
    Path? path,
    CarView? view,
    bool? isSelected,
    String? notes,
    int? damageLevel,
  }) {
    return CarPart(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      view: view ?? this.view,
      isSelected: isSelected ?? this.isSelected,
      notes: notes ?? this.notes,
      damageLevel: damageLevel ?? this.damageLevel,
    );
  }
}

/// Represents the different views of a vehicle
enum CarView {
  front,
  back,
  leftSide,
  rightSide,
  top
}

/// Represents a damage level for a car part
enum DamageLevel {
  none,
  minor,
  moderate,
  major,
  severe,
  totaled
}

/// Extension to get a color based on damage level
extension DamageLevelColor on DamageLevel {
  Color get color {
    switch (this) {
      case DamageLevel.none:
        return Colors.transparent;
      case DamageLevel.minor:
        return Colors.yellow.shade300;
      case DamageLevel.moderate:
        return Colors.orange;
      case DamageLevel.major:
        return Colors.deepOrange;
      case DamageLevel.severe:
        return Colors.red;
      case DamageLevel.totaled:
        return Colors.red.shade900;
    }
  }
}
