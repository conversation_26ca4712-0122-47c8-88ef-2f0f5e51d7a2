import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/product_model.dart';
import '../models/transaction_model.dart';
import '../../../data/providers/inventory_firestore_provider.dart';
// Import your authentication service or way to get userId
// import 'package:your_app/app/services/auth_service.dart';

class TransactionController extends GetxController {
  final InventoryFirestoreProvider provider = Get.find<InventoryFirestoreProvider>();
  // final AuthService authService = Get.find(); // Example

  late final TransactionType transactionType; // Made late final

  // Holds selected products (productId -> ProductModel)
  final RxMap<String, ProductModel> _selectedProductDetails = <String, ProductModel>{}.obs;
  // Holds the quantity change for each selected product (productId -> quantityChange)
  final RxMap<String, int> productQuantities = <String, int>{}.obs;

  final RxBool isProcessing = false.obs;
  final RxString error = ''.obs;

  // Expose selected product IDs for the UI list builder
  List<String> get selectedProductIds => productQuantities.keys.toList();

  // <<<---- ADD THIS HELPER METHOD ---->>>
  ProductModel? getSelectedProductDetails(String productId) {
    return _selectedProductDetails[productId];
  }
  // <<<----------------------------- >>>

  @override
  void onInit() {
    super.onInit();
    // Initialize transactionType safely from arguments
    if (Get.arguments is TransactionType) {
      transactionType = Get.arguments;
    } else {
      // Handle error: Argument is not the expected type or is null
      print("Error: Transaction type argument missing or invalid!");
      transactionType = TransactionType.usage; // Default or throw error
      // Optionally show error message and close the screen
      // Get.snackbar('Error', 'Transaction type missing.', ...);
      // Get.back();
    }
  }


  String get transactionTitle {
    switch(transactionType) {
      case TransactionType.purchase: return "Record Purchase";
      case TransactionType.usage: return "Record Usage";
      case TransactionType.sale: return "Record Sale";
      case TransactionType.adjustment: return "Adjust Stock";
    }
  }

  // Determines the sign for quantity changes based on transaction type
  // Returns 1 for increase (purchase), -1 for decrease (usage, sale)
  // Adjustment needs special handling in UI/updateQuantity if it can be +/-
  int getQuantityChangeSignMultiplier() {
    switch (transactionType) {
      case TransactionType.purchase:
        return 1;
      case TransactionType.usage:
      case TransactionType.sale:
        return -1;
      case TransactionType.adjustment:
      // For adjustment, the SIGN should ideally come from the input itself
      // Let's assume QuantitySelectorWidget provides positive values,
      // and we need separate +/- buttons or logic here for adjustment.
      // Defaulting to 1, but this needs UI refinement for adjustments.
        return 1; // Needs review based on UI for Adjustment type
    }
  }

  void selectProduct(ProductModel product) {
    if (product.id == null) return; // Safety check

    if (!productQuantities.containsKey(product.id!)) {
      _selectedProductDetails[product.id!] = product;
      // Start with quantity 1 (absolute). Sign applied later or handled by UI for adjustment.
      productQuantities[product.id!] = 1; // Always start with positive 1 in UI map
    }
    // Close the selection modal/screen
    if(Get.isBottomSheetOpen ?? false) Get.back();
    // else if (Get.isDialogOpen ?? false) Get.back();
    // else // handle if navigated to a separate page
  }

  void removeProduct(String productId) {
    productQuantities.remove(productId);
    _selectedProductDetails.remove(productId);
  }

  void updateQuantity(String productId, int newAbsoluteQuantity) {
    if (productQuantities.containsKey(productId)) {
      // Store the absolute quantity from the UI widget
      productQuantities[productId] = newAbsoluteQuantity.abs();
    }
  }

  Future<void> submitTransaction() async {
    if (productQuantities.isEmpty) {
      Get.snackbar('Error', 'Please select at least one product.', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    // Basic validation: Ensure quantities are not zero
    if (productQuantities.values.any((qty) => qty == 0)) {
      Get.snackbar('Error', 'Quantities cannot be zero.', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    if (isProcessing.value) return; // Prevent double submission
    isProcessing.value = true;
    error.value = '';
    final String currentUserId = "DUMMY_USER_ID"; // !!! Replace with actual user ID logic !!!

    // Use a temporary list to avoid issues if map changes during iteration
    final List<MapEntry<String, int>> entries = productQuantities.entries.toList();

    try {
      for (var entry in entries) {
        final productId = entry.key;
        final uiQuantity = entry.value; // This is the ABSOLUTE quantity from UI
        final product = _selectedProductDetails[productId];

        if (product == null) {
          print("Warning: Product details not found for ID $productId during submission.");
          continue;
        }

        // Determine the actual quantity change with sign
        // Special handling for ADJUSTMENT might be needed here if UI allows +/- input
        final int quantityChange;
        if (transactionType == TransactionType.adjustment) {
          // TODO: Refine Adjustment Logic
          // Assumption: UI's QuantitySelectorWidget only gives positive values.
          // Need separate UI element (like +/- buttons next to item) to decide if adjustment is increase or decrease.
          // For now, treating adjustment as POSITIVE - THIS NEEDS REVIEW
          print("Warning: Adjustment quantity sign handling needs review!");
          quantityChange = uiQuantity; // Treat as increase for now
        } else {
          quantityChange = uiQuantity * getQuantityChangeSignMultiplier();
        }


        if (quantityChange == 0) continue; // Skip zero changes if any slip through

        await provider.recordTransaction(
          productId: productId,
          productName: product.name,
          type: transactionType,
          quantityChange: quantityChange, // Pass the calculated change with sign
          userId: currentUserId,
          // relatedJobId: Get.arguments?['jobId'],
          notes: "Recorded via app",
        );
      }

      Get.back();
      Get.snackbar('Success', '$transactionTitle recorded successfully.', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green, colorText: Colors.white);
      productQuantities.clear();
      _selectedProductDetails.clear();

    } catch (e) {
      print("Transaction submission failed: $e");
      error.value = e.toString();
      Get.snackbar('Error', 'Failed to submit: ${e.toString()}', snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 5), backgroundColor: Colors.red, colorText: Colors.white);
    } finally {
      isProcessing.value = false;
    }
  }
}