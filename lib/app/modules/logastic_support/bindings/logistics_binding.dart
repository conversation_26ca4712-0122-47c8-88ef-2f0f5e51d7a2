import 'package:get/get.dart';


import '../../sector/controller/sector_controller.dart';
import '../../user/controller/user_controller.dart';
import '../controller/logistics_controller.dart';

class LogisticsBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(SectorController());
    Get.lazyPut<UserController>(() => UserController(), fenix: true);
    Get.lazyPut<LogisticsController>(
          () => LogisticsController(),
    );
  }
}