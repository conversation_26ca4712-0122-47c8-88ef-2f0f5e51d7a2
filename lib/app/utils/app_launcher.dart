import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// A class that handles app crashes and restarts the app when necessary
class AppLauncher {
  static final AppLauncher _instance = AppLauncher._internal();
  factory AppLauncher() => _instance;
  AppLauncher._internal();

  final Logger _logger = Logger();
  bool _isHandlingError = false;
  int _errorCount = 0;
  DateTime? _lastErrorTime;
  static const int _maxErrorsBeforeDelay = 3;
  static const Duration _errorCountResetDuration = Duration(minutes: 5);
  static const Duration _restartDelay = Duration(seconds: 2);

  /// Initialize the error handlers
  void initialize() {
    // Set up Flutter error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleError(details.exception, details.stack);
    };

    // Handle errors that occur in the Dart isolate
    Isolate.current.addErrorListener(RawReceivePort((pair) {
      final List<dynamic> errorAndStacktrace = pair;
      _handleError(errorAndStacktrace[0], errorAndStacktrace[1]);
    }).sendPort);

    // Handle async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _handleError(error, stack);
      return true;
    };

    _logger.i('AppLauncher initialized');
  }

  /// Handle errors and decide whether to restart the app
  void _handleError(dynamic error, StackTrace? stackTrace) {
    // Prevent recursive error handling
    if (_isHandlingError) return;
    _isHandlingError = true;

    try {
      _logger.e('App error caught', error: error, stackTrace: stackTrace);
      
      // Check if we've had too many errors recently
      final now = DateTime.now();
      if (_lastErrorTime != null && 
          now.difference(_lastErrorTime!) < _errorCountResetDuration) {
        _errorCount++;
      } else {
        _errorCount = 1;
      }
      _lastErrorTime = now;

      // If we've had too many errors in a short time, wait longer before restarting
      if (_errorCount > _maxErrorsBeforeDelay) {
        _logger.w('Too many errors ($_errorCount) in a short time, delaying restart');
        Future.delayed(Duration(seconds: _errorCount * 2), () {
          _restartApp();
        });
      } else {
        // Otherwise restart quickly
        Future.delayed(_restartDelay, () {
          _restartApp();
        });
      }
    } catch (e) {
      _logger.e('Error in error handler', error: e);
    } finally {
      _isHandlingError = false;
    }
  }

  /// Restart the app
  void _restartApp() {
    _logger.i('Restarting app after error');
    
    // Use runApp to restart the app with the same widget
    // This assumes that the main app widget is stored somewhere accessible
    // For a more complete solution, you might need to store the app widget reference
    try {
      // Get the current context from the navigator key if available
      final context = navigatorKey.currentContext;
      if (context != null) {
        // Show a brief message to the user
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم اكتشاف خطأ، جاري إعادة تشغيل التطبيق...'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      // Wait a moment for the snackbar to be visible
      Future.delayed(const Duration(seconds: 1), () {
        restartAppFunction?.call();
      });
    } catch (e) {
      _logger.e('Error during app restart', error: e);
      // If we can't restart gracefully, force restart
      restartAppFunction?.call();
    }
  }

  // Global navigator key to access context from anywhere
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  // Function to be set in main.dart that knows how to restart the app
  static VoidCallback? restartAppFunction;
}
