import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/services/car_service.dart';
import 'package:cars_app/app/services/firestore_service.dart';
import 'package:cars_app/app/services/problem_tag_service.dart';
import 'package:cars_app/app/widgets/awesome_snackbar_content.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/problem_tag_model.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/user_model.dart';
import '../../../services/user_storage_service.dart';
import '../../user/controller/user_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'car_controller.dart';

class WorkshopFormController extends GetxController {
  final carController = Get.find<CarController>();

  final formKey = GlobalKey<FormState>();
  final notesController = TextEditingController();
  // Problem tags that the user has selected
  final RxList<String> selectedProblemTags = <String>[].obs;
  final ProblemTagService tagService = Get.find<ProblemTagService>();

  final LocalStorageService localStorageService =
      Get.find<LocalStorageService>();

  final RxString userId = ''.obs;
  final RxString userName = ''.obs;

  final Rx<WorkshopStatus> currentStatus = WorkshopStatus.maintenance.obs;
  final RxBool hasReplacement = false.obs;
  final RxString maintenanceType = 'routine_maintenance'.obs;
  final RxBool isSubmitting = false.obs;
  final RxString errorMessage = ''.obs;

  // Car data
  final Rxn<Car> car = Rxn<Car>();
  final Rxn<WorkshopEntry> existingEntry = Rxn<WorkshopEntry>();
  final RxBool isMaintenance = true.obs;
  final CarService carService = Get.find<CarService>();
  @override
  void onInit() async {
    super.onInit();
    // Get arguments passed to the form
    if (Get.arguments != null) {
      car.value = Get.arguments['car'];
      existingEntry.value = Get.arguments['existingEntry'];
      isMaintenance.value = Get.arguments['isMaintenance'] ?? true;
    }

    userId.value = (await localStorageService.getUserId())!;
    userName.value = (await localStorageService.getUsername())!;
    // Initialize form with existing entry data if available
    if (existingEntry.value != null) {
      currentStatus.value = existingEntry.value!.status;
      // notesController.text = existingEntry.value!. ?? '';

      // if (existingEntry.value!.additionalData != null) {
      //   hasReplacement.value = existingEntry.value!.additionalData!['hasReplacement'] ?? false;
      //   maintenanceType.value = existingEntry.value!.additionalData!['maintenanceType'] ?? 'routine_maintenance';
      // }
    }
  }

  submitForm() async {
    if (notesController.text.isEmpty) {
      Get.snackbar('حقل فارغ', 'الرجاء ادخال الملاحظات');
    }
    if (isSubmitting.value) return;
    if (!formKey.currentState!.validate()) return;

    print('clicked');
    try {
      print('started');

      isSubmitting.value = true;
      errorMessage.value = '';

      WorkshopEntry newEntry = WorkshopEntry(
        id: Uuid().v4(),
        carId: car.value!.id,
        status: currentStatus.value,
        createAt: DateTime.now(),
        maintenance_type: maintenanceType.value,
        senderId: userId.value!,
        problemTagIds: selectedProblemTags.toList(),
        statuses: [
          WorkshopEntryStatus(
              status: CarStatus.sentRequest,
              createAt: DateTime.now(),
              senderId: userId.value,
              notes: notesController.text,
              senderName: userName.value)
        ],
        isThereIsReplacement: hasReplacement.value,
      );

      carService.createNewEntry(car.value!, car.value!.id, newEntry);
      Get.back();
    } catch (e) {
      errorMessage.value = 'error_submitting_form'.tr;
    } finally {
      print('finished');
      isSubmitting.value = false;
    }
  }

  @override
  void onClose() {
    notesController.dispose();
    super.onClose();
  }

  // String getPreviousNotes(String search) {}
}
