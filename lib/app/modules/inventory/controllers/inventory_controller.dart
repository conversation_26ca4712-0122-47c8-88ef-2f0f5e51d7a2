import 'package:get/get.dart';
import '../../../data/providers/inventory_firestore_provider.dart';
import '../models/product_model.dart';
import '../models/transaction_model.dart';
import '../../../routes/app_pages.dart';

class InventoryController extends GetxController {
  final InventoryFirestoreProvider provider = Get.find<InventoryFirestoreProvider>();

  final RxBool isLoading = true.obs;
  final RxBool isSavingProduct = false.obs;
  final RxString error = ''.obs;

  RxList<ProductModel> products = <ProductModel>[].obs;
  RxList<Map<String, String>> categories = <Map<String, String>>[].obs;

  RxString selectedCategory = ''.obs;

  Map<String, int> stats = {
    'total': 0,
    'inStock': 0,
    'outOfStock': 0,
  };

  @override
  void onInit() {
    super.onInit();
    _bindProductsStream();
  }

  List<ProductModel> get filteredItems {
    if (selectedCategory.value.isEmpty) {
      return products;
    }
    return products.where((p) => p.id == selectedCategory.value).toList();
  }

  void resetFilters() {
    selectedCategory.value = '';
    updateStats();
  }

  void applyFilters({String? category}) {
    if (category != null) {
      selectedCategory.value = category;
    }
    updateStats();
  }

  void updateStats() {
    final items = filteredItems;
    stats['total'] = items.length;
    stats['inStock'] = items.where((p) => p.currentQuantity > 0).length;
    stats['outOfStock'] = items.where((p) => p.currentQuantity == 0).length;
    update();
  }



  void _bindProductsStream() {
    if (!isLoading.value) isLoading.value = true;
    error.value = '';

    products.bindStream(provider.getProductsStream().handleError((err) {
      error.value = "Failed to load products.";
      isLoading.value = false;
    }));

    ever(products, (_) {
      if (isLoading.value) isLoading.value = false;
      if (error.isNotEmpty) error.value = '';
      updateStats();
    });
  }

  Future<void> refreshProducts() async {
    _bindProductsStream();
  }

  void goToAddProduct() {
    Get.toNamed(Routes.addEditProduct);
  }

  void goToEditProduct(ProductModel product) {
    Get.toNamed(Routes.addEditProduct, arguments: product);
  }

  void goToRecordUsage() {
    Get.toNamed(Routes.transaction, arguments: TransactionType.usage);
  }

  void goToRecordPurchase() {
    Get.toNamed(Routes.transaction, arguments: TransactionType.purchase);
  }

  void goToRecordSale() {
    Get.toNamed(Routes.transaction, arguments: TransactionType.sale);
  }

  void goToRecordAdjustment() {
    Get.toNamed(Routes.transaction, arguments: TransactionType.adjustment);
  }

  void goToTransactionHistory() {
    Get.toNamed(Routes.transactionHistory);
  }

  Future<void> addProduct(ProductModel product) async {
    if (isSavingProduct.value) return;
    isSavingProduct.value = true;
    try {
      final initialQuantity = product.currentQuantity;
      await provider.addProduct(product);
      // Optionally: create initial transaction log if needed
      if (initialQuantity > 0) {
        // Add transaction logic here
      }
      isSavingProduct.value = false;
    } catch (e) {
      error.value = "Failed to add product.";
      isSavingProduct.value = false;
    }
  }

  Future<void> editProduct(ProductModel product) async {
    if (isSavingProduct.value) return;
    isSavingProduct.value = true;
    try {
      await provider.updateProduct(product);
      isSavingProduct.value = false;
    } catch (e) {
      error.value = "Failed to update product.";
      isSavingProduct.value = false;
    }
  }

  Future<void> deleteProduct(ProductModel product) async {
    try {
      await provider.deleteProduct(product.id!);
      // Optionally: show snackbar or update UI
    } catch (e) {
      error.value = "Failed to delete product.";
    }
  }
}