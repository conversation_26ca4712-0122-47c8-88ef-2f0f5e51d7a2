import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/data/models/user_model.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:cars_app/app/services/car_rotation_service.dart';
import 'package:cars_app/app/themes/app_theme.dart';
import 'package:cars_app/app/widgets/awesome_snackbar_content.dart';
import 'package:cars_app/app/widgets/car_rotation_dashboard.dart';
import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:cars_app/app/widgets/custom_text_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/notification_icon_widget.dart';
import '../../../widgets/sector_car_card.dart';
import '../controller/sector_manager_controller.dart';
import '../../../data/models/car_model.dart';

class SectorMangerView extends GetView<SectorManagerController> {
  const SectorMangerView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.cars.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.directions_car_outlined,
                    size: 64,
                    color: AppColors.primary.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد سيارات في هذا القطاع',
                    style: TextStyle(
                      fontSize: 18,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          }

          return StreamBuilder<List<Car>>(
            stream: controller.getCarsStream(),
            builder: (context, snapshot) {
              final cars = snapshot.data ?? [];
              return NestedScrollView(
                headerSliverBuilder: (context, innerBoxIsScrolled) {
                  return [
                    _buildSliverAppBar(context, cars, innerBoxIsScrolled),
                  ];
                },
                body: Column(
                  children: [
                    TabBar(
                      indicatorColor: AppColors.primary,
                      labelColor: AppColors.primary,
                      unselectedLabelColor: Colors.grey,
                      tabs: const [
                        Tab(text: 'السيارات'),
                        Tab(text: 'الدعم'),
                        Tab(text: 'اعاده'),
                        Tab(text: 'التدوير'),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          Container(
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            child: ListView.builder(
                              itemCount: cars.length,
                              padding: const EdgeInsets.only(top: 8, bottom: 16),
                              itemBuilder: (context, index) {
                                final car = cars[index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: SectorCarCard(
                                    sectorId: controller.sector.value!.id,
                                    uid: controller.uid.value,
                                    car: car,
                                    confirmCallCarToWorkshop: () {
                                      controller.carService.updateCarStatusWithEntry(
                                          car, car.id, CarStatus.agreeDeliveryToWorkShop, '');
                                    },
                                    rejectCallCarToWorkshop: () {
                                      controller.carService.updateCarStatusWithEntry(
                                          car, car.id, CarStatus.rejected, '');
                                    },
                                    cancelAction: () {
                                      controller.carService.updateCarStatusWithEntry(
                                          car, car.id, CarStatus.rejected, '');
                                    },
                                    confirmAction: () {
                                      controller.carService.updateCarStatusWithEntry(
                                          car, car.id, CarStatus.active, '');
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                          _buildSupportWidget(),
                          _buildRestoreWidget(),
                          CarRotationDashboard(
                            sectorId: controller.sector.value!.id,
                            action: null,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context, List<Car> cars, bool innerBoxIsScrolled) {
    return SliverAppBar(
      expandedHeight: 220.0,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      elevation: 0,
      forceElevated: innerBoxIsScrolled,
      title: Obx(() {
        return Text(
          'مشرف قطاع ${controller.sector.value?.name ?? ''}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        );
      }),
      actions: [
        // Search icon
        Obx(() => controller.isSearching.value
            ? IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: controller.toggleSearch,
              )
            : IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: controller.toggleSearch,
              )),
        const NotificationIconWidget(),
        IconButton(
          onPressed: () => Get.toNamed(Routes.PROFILE),
          icon: const Icon(Icons.person, color: Colors.white),
        ),
        IconButton(
          onPressed: () => Get.toNamed(Routes.CarRotationDashboard, arguments: controller.sector.value?.id),
          icon: const Icon(Icons.rotate_90_degrees_ccw, color: Colors.white),
        ),
      ],
      // Search bar when isSearching is true
      bottom: Obx(() => controller.isSearching.value
          ? PreferredSize(
              preferredSize: const Size.fromHeight(60.0),
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                color: AppColors.primary,
                child: TextField(
                  controller: controller.searchController,
                  autofocus: true,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'بحث عن سيارة...',
                    hintStyle: const TextStyle(color: Colors.white70),
                    prefixIcon: const Icon(Icons.search, color: Colors.white),
                    filled: true,
                    fillColor: Colors.white.withOpacity(0.2),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onChanged: controller.updateSearchQuery,
                ),
              ),
            )
          : const PreferredSize(
              preferredSize: Size.fromHeight(0),
              child: SizedBox(),
            )),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          color: AppColors.primary,
          child: Padding(
            padding: const EdgeInsets.only(top: 100.0, left: 16.0, right: 16.0),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildInfoColumn(
                          'عدد السيارات',
                          '${cars.where((car) => car.tempSectorId.isEmpty || car.tempSectorId == controller.sector.value!.id).length}',
                          Icons.directions_car,
                        ),
                        _buildInfoColumn(
                          'نشطة',
                          '${cars.where((car) => car.status == CarStatus.active && car.tempSectorId.isEmpty).length}',
                          Icons.check_circle,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'استدعاء للورشة',
                          '${cars.where((car) => car.status == CarStatus.callToWorkshop).length}',
                          Icons.call_made,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'عند الفني',
                          '${cars.where((car) => car.status == CarStatus.pending).length}',
                          Icons.accessibility_new,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'أرسال مجموعة',
                          '${cars.where((car) => car.status == CarStatus.sendGroup).length}',
                          Icons.group,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'تم الاستلام',
                          '${cars.where((car) => car.status == CarStatus.receipt).length}',
                          Icons.receipt,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'ارسال طلب',
                          '${cars.where((car) => car.status == CarStatus.sentRequest).length}',
                          Icons.request_page,
                        ),
                        Container(
                          width: 1,
                          height: 40,
                          color: Colors.white.withOpacity(0.2),
                        ),
                        _buildInfoColumn(
                          'السيارات الجاهزة',
                          '${cars.where((car) => car.status == CarStatus.done).length}',
                          Icons.done,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSupportWidget() {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
        onPressed: () {
          Get.bottomSheet(
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  _buildNotesCard(),
                  CustomButton(
                    isSwipe: true,
                    text: 'تأكيد',
                    onPressed: () {
                      controller.reasonController.text = '';
                      CarRotationService rotationService = Get.find();
                      rotationService.createCarRotation(
                          car: null,
                          action: RotateAction.support,
                          cause: controller.reasonController.text,
                          fromSectorId: '',
                          toSector: RotateStatus.accepted,
                          toSectorId: controller.sector.value!.id,
                          fromSectorName: '',
                          toSectorName: controller.sector.value!.name,
                          type: RotateType.temporary,
                          isActive: false);
                      Get.back();
                      showCustomSnackBar(
                          title: 'طلب دعم',
                          message: 'تم ارسال طلب الدعم',
                          contentType: ContentType.success);
                    },
                  )
                ],
              ),
            ),
          );
        },
      ),
      body: CarRotationDashboard(
        action: RotateAction.support,
        sectorId: controller.sector.value!.id,
      ),
    );
  }

  Widget _buildRestoreWidget() {
    return Scaffold(
      body: CarRotationDashboard(
        action: RotateAction.restore,
        sectorId: controller.sector.value!.id,
      ),
    );
  }

  Widget _buildNotesCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.note_add,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'سبب الدعم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: controller.reasonController,
            maxLines: 3,
            textDirection: TextDirection.rtl,
            style: const TextStyle(fontFamily: 'Cairo'),
            decoration: InputDecoration(
              hintText: 'اكتب سبب الدعم',
              hintStyle: const TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // Handle notes input
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
