import 'dart:typed_data';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/material.dart' hide Column;
import '../../data/models/car_model.dart';
import '../../modules/reports/controllers/reports_controller.dart';
import 'pdf_helpers.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';

/// Service for generating PDF reports
class PdfExportService {
  /// Show dialog to choose export format
  Future<ExportFormat?> showExportFormatDialog(BuildContext context) async {
    return showDialog<ExportFormat>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('اختر صيغة التصدير'),
          content: SingleChildScrollView(
            child:   ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('PDF'),
              onTap: () => Navigator.of(context).pop(ExportFormat.pdf),
            ),
          ),
        );
      },
    );
  }

  /// Generate daily report based on selected format
  Future<Uint8List?> generateDailyReportWithFormat({
    required BuildContext context,
    required List<Car> cars,
  }) async {
    final format = await showExportFormatDialog(context);
    if (format == null) return null;

    return generateDailyReport(cars: cars);
  }

  /// Generate a workshop report PDF
  Future<Uint8List> generateWorkshopReport({
    required List<WorkshopReportEntry> entries,
    required DateTime startDate,
    required DateTime endDate,
    required List<StatusCount> statusDistribution,
    required List<SectorCount> sectorDistribution,
  }) async {
    // Load and register the Noto Sans Arabic font
    final arabicTtf = await PdfHelpers.loadArabicFont();

    // Format date string for display
    final dateStr =
        '${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}';

    // Create PDF document with metadata
    final pdf = pw.Document(
      title: 'تقرير ورشة',
      author: 'نظام إدارة المركبات',
      creator: 'تطبيق إدارة المركبات',
      subject: 'تقرير حالة المركبات في الورشة',
      keywords: 'ورشة, مركبات, تقرير, صيانة',
      producer: 'Cars App',
    );

    // Add pages to the document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicTtf,
          bold: arabicTtf,
        ),
        header: (context) => PdfHelpers.buildHeader(
            context, arabicTtf, 'تقرير حالة المركبات في الورشة'),
        footer: (context) => PdfHelpers.buildFooter(context, arabicTtf),
        build: (context) => [
          // Title
          pw.Container(
            padding:
            const pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.primaryColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              border: pw.Border.all(color: PdfHelpers.primaryColor, width: 1),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  'تقرير ورشة المركبات',
                  style: pw.TextStyle(
                    color: PdfHelpers.primaryColor,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: arabicTtf,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'الفترة: $dateStr',
                  style: pw.TextStyle(
                    color: PdfHelpers.textSecondaryColor,
                    fontSize: 14,
                    font: arabicTtf,
                  ),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Summary Section
          PdfHelpers.sectionHeader('ملخص الإحصائيات', arabicTtf),
          pw.SizedBox(height: 10),

          // Summary Cards
          pw.Container(
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.backgroundColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  child: PdfHelpers.infoCard(
                      'عدد السجلات', '${entries.length}', arabicTtf),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: PdfHelpers.infoCard(
                      'عدد القطاعات',
                      '${sectorDistribution.length}', arabicTtf),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Detailed Records Section
          PdfHelpers.sectionHeader('تفاصيل السجلات', arabicTtf),
          pw.SizedBox(height: 10),

          // Detailed Records Table - with pagination for large datasets
          _buildDetailedRecordsTable(context, entries, arabicTtf),

          pw.SizedBox(height: 20),

          // Sector Distribution Section
          PdfHelpers.sectionHeader('توزيع القطاعات', arabicTtf),
          pw.SizedBox(height: 10),

          // Sector Distribution Table
          _buildSectorDistributionTable(context, sectorDistribution, arabicTtf),
        ],
      ),
    );

    return pdf.save();
  }

  /// Generate a car report PDF
  Future<Uint8List> generateCarReport({
    required Car car,
    required List<WorkshopReportEntry> entries,
    required List<StatusCount> statusDistribution,
  }) async {
    // Load and register the Noto Sans Arabic font
    final arabicTtf = await PdfHelpers.loadArabicFont();

    // Create PDF document with metadata
    final pdf = pw.Document(
      title: 'تقرير المركبة',
      author: 'نظام إدارة المركبات',
      creator: 'تطبيق إدارة المركبات',
      subject: 'تقرير حالة المركبة في الورشة',
      keywords: 'ورشة, مركبات, تقرير, صيانة',
      producer: 'Cars App',
    );

    // Add pages to the document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicTtf,
          bold: arabicTtf,
        ),
        header: (context) => PdfHelpers.buildHeader(
            context, arabicTtf, 'تقرير حالة المركبة في الورشة'),
        footer: (context) => PdfHelpers.buildFooter(context, arabicTtf),
        build: (context) => [
          // Car Details Section - More compact
          _buildCarDetailsSection(car, arabicTtf),

          pw.SizedBox(height: 10),

          // Car Information Section - More compact
          PdfHelpers.sectionHeader('معلومات المركبة', arabicTtf),
          pw.SizedBox(height: 5),

          // Car Info Grid - More compact
          _buildCarInfoGrid(car, arabicTtf),

          pw.SizedBox(height: 20),

          // Status Distribution Section - More compact
          if (statusDistribution.isNotEmpty) ...[
            PdfHelpers.sectionHeader('توزيع الحالات', arabicTtf),
            pw.SizedBox(height: 5),

            // Status Distribution Table - More compact
            _buildStatusDistributionTable(
                context, statusDistribution, arabicTtf),

            pw.SizedBox(height: 10),
          ],

          // Workshop Entries Section
          PdfHelpers.sectionHeader('سجلات الورشة', arabicTtf),
          pw.SizedBox(height: 10),

          // Workshop Entries Table - with pagination for large datasets
          _buildWorkshopEntriesTable(context, entries, arabicTtf),

          // Status Legend - More compact
          pw.SizedBox(height: 10),
          _buildStatusLegend(arabicTtf),
        ],
      ),
    );

    return pdf.save();
  }

  /// Generate a status summary report PDF
  Future<Uint8List> generateStatusSummaryReport({
    required Map<CarStatus, List<Car>> carsByStatus,
    required DateTime startDate,
    required DateTime endDate,
    String? sectorName,
  }) async {
    // Load and register the Noto Sans Arabic font
    final arabicTtf = await PdfHelpers.loadArabicFont();

    // Format date string for display
    final dateStr =
        '${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}';

    // Create PDF document with metadata
    final pdf = pw.Document(
      title: 'تقرير ملخص الحالات',
      author: 'نظام إدارة المركبات',
      creator: 'تطبيق إدارة المركبات',
      subject: 'تقرير ملخص حالات المركبات',
      keywords: 'مركبات, تقرير, حالات, ملخص',
      producer: 'Cars App',
    );

    // Add pages to the document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicTtf,
          bold: arabicTtf,
        ),
        header: (context) => PdfHelpers.buildHeader(
            context, arabicTtf, 'تقرير ملخص حالات المركبات'),
        footer: (context) => PdfHelpers.buildFooter(context, arabicTtf),
        build: (context) => [
          // Title
          pw.Container(
            padding:
            const pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.primaryColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              border: pw.Border.all(color: PdfHelpers.primaryColor, width: 1),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  'تقرير ملخص حالات المركبات',
                  style: pw.TextStyle(
                    color: PdfHelpers.primaryColor,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: arabicTtf,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'الفترة: $dateStr',
                  style: pw.TextStyle(
                    color: PdfHelpers.textSecondaryColor,
                    fontSize: 14,
                    font: arabicTtf,
                  ),
                ),
                if (sectorName != null && sectorName.isNotEmpty)
                  pw.Text(
                    'القطاع: $sectorName',
                    style: pw.TextStyle(
                      color: PdfHelpers.textSecondaryColor,
                      fontSize: 14,
                      font: arabicTtf,
                    ),
                  ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Summary Section
          PdfHelpers.sectionHeader('ملخص الإحصائيات', arabicTtf),
          pw.SizedBox(height: 10),

          // Summary Cards
          pw.Container(
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.backgroundColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Column(
              children: [
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: PdfHelpers.infoCard(
                          'النشطة',
                          '${carsByStatus[CarStatus.active]?.length ?? 0}',
                          arabicTtf,
                          color: PdfHelpers.successColor),
                    ),
                    pw.SizedBox(width: 10),
                    pw.Expanded(
                      child: PdfHelpers.infoCard(
                          'السيارات العطلة',
                          '${carsByStatus[CarStatus.sentRequest]?.length ?? 0}',
                          arabicTtf,
                          color: PdfHelpers.warningColor),
                    ),
                    pw.SizedBox(width: 10),
                    pw.Expanded(
                      child: PdfHelpers.infoCard(
                          'في الورشة',
                          '${carsByStatus[CarStatus.pending]?.length ?? 0}',
                          arabicTtf,
                          color: PdfHelpers.infoColor),
                    ),
                  ],
                ),
                pw.SizedBox(height: 15),
                // Additional summary information
                pw.Container(
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfHelpers.backgroundColor,
                    border: pw.Border.all(color: PdfHelpers.borderColor),
                    borderRadius:
                    const pw.BorderRadius.all(pw.Radius.circular(4)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'إجمالي المركبات: ${(carsByStatus[CarStatus.active]?.length ?? 0) + (carsByStatus[CarStatus.sentRequest]?.length ?? 0) + (carsByStatus[CarStatus.pending]?.length ?? 0) + (carsByStatus[CarStatus.done]?.length ?? 0)}',
                        style: pw.TextStyle(
                          font: arabicTtf,
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'الفترة: ${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}',
                        style: pw.TextStyle(
                          font: arabicTtf,
                          fontSize: 12,
                        ),
                      ),
                      if (sectorName != null && sectorName.isNotEmpty)
                        pw.Text(
                          'القطاع: $sectorName',
                          style: pw.TextStyle(
                            font: arabicTtf,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // No detailed tables as per user request - only showing summary numbers

          // Status Legend
          pw.SizedBox(height: 10),
          _buildStatusLegend(arabicTtf),
        ],
      ),
    );

    return pdf.save();
  }

  /// Generate an advanced workshop report PDF showing all workshop entries for all cars
  Future<Uint8List> generateAdvancedWorkshopReport({
    required List<Car> cars,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    // Load and register the Noto Sans Arabic font
    final arabicTtf = await PdfHelpers.loadArabicFont();

    // Format date string for display
    final dateStr =
        '${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}';

    // Create PDF document with metadata
    final pdf = pw.Document(
      title: 'تقرير متقدم للورشة',
      author: 'نظام إدارة المركبات',
      creator: 'تطبيق إدارة المركبات',
      subject: 'تقرير تفصيلي لسجلات الورشة',
      keywords: 'ورشة, مركبات, تقرير, صيانة, تفاصيل',
      producer: 'Cars App',
    );

    // Add pages to the document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicTtf,
          bold: arabicTtf,
        ),
        header: (context) => PdfHelpers.buildHeader(
            context, arabicTtf, 'تقرير تفصيلي لسجلات الورشة'),
        footer: (context) => PdfHelpers.buildFooter(context, arabicTtf),
        build: (context) => [
          // Title
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.primaryColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              border: pw.Border.all(color: PdfHelpers.primaryColor, width: 1),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  'تقرير تفصيلي لسجلات الورشة',
                  style: pw.TextStyle(
                    color: PdfHelpers.primaryColor,
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: arabicTtf,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'الفترة: $dateStr',
                  style: pw.TextStyle(
                    color: PdfHelpers.textSecondaryColor,
                    fontSize: 14,
                    font: arabicTtf,
                  ),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Summary Section
          PdfHelpers.sectionHeader('ملخص الإحصائيات', arabicTtf),
          pw.SizedBox(height: 10),

          // Summary Cards
          pw.Container(
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.backgroundColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  child: PdfHelpers.infoCard(
                      'عدد المركبات', '${cars.length}', arabicTtf),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: PdfHelpers.infoCard(
                      'إجمالي سجلات الورشة',
                      '${cars.fold(0, (sum, car) => sum + car.workshopHistory.length)}',
                      arabicTtf),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Detailed Workshop Entries Section
          PdfHelpers.sectionHeader('تفاصيل سجلات الورشة', arabicTtf),
          pw.SizedBox(height: 10),

          // For each car, create a section with its workshop entries
          ...cars.where((car) => car.workshopHistory.isNotEmpty).map((car) => pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Car header
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      // color: PdfHelpers.primaryColor.withOpacity(0.1),
                      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                    ),
                    child: pw.Row(
                      children: [
                        pw.Text(
                          '${car.plateNumber}-${car.plateCharacters}',
                          style: pw.TextStyle(
                            color: PdfHelpers.primaryColor,
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                            font: arabicTtf,
                          ),
                        ),
                        pw.SizedBox(width: 10),
                        pw.Text(
                          car.carModel,
                          style: pw.TextStyle(
                            color: PdfHelpers.textSecondaryColor,
                            fontSize: 10,
                            font: arabicTtf,
                          ),
                        ),
                        pw.SizedBox(width: 10),
                        pw.Text(
                          car.sectorName,
                          style: pw.TextStyle(
                            color: PdfHelpers.textSecondaryColor,
                            fontSize: 10,
                            font: arabicTtf,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 5),

                  // Workshop entries table for this car
                  if (car.workshopHistory.isNotEmpty)
                    pw.TableHelper.fromTextArray(
                      context: context,
                      headerDecoration: const pw.BoxDecoration(
                        color: PdfHelpers.primaryColor,
                        borderRadius: pw.BorderRadius.only(
                          topLeft: pw.Radius.circular(3),
                          topRight: pw.Radius.circular(3),
                        ),
                      ),
                      headerHeight: 16,
                      cellHeight: 18,
                      cellPadding: const pw.EdgeInsets.all(1),
                      cellAlignments: {
                        0: pw.Alignment.center,
                        1: pw.Alignment.center,
                        2: pw.Alignment.center,
                        3: pw.Alignment.center,
                        4: pw.Alignment.center,
                        5: pw.Alignment.center,
                        6: pw.Alignment.center,
                        7: pw.Alignment.center,
                      },
                      headerStyle: pw.TextStyle(
                        color: PdfColors.white,
                        fontWeight: pw.FontWeight.bold,
                        font: arabicTtf,
                        fontSize: 7,
                      ),
                      cellStyle: pw.TextStyle(
                        font: arabicTtf,
                        color: PdfHelpers.textColor,
                        fontSize: 6,
                      ),
                      rowDecoration: const pw.BoxDecoration(
                        border: pw.Border(
                          bottom: pw.BorderSide(
                            color: PdfHelpers.borderColor,
                            width: 0.2,
                          ),
                        ),
                      ),
                      headers: [
                        'القطاع',
                        'اللوحة',
                        'الموديل',
                        'الحالات',
                        'تاريخ البدء',
                        'المدة الكلية (أيام)',
                        'مدة الصيانة (أيام)',
                        'الملاحظات',
                      ],
                      data: car.workshopHistory.map((entry) {
                        // Sort statuses by date
                        final sortedStatuses = entry.statuses.toList()
                          ..sort((a, b) => a.createAt.compareTo(b.createAt));
                        
                        // Calculate total duration
                        final startDate = sortedStatuses.first.createAt;
                        final endDate = sortedStatuses.last.createAt;
                        final duration = endDate.difference(startDate).inDays;

                        // Calculate maintenance duration (from creation to active status)
                        String maintenanceDuration = 'N/A';
                        final activeStatus = sortedStatuses.firstWhere(
                          (status) => status.status == CarStatus.active,
                          orElse: () => sortedStatuses.first,
                        );
                        if (activeStatus.status == CarStatus.active) {
                          final maintenanceDays = activeStatus.createAt.difference(startDate).inDays;
                          maintenanceDuration = maintenanceDays.toString();
                        }

                        // Create status progression text
                        final hasSentRequest = sortedStatuses.any((status) => status.status == CarStatus.sentRequest) ? '1' : '0';
                        final hasPending = sortedStatuses.any((status) => status.status == CarStatus.pending) ? '1' : '0';
                        final hasActive = sortedStatuses.any((status) => status.status == CarStatus.active) ? '1' : '0';
                        final statusProgression = 'طلب صيانة: $hasSentRequest | تحت الاجراء: $hasPending | نشط: $hasActive';

                        return [
                          car.sectorName,
                          '${car.plateNumber}-${car.plateCharacters}',
                          car.carModel,
                          statusProgression,
                          DateFormat('yyyy-MM-dd').format(startDate),
                          duration.toString(),
                          maintenanceDuration,
                          sortedStatuses.last.notes ?? '',
                        ];
                      }).toList(),
                    )
                  else
                    pw.Container(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'لا توجد سجلات ورشة لهذه المركبة',
                        style: pw.TextStyle(
                          color: PdfHelpers.textSecondaryColor,
                          fontSize: 8,
                          font: arabicTtf,
                          fontStyle: pw.FontStyle.italic,
                        ),
                      ),
                    ),
                  pw.SizedBox(height: 15),
                ],
              )),
        ],
      ),
    );

    return pdf.save();
  }

  /// Build the detailed records table
  pw.Widget _buildDetailedRecordsTable(pw.Context context,
      List<WorkshopReportEntry> entries, pw.Font arabicTtf) {
    // Sort entries by date (newest first)
    final sortedEntries = List<WorkshopReportEntry>.from(entries)
      ..sort((a, b) => b.status.createAt.compareTo(a.status.createAt));

    // Limit to 20 entries to prevent TooManyPagesException
    const maxEntries = 20;
    final limitedEntries = sortedEntries.take(maxEntries).toList();

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Show a message about data size if there are many entries
        if (entries.length > maxEntries)
          pw.Container(
            padding: const pw.EdgeInsets.only(bottom: 5),
            child: pw.Text(
              'تم تحديد ${entries.length} سجل. يتم عرض أول $maxEntries سجل فقط في هذا التقرير.',
              style: pw.TextStyle(
                color: PdfHelpers.warningColor,
                font: arabicTtf,
                fontSize: 7,
                fontStyle: pw.FontStyle.italic,
              ),
            ),
          ),

        // Create the table with limited entries
        pw.TableHelper.fromTextArray(
          context: context,
          headerDecoration: const pw.BoxDecoration(
            color: PdfHelpers.primaryColor,
            borderRadius: pw.BorderRadius.only(
              topLeft: pw.Radius.circular(3),
              topRight: pw.Radius.circular(3),
            ),
          ),
          headerHeight: 16,
          cellHeight: 18,
          cellPadding: const pw.EdgeInsets.all(1),
          cellAlignments: {
            0: pw.Alignment.center,
            1: pw.Alignment.center,
            2: pw.Alignment.center,
            3: pw.Alignment.center,
            4: pw.Alignment.center,
            5: pw.Alignment.center,
            6: pw.Alignment.center,
            7: pw.Alignment.center,
          },
          headerStyle: pw.TextStyle(
            color: PdfColors.white,
            fontWeight: pw.FontWeight.bold,
            font: arabicTtf,
            fontSize: 7,
          ),
          cellStyle: pw.TextStyle(
            font: arabicTtf,
            color: PdfHelpers.textColor,
            fontSize: 6,
          ),
          rowDecoration: const pw.BoxDecoration(
            border: pw.Border(
              bottom: pw.BorderSide(
                color: PdfHelpers.borderColor,
                width: 0.2,
              ),
            ),
          ),
          headers: [
            'رقم',
            'صيانة',
            'تحت الاجراء',
            'جاهزة',
            'تاريخ البدء',
            'المدة (أيام)',
            'الملاحظات',
          ],
          data: limitedEntries.map((entry) {
            // Find the active status date
            DateTime? activeDate;
            for (var status in entry.workshopEntry.statuses) {
              if (status.status == CarStatus.active) {
                activeDate = status.createAt;
                break;
              }
            }

            // Calculate duration from creation to active date
            final startDate = entry.workshopEntry.createAt;
            final endDate = activeDate ?? DateTime.now();
            final duration = endDate.difference(startDate).inDays;

            // Check status indicators from workshop status list
            final hasSentRequest = entry.workshopEntry.statuses.any((s) => s.status == CarStatus.sentRequest) ? '1' : '0';
            final hasPending = entry.workshopEntry.statuses.any((s) => s.status == CarStatus.pending) ? '1' : '0';
            final hasActive = entry.workshopEntry.statuses.any((s) => s.status == CarStatus.active) ? '1' : '0';

            // Get notes
            String notes = entry.status.notes ?? '';
            if (notes.length > 15) {
              notes = '${notes.substring(0, 15)}...';
            }

            return [
              '${entry.car.plateNumber}-${entry.car.plateCharacters} - ${entry.car.sectorName}',
              hasSentRequest,
              hasPending,
              hasActive,
              DateFormat('yyyy-MM-dd').format(startDate),
              duration.toString(),
              notes,
            ];
          }).toList(),
        ),

        // Add a note about exporting to Excel for full data
        if (entries.length > maxEntries)
          pw.Container(
            padding: const pw.EdgeInsets.only(top: 5),
            child: pw.Text(
              'للحصول على جميع البيانات، يرجى تصدير التقرير بتنسيق Excel.',
              style: pw.TextStyle(
                color: PdfHelpers.infoColor,
                font: arabicTtf,
                fontSize: 7,
                fontStyle: pw.FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  /// Build sector distribution table
  pw.Widget _buildSectorDistributionTable(
      pw.Context context,
      List<SectorCount> sectorDistribution,
      pw.Font arabicTtf) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
        border: pw.Border.all(color: PdfHelpers.borderColor, width: 0.5),
      ),
      padding: const pw.EdgeInsets.all(5),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'توزيع القطاعات',
            style: pw.TextStyle(
              color: PdfHelpers.primaryColor,
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.TableHelper.fromTextArray(
            context: context,
            headerDecoration: const pw.BoxDecoration(
              color: PdfHelpers.primaryColor,
            ),
            headerHeight: 16,
            cellHeight: 14,
            cellPadding: const pw.EdgeInsets.all(1),
            headerStyle: pw.TextStyle(
              color: PdfColors.white,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
              fontSize: 7,
            ),
            cellStyle: pw.TextStyle(
              font: arabicTtf,
              fontSize: 6,
            ),
            cellAlignment: pw.Alignment.centerRight,
            headers: ['القطاع', 'العدد'],
            data: sectorDistribution
                .take(5) // Limit to top 5 sectors
                .map((e) => [
              e.sectorName,
              e.count.toString(),
            ])
                .toList(),
          ),
        ],
      ),
    );
  }

  /// Build status legend
  pw.Widget _buildStatusLegend(pw.Font arabicTtf) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      decoration: pw.BoxDecoration(
        color: PdfHelpers.backgroundColor,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
        border: pw.Border.all(color: PdfHelpers.borderColor, width: 0.3),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'دليل الحالات',
            style: pw.TextStyle(
              color: PdfHelpers.textColor,
              fontSize: 9,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              PdfHelpers.buildStatusLegendItem(
                  arabicTtf, 'نشط', PdfHelpers.successColor),
              PdfHelpers.buildStatusLegendItem(
                  arabicTtf, 'صيانة', PdfHelpers.warningColor),
              PdfHelpers.buildStatusLegendItem(
                  arabicTtf, 'معطل', PdfHelpers.errorColor),
              PdfHelpers.buildStatusLegendItem(
                  arabicTtf, 'قيد الانتظار', PdfHelpers.infoColor),
              PdfHelpers.buildStatusLegendItem(
                  arabicTtf, 'أخرى', PdfHelpers.accentColor),
            ],
          ),
        ],
      ),
    );
  }

  /// Build car details section
  pw.Widget _buildCarDetailsSection(Car car, pw.Font arabicTtf) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      decoration: pw.BoxDecoration(
        color: PdfHelpers.primaryColor,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
        border: pw.Border.all(color: PdfHelpers.primaryColor, width: 0.5),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'تقرير المركبة',
            style: pw.TextStyle(
              color: PdfHelpers.primaryColor,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            '${car.plateNumber}-${car.plateCharacters}',
            style: pw.TextStyle(
              color: PdfHelpers.textColor,
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
            ),
          ),
          pw.SizedBox(height: 2),
          pw.Text(
            car.carModel,
            style: pw.TextStyle(
              color: PdfHelpers.textSecondaryColor,
              fontSize: 12,
              font: arabicTtf,
            ),
          ),
        ],
      ),
    );
  }

  /// Build car info grid
  pw.Widget _buildCarInfoGrid(Car car, pw.Font arabicTtf) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      decoration: const pw.BoxDecoration(
        color: PdfHelpers.backgroundColor,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(4)),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            children: [
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow('القطاع', car.sectorName,
                      arabicTtf, PdfHelpers.textColor)),
              pw.SizedBox(width: 5),
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow(
                      'النوع', car.carType, arabicTtf, PdfHelpers.textColor)),
            ],
          ),
          pw.Divider(color: PdfHelpers.borderColor, height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow(
                      'الحالة',
                      car.status.toString().split('.').last,
                      arabicTtf,
                      PdfHelpers.textColor)),
              pw.SizedBox(width: 5),
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow(
                      'تاريخ الإنشاء',
                      DateFormat('yyyy-MM-dd').format(car.createAt),
                      arabicTtf,
                      PdfHelpers.textColor)),
            ],
          ),
          pw.Divider(color: PdfHelpers.borderColor, height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow(
                      'عدد سجلات الورشة',
                      car.workshopHistory.length.toString(),
                      arabicTtf,
                      PdfHelpers.textColor)),
              pw.SizedBox(width: 5),
              pw.Expanded(
                  child: PdfHelpers.compactInfoRow(
                      'داخلي/خارجي',
                      car.isInternal ? 'داخلي' : 'خارجي',
                      arabicTtf,
                      PdfHelpers.textColor)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build status distribution table
  pw.Widget _buildStatusDistributionTable(pw.Context context,
      List<StatusCount> statusDistribution, pw.Font arabicTtf) {
    return pw.TableHelper.fromTextArray(
      context: context,
      headerDecoration: const pw.BoxDecoration(
        color: PdfHelpers.primaryColor,
      ),
      headerHeight: 20,
      cellHeight: 20,
      cellPadding: const pw.EdgeInsets.all(2),
      headerStyle: pw.TextStyle(
        color: PdfHelpers.textColor,
        fontWeight: pw.FontWeight.bold,
        font: arabicTtf,
        fontSize: 9,
      ),
      cellStyle: pw.TextStyle(
        font: arabicTtf,
        fontSize: 8,
      ),
      cellAlignment: pw.Alignment.centerRight,
      headers: ['الحالة', 'العدد', 'النسبة'],
      data: PdfHelpers.generateStatusDistributionData(statusDistribution),
    );
  }

  /// Build workshop entries table
  pw.Widget _buildWorkshopEntriesTable(pw.Context context,
      List<WorkshopReportEntry> entries, pw.Font arabicTtf) {
    // Limit to 10 entries to prevent TooManyPagesException
    const maxEntries = 100;
    final limitedEntries = entries.take(maxEntries).toList();

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Show a message about data size if there are many entries
        if (entries.length > maxEntries)
          pw.Container(
            padding: const pw.EdgeInsets.only(bottom: 5),
            child: pw.Text(
              'تم تحديد ${entries.length} سجل. يتم عرض أول $maxEntries سجل فقط في هذا التقرير.',
              style: pw.TextStyle(
                color: PdfHelpers.warningColor,
                font: arabicTtf,
                fontSize: 7,
                fontStyle: pw.FontStyle.italic,
              ),
            ),
          ),

        // Create the table with limited entries and more compact design
        pw.TableHelper.fromTextArray(
          context: context,
          headerDecoration: const pw.BoxDecoration(
            color: PdfHelpers.primaryColor,
            borderRadius: pw.BorderRadius.only(
              topLeft: pw.Radius.circular(3),
              topRight: pw.Radius.circular(3),
            ),
          ),
          headerHeight: 16,
          cellHeight: 18,
          cellPadding: const pw.EdgeInsets.all(1),
          cellAlignments: {
            0: pw.Alignment.center,
            1: pw.Alignment.centerRight,
            2: pw.Alignment.center,
            3: pw.Alignment.centerRight,
            4: pw.Alignment.centerRight,
          },
          headerStyle: pw.TextStyle(
            color: PdfColors.white,
            fontWeight: pw.FontWeight.bold,
            font: arabicTtf,
            fontSize: 7,
          ),
          cellStyle: pw.TextStyle(
            font: arabicTtf,
            color: PdfHelpers.textColor,
            fontSize: 6,
          ),
          rowDecoration: const pw.BoxDecoration(
            border: pw.Border(
              bottom: pw.BorderSide(
                color: PdfHelpers.borderColor,
                width: 0.2,
              ),
            ),
          ),
          headers: [
            'رقم',
            'صيانة',
            'طلب صيانة',
            'تحت الاجراء',
            'نشط',
            'تاريخ البدء',
            'المدة (أيام)',
            'الملاحظات',
          ],
          data: limitedEntries.map((entry) {
            String notes = entry.status.notes ?? '';
            if (notes.length > 15) {
              notes = '${notes.substring(0, 15)}...';
            }

            return [
              '${entry.car.plateNumber}-${entry.car.plateCharacters} - ${entry.car.sectorName}',
              entry.status.status.toString().split('.').last,
              DateFormat('yyyy-MM-dd')
                  .format(entry.status.createAt), // Remove time to save space
              notes,
            ];
          }).toList(),
        ),

        // Add a note about exporting to Excel for full data
        if (entries.length > maxEntries)
          pw.Container(
            padding: const pw.EdgeInsets.only(top: 5),
            child: pw.Text(
              'للحصول على جميع البيانات، يرجى تصدير التقرير بتنسيق Excel.',
              style: pw.TextStyle(
                color: PdfHelpers.infoColor,
                font: arabicTtf,
                fontSize: 7,
                fontStyle: pw.FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  /// Generate a daily report PDF showing cars' status for the last 24 hours
  Future<Uint8List> generateDailyReport({
    required List<Car> cars,
  }) async {
    // Load and register the Noto Sans Arabic font
    final arabicTtf = await PdfHelpers.loadArabicFont();

    // Get today's date range (00:00 to now)
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = now;

    // Filter cars that had status changes in the last 24 hours
    final relevantCars = cars.where((car) {
      final lastStatusChange = car.createAt;
      return lastStatusChange.isAfter(startOfDay) && lastStatusChange.isBefore(endOfDay);
    }).toList();

    // Create PDF document with metadata
    final pdf = pw.Document(
      title: 'تقرير يومي',
      author: 'نظام إدارة المركبات',
      creator: 'تطبيق إدارة المركبات',
      subject: 'تقرير حالة المركبات اليومي',
      keywords: 'مركبات, تقرير, يومي, حالات',
      producer: 'Cars App',
    );

    // Add pages to the document
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: arabicTtf,
          bold: arabicTtf,
        ),
        header: (context) => PdfHelpers.buildHeader(
            context, arabicTtf, 'تقرير حالة المركبات اليومي'),
        footer: (context) => PdfHelpers.buildFooter(context, arabicTtf),
        build: (context) => [
          // Title with icon
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.primaryColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              border: pw.Border.all(color: PdfHelpers.primaryColor, width: 1),
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Icon(
                  pw.IconData(0xf1b2), // Car icon
                  color: PdfColors.white,
                  size: 24,
                ),
                pw.SizedBox(width: 10),
                pw.Column(
                  children: [
                    pw.Text(
                      'تقرير حالة المركبات اليومي',
                      style: pw.TextStyle(
                        color: PdfHelpers.primaryColor,
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        font: arabicTtf,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'الفترة: ${DateFormat('yyyy-MM-dd').format(startOfDay)} - ${DateFormat('yyyy-MM-dd HH:mm').format(endOfDay)}',
                      style: pw.TextStyle(
                        color: PdfHelpers.textSecondaryColor,
                        fontSize: 14,
                        font: arabicTtf,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Summary Section with icons
          PdfHelpers.sectionHeader('ملخص الإحصائيات', arabicTtf),
          pw.SizedBox(height: 10),

          // Summary Cards with icons
          pw.Container(
            padding: const pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              color: PdfHelpers.backgroundColor,
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
            ),
            child: pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.white,
                      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                      border: pw.Border.all(color: PdfHelpers.borderColor),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Icon(
                          pw.IconData(0xf1b2), // Car icon
                          color: PdfHelpers.primaryColor,
                          size: 20,
                        ),
                        pw.SizedBox(height: 5),
                        PdfHelpers.infoCard(
                            'إجمالي المركبات', '${cars.length}', arabicTtf),
                      ],
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(10),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.white,
                      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                      border: pw.Border.all(color: PdfHelpers.borderColor),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Icon(
                          pw.IconData(0xf0e7), // Update icon
                          color: PdfHelpers.successColor,
                          size: 20,
                        ),
                        pw.SizedBox(height: 5),
                        PdfHelpers.infoCard(
                            'تم تحديث الحالة',
                            '${relevantCars.length}',
                            arabicTtf),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          pw.SizedBox(height: 20),

          // Group cars by sector
          ..._groupCarsBySector(relevantCars).entries.map((sectorEntry) {
            final sectorName = sectorEntry.key;
            final sectorCars = sectorEntry.value;

            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Sector Header
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  decoration: pw.BoxDecoration(
                    // color: PdfHelpers.primaryColor.withOpacity(0.1),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                  ),
                  child: pw.Row(
                    children: [
                      pw.Icon(
                        pw.IconData(0xf1b2), // Car icon
                        color: PdfHelpers.primaryColor,
                        size: 16,
                      ),
                      pw.SizedBox(width: 8),
                      pw.Text(
                        sectorName,
                        style: pw.TextStyle(
                          color: PdfHelpers.primaryColor,
                          fontSize: 14,
                          fontWeight: pw.FontWeight.bold,
                          font: arabicTtf,
                        ),
                      ),
                      pw.SizedBox(width: 8),
                      pw.Text(
                        '(${sectorCars.length})',
                        style: pw.TextStyle(
                          color: PdfHelpers.textSecondaryColor,
                          fontSize: 12,
                          font: arabicTtf,
                        ),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 10),

                // Sector Cars Table
                pw.TableHelper.fromTextArray(
                  context: context,
                  headerDecoration: const pw.BoxDecoration(
                    color: PdfHelpers.primaryColor,
                    borderRadius: pw.BorderRadius.only(
                      topLeft: pw.Radius.circular(3),
                      topRight: pw.Radius.circular(3),
                    ),
                  ),
                  headerHeight: 16,
                  cellHeight: 18,
                  cellPadding: const pw.EdgeInsets.all(1),
                  cellAlignments: {
                    0: pw.Alignment.center,
                    1: pw.Alignment.center,
                    2: pw.Alignment.center,
                  },
                  headerStyle: pw.TextStyle(
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                    font: arabicTtf,
                    fontSize: 7,
                  ),
                  cellStyle: pw.TextStyle(
                    font: arabicTtf,
                    color: PdfHelpers.textColor,
                    fontSize: 6,
                  ),
                  rowDecoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(
                        color: PdfHelpers.borderColor,
                        width: 0.2,
                      ),
                    ),
                  ),
                  headers: [
                    'اللوحة',
                    'الحالة',
                    'آخر تحديث',
                  ],
                  data: sectorCars.map((car) {
                    // Get status text and icon
                    String statusText = car.status.name.tr;
                    int statusIcon = 0;
                    PdfColor statusColor = PdfColors.black;

                    switch (car.status) {
                      case CarStatus.sentRequest:
                        statusIcon = 0xf0e7; // Update icon
                        statusColor = PdfHelpers.warningColor;
                        break;
                      case CarStatus.pending:
                        statusIcon = 0xf0c3; // Wrench icon
                        statusColor = PdfHelpers.infoColor;
                        break;
                      case CarStatus.active:
                        statusIcon = 0xf00c; // Check icon
                        statusColor = PdfHelpers.successColor;
                        break;
                      case CarStatus.done:
                        statusIcon = 0xf00c; // Check icon
                        statusColor = PdfHelpers.successColor;
                        break;
                      default:
                        statusIcon = 0xf128; // Question icon
                        statusColor = PdfHelpers.textSecondaryColor;
                    }

                    return [
                      '${car.plateNumber}-${car.plateCharacters}',
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Icon(
                            pw.IconData(statusIcon),
                            color: statusColor,
                            size: 8,
                          ),
                          pw.SizedBox(width: 2),
                          pw.Text(statusText),
                        ],
                      ),
                      DateFormat('yyyy-MM-dd HH:mm').format(car.createAt),
                    ];
                  }).toList(),
                ),
                pw.SizedBox(height: 20),
              ],
            );
          }).toList(),

          // Status Legend with icons
          pw.SizedBox(height: 10),
          _buildStatusLegend(arabicTtf),
        ],
      ),
    );

    return pdf.save();
  }

  /// Group cars by sector
  Map<String, List<Car>> _groupCarsBySector(List<Car> cars) {
    final Map<String, List<Car>> groupedCars = {};
    
    for (var car in cars) {
      if (!groupedCars.containsKey(car.sectorName)) {
        groupedCars[car.sectorName] = [];
      }
      groupedCars[car.sectorName]!.add(car);
    }

    // Sort sectors alphabetically
    final sortedKeys = groupedCars.keys.toList()..sort();
    return Map.fromEntries(
      sortedKeys.map((key) => MapEntry(key, groupedCars[key]!)),
    );
  }
}

/// Export format enum
enum ExportFormat {
  pdf,
}
