import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

enum RotateType {
  temporary,
  permanent
}
enum RotateStatus {
  pending,
  accepted,
  rejected
}
enum RotateAction{
  support,
  restore,
  none
}
class CarRotate {
  final String id;
  final String carId;
  final String fromSectorId;
  final String toSectorId;
  final RotateAction action;
  final String fromSectorName;
  final String toSectorName;
  final RotateType type;
  final String cause;
  final RotateStatus isFromSectorMangerAgree;
  final RotateStatus isToSectorMangerAgree;
  final DateTime? agreedByFromSectorManagerAt;
  final DateTime? agreedByToSectorManagerAt;
  final RotateStatus managerAgreeStatus;

  CarRotate({
    required this.id,
    required this.carId,
    required this.type,
    required this.cause,
    required this.action,
    required this.managerAgreeStatus,
    required this.fromSectorId,
    required this.toSectorId,
    required this.fromSectorName,
    required this.toSectorName,
    required this.isFromSectorMangerAgree,
    required this.isToSectorMangerAgree,
    this.agreedByFromSectorManagerAt,
    this.agreedByToSectorManagerAt,
  });

  factory CarRotate.fromJson(Map<String, dynamic> json) {
    // Parse timestamp to DateTime
    DateTime? parseTimestamp(dynamic value) {
      if (value == null) return null;
      if (value is Timestamp) return value.toDate();
      if (value is Map && value['seconds'] != null) {
        return DateTime.fromMillisecondsSinceEpoch(value['seconds'] * 1000);
      }
      try {
        return DateTime.parse(value.toString());
      } catch (e) {
        print('Error parsing timestamp: $e');
        return null;
      }
    }

    return CarRotate(
      id: json['id'] as String? ?? '',
      type: RotateType.values.firstWhere((e) => e.toString().split('.').last.toLowerCase() == json['type'].toString().split('.').last.toLowerCase()),
      carId: json['carId'] as String? ?? '',
      cause: json['cause'] as String? ?? '',
      managerAgreeStatus: RotateStatus.values.firstWhere((e) => e.toString().split('.').last.toLowerCase() == json['managerAgreeStatus'].toString().split('.').last.toLowerCase()),
      fromSectorId: json['fromSectorId'] as String? ?? '',
      toSectorId: json['toSectorId'] as String? ?? '',
      fromSectorName: json['fromSectorName'] as String? ?? '',
      toSectorName: json['toSectorName'] as String? ?? '',
      action: RotateAction.values.firstWhere((e) => e.toString().split('.').last.toLowerCase() == json['action'].toString().split('.').last.toLowerCase()),
      isFromSectorMangerAgree:RotateStatus.values.firstWhere((e) => e.toString().split('.').last.toLowerCase() == json['isFromSectorMangerAgree'].toString().split('.').last.toLowerCase()),
      isToSectorMangerAgree:RotateStatus.values.firstWhere((e) => e.toString().split('.').last.toLowerCase() == json['isToSectorMangerAgree'].toString().split('.').last.toLowerCase()),
      agreedByFromSectorManagerAt: parseTimestamp(json['agreedByFromSectorManagerAt']),
      agreedByToSectorManagerAt: parseTimestamp(json['agreedByToSectorManagerAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'carId': carId,
      'managerAgreeStatus': managerAgreeStatus.toString().split('.').last,
      'cause': cause,
      'fromSectorId': fromSectorId,
      'type': type == RotateType.temporary ? 'temporary' : 'permanent',
      'toSectorId': toSectorId,
      'action': action.toString().split('.').last,
      'fromSectorName': fromSectorName,
      'toSectorName': toSectorName,
      'isFromSectorMangerAgree': isFromSectorMangerAgree.toString().split('.').last,
      'isToSectorMangerAgree': isToSectorMangerAgree.toString().split('.').last,
      'agreedByFromSectorManagerAt': agreedByFromSectorManagerAt != null ? 
        Timestamp.fromDate(agreedByFromSectorManagerAt!) : null,
      'agreedByToSectorManagerAt': agreedByToSectorManagerAt != null ? 
        Timestamp.fromDate(agreedByToSectorManagerAt!) : null,
    };
  }

  CarRotate copyWith({
    String? id,
    String? carId,
    String? fromSectorId,
    String? toSectorId,
    bool? isEnabled,
    String? fromSectorName,
    String? toSectorName,
    RotateType? type,
    RotateAction? action,
    RotateStatus? isFromSectorMangerAgree,
    RotateStatus? isToSectorMangerAgree,
    DateTime? agreedByFromSectorManagerAt,
    DateTime? agreedByToSectorManagerAt,
  }) {
    return CarRotate(
      id: id ?? this.id,
      carId: carId ?? this.carId,
      fromSectorId: fromSectorId ?? this.fromSectorId,
      toSectorId: toSectorId ?? this.toSectorId,
      fromSectorName: fromSectorName ?? this.fromSectorName,
      toSectorName: toSectorName ?? this.toSectorName,
      type: type ?? this.type,
      cause: cause ?? this.cause,
      action: action ?? this.action,
      managerAgreeStatus: managerAgreeStatus ?? this.managerAgreeStatus,
      isFromSectorMangerAgree: isFromSectorMangerAgree ?? this.isFromSectorMangerAgree,
      isToSectorMangerAgree: isToSectorMangerAgree ?? this.isToSectorMangerAgree,
      agreedByFromSectorManagerAt: agreedByFromSectorManagerAt ?? this.agreedByFromSectorManagerAt,
      agreedByToSectorManagerAt: agreedByToSectorManagerAt ?? this.agreedByToSectorManagerAt,
    );
  }
}