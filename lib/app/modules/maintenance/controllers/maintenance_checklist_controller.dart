import 'package:get/get.dart';
import '../../../data/models/maintenance_types.dart';

class MaintenanceChecklistController extends GetxController {
  final RxList<int> selectedItems = <int>[].obs;
  final RxBool isLoading = false.obs;

  // For tracking check vs repair
  final RxMap<int, String> itemStatus = <int, String>{}.obs;

  @override
  void onInit() {
    super.onInit();
  }

  void toggleItem(int id) {
    if (selectedItems.contains(id)) {
      selectedItems.remove(id);
      itemStatus.remove(id);
    } else {
      selectedItems.add(id);
      itemStatus[id] = 'فحص'; // Default to inspection (فحص)
    }
  }

  void setItemStatus(int id, String status) {
    // Always ensure the item is in the selected list
    if (!selectedItems.contains(id)) {
      selectedItems.add(id);
    }

    // If the current status is the same as the new status, do nothing
    // This prevents toggling the same status off
    if (itemStatus[id] == status) {
      return;
    }

    // Set the new status
    itemStatus[id] = status;
  }

  String? getItemStatus(int id) {
    return itemStatus[id];
  }

  bool isItemSelected(int id) {
    return selectedItems.contains(id);
  }

  List<MaintenanceType> getSelectedMaintenanceTypes() {
    return selectedItems
        .map((id) => getMaintenanceTypeById(id))
        .whereType<MaintenanceType>()
        .toList();
  }

  void clearSelection() {
    selectedItems.clear();
    itemStatus.clear();
  }

  void selectAll() {
    selectedItems.clear();
    itemStatus.clear();

    for (var item in standardMaintenanceTypes) {
      selectedItems.add(item.id);
      itemStatus[item.id] = 'فحص'; // Default to inspection (فحص)
    }
  }

  // Load saved checklist
  void loadChecklist(List<Map<String, dynamic>> savedChecklist) {
    clearSelection();

    for (var item in savedChecklist) {
      final id = item['id'] as int;
      final status = item['status'] as String?;

      if (id > 0) {
        selectedItems.add(id);
        if (status != null) {
          itemStatus[id] = status;
        } else {
          itemStatus[id] = 'فحص'; // Default to inspection (فحص)
        }
      }
    }
  }

  // Export checklist for saving
  List<Map<String, dynamic>> exportChecklist() {
    return selectedItems.map((id) {
      return {
        'id': id,
        'name': getMaintenanceTypeById(id)?.name ?? '',
        'status': itemStatus[id] ?? 'فحص', // Default to inspection (فحص)
      };
    }).toList();
  }
}
