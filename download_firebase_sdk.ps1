$url = "https://firebase.google.com/download/cpp"
$outputPath = "firebase_cpp_sdk_windows.zip"
$extractPath = "build/windows/x64/extracted"

# Create the extraction directory if it doesn't exist
New-Item -ItemType Directory -Force -Path $extractPath

# Download the Firebase C++ SDK
Invoke-WebRequest -Uri $url -OutFile $outputPath

# Extract the ZIP file
Expand-Archive -Path $outputPath -DestinationPath $extractPath -Force

# Clean up the ZIP file
Remove-Item $outputPath
