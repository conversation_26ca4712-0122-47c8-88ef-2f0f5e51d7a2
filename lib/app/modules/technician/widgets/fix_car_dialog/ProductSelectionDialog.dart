import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' show max;
import '../../../../themes/app_colors.dart';
import '../../../inventory/controllers/inventory_controller.dart';
import '../../../inventory/models/product_model.dart';

import 'product_selection_item.dart'; // Import the item widget

/// Dialog for selecting products to add to the maintenance list
class ProductSelectionDialog extends StatelessWidget {
    final InventoryController inventoryController;
    final RxList<Map<String, dynamic>> selectedProducts; // The list to modify

    // Internal state for search and filtering
    final TextEditingController _searchController = TextEditingController();
    final RxString _searchQuery = ''.obs;
    final Rx<List<ProductModel>> _filteredProducts = Rx<List<ProductModel>>([]);

    ProductSelectionDialog({
        Key? key,
                required this.inventoryController,
                required this.selectedProducts, // Receive the list
    }) : super(key: key) {
        // Initialize filtered products (only available ones) and set up search listener
        _updateFilteredProducts(_searchQuery.value);
        _searchController.addListener(_onSearchChanged);
    }

    void _onSearchChanged() {
        _searchQuery.value = _searchController.text;
        _updateFilteredProducts(_searchQuery.value);
    }

    void _updateFilteredProducts(String query) {
        // Filter by current quantity > 0 and then by search query
        final allAvailableProducts = inventoryController.products
                .where((product) => product.currentQuantity > 0)
        .toList();

        if (query.isEmpty) {
            _filteredProducts.value = allAvailableProducts;
        } else {
            _filteredProducts.value = allAvailableProducts
                    .where((product) =>
                    product.name.toLowerCase().contains(query.toLowerCase()) ||
                    (product.sku?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();
        }
    }

    // Helper methods for responsive dimensions (can be duplicated or put in shared helpers)
    double _getMaxDialogWidth(BuildContext context) {
        final double screenWidth = MediaQuery.of(context).size.width;
        if (screenWidth > 600) {
            return 500.w; // Slightly smaller max width for the selection dialog
        } else {
            return screenWidth * 0.95;
        }
    }

    double _getHorizontalPadding(BuildContext context) {
        final double screenWidth = MediaQuery.of(context).size.width;
        if (screenWidth > 600) {
            final double dialogWidth = _getMaxDialogWidth(context);
            // Ensure padding is never negative
            return max((screenWidth - dialogWidth) / 2, 0);
        } else {
            return 20.w;
        }
    }


    /// Builds the product selection dialog header
    Widget _buildHeader(BuildContext context) {
        return Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
        ),
      ),
        child: Row(
                children: [
        Icon(Icons.inventory_2, color: Colors.white, size: 24.r),
        SizedBox(width: 12.w),
        Text(
                'select_products'.tr,
                style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
                IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
        onPressed: () => Get.back(),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
    }

    /// Builds the search bar for product selection
    Widget _buildSearchBar(BuildContext context) {
        return Padding(
                padding: EdgeInsets.all(16.r),
                child: Material(
                  color: Colors.transparent,
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'search_products'.tr,
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                        borderSide: const BorderSide(color: AppColors.primary),
                      ),
                    ),
                  ),
                ),
              );
    }

    /// Builds the product selection list
    Widget _buildProductList(BuildContext context) {
        return Flexible(
                child: Obx(() { // Observe changes in _filteredProducts and selectedProducts
            if (_filteredProducts.value.isEmpty) {
                // If the search query is not empty but results are empty, show "no products found"
                if (_searchQuery.isNotEmpty) {
                    return Center(
                            child: Padding(
                            padding: EdgeInsets.all(24.r),
                            child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                    Lottie.asset( // Use .asset if you download it, or .network
                            'assets/lottie/no_results_found.json', // Example asset path
                            // 'https://assets5.lottiefiles.com/packages/lf20_ydo1amjm.json', // Original network URL
                            width: 120.w,
                            height: 120.h,
                       ),
                    SizedBox(height: 16.h),
                    Text(
                            'no_products_found'.tr,
                            style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.textSecondary,
                         ),
                    textAlign: TextAlign.center,
                       ),
                     ],
                   ),
                 ),
               );
                }
                // If search query is empty and filtered list is empty, means no available products
                return Center(
                        child: Padding(
                        padding: EdgeInsets.all(24.r),
                        child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                Icon( // Use an icon if no Lottie or if you prefer
                        Icons.inventory_2_outlined,
                        size: 60.r,
                        color: AppColors.textSecondary,
                   ),
                SizedBox(height: 16.h),
                Text(
                        'no_available_products'.tr, // New translation key needed
                        style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textSecondary,
                      ),
                textAlign: TextAlign.center,
                    ),
                 ],
               ),
             ),
           );
            }

            // Use ListView.builder for potentially long lists, but since it's in a dialog
            // and likely shorter, ListView.separated is fine too.
            return ListView.separated(
                    padding: EdgeInsets.symmetric(horizontal: 16.r),
            itemCount: _filteredProducts.value.length,
                    separatorBuilder: (_, __) => const Divider(height: 1),
            itemBuilder: (context, index) {
                final product = _filteredProducts.value[index];

                // Check if product is already selected using its ID
                final bool isSelected = selectedProducts.any(
                        (item) => (item['product'] as ProductModel).id == product.id);

                // Use the dedicated widget for list items
                return ProductSelectionItem(
                        product: product,
                        isSelected: isSelected,
                        onTap: () {
                    // Modify the selectedProducts list from the main dialog
                    if (isSelected) {
                        selectedProducts.removeWhere((item) =>
                                (item['product'] as ProductModel).id == product.id);
                    } else {
                        // Add product with initial quantity of 1 (as RxInt)
                        selectedProducts.add({
                                'product': product,
                                'quantity': 1.obs, // Use RxInt here
                    });
                    }
                    // No need to manually refresh the list here,
                    // the Obx in ProductsSection (main dialog) listens to selectedProducts.
                    // However, the selection dialog list itself also needs to update
                    // which items are marked as selected. The `isSelected` check inside
                    // the item builder happens implicitly when the parent Obx rebuilds
                    // due to _filteredProducts changing (which happens on search)
                    // or if we wrap the specific item's selection logic in Obx.
                    // Let's make the item widget react to its selected state.
                },
            );
            },
        );
        }),
    );
    }

    /// Builds the product selection action buttons
    Widget _buildActions(BuildContext context) {
        return Container(
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
        ),
        boxShadow: [
        BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
          ),
        ],
      ),
        child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
        Obx(() => Text( // Observe selectedProducts length
                '${selectedProducts.length} ${'items_selected'.tr}',
                style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14.sp,
            ),
          )),
          const Spacer(),
                TextButton(
                        onPressed: () => Get.back(), // Close this selection dialog
                child: Text(
                'cancel'.tr,
                style: const TextStyle(color: AppColors.textSecondary),
            ),
          ),
        SizedBox(width: 16.w),
        ElevatedButton(
                onPressed: () => Get.back(), // Close this selection dialog
                style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
        shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r)),
            ),
        child: Text('done'.tr),
          ),
        ],
      ),
    );
    }

    @override
    Widget build(BuildContext context) {
        final double maxWidth = _getMaxDialogWidth(context);
        final double maxHeight = MediaQuery.of(context).size.height * 0.85;
        final double horizontalPadding = _getHorizontalPadding(context);

        return Dialog(
                insetPadding: EdgeInsets.symmetric(
                horizontal: max(horizontalPadding, 0), // Ensure non-negative padding
                vertical: max(24.h, 0), // Ensure non-negative padding
       ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
        child: Container(
                width: maxWidth, // Apply max width
                constraints: BoxConstraints(
                maxHeight: maxHeight,
                maxWidth: maxWidth, // Also apply max width here
         ),
        decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
        BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
             ),
           ],
         ),
        child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
        // Header
        _buildHeader(context),

                // Search bar
                _buildSearchBar(context),

                // Products list
                _buildProductList(context),

                // Actions
                _buildActions(context),
           ],
         ),
       ),
     );
    }

    @override
    void dispose() {
        _searchController.removeListener(_onSearchChanged);
        _searchController.dispose();
        _searchQuery.close(); // Dispose RxString
        _filteredProducts.close(); // Dispose RxList
        // No need to dispose selectedProducts here, it's owned by the parent dialog
    }
}