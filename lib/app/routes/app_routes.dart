part of 'app_pages.dart';

abstract class Routes {
  Routes._();
  static const HOME = '/home';
  static const SPLASH = '/splash';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const CARS = '/cars';
  static const CarRotationDashboard = '/car-rotation-dashboard';
  static const CHAT = '/chat';
  static const SECTOR_MANAGER = '/sector-manager';
  static const SECTORS = '/sectors';
  static const USERS = '/users';
  static const SETTINGS = '/settings';
  static const VEHICLE_INSPECTION = '/vehicle-inspection';
  static const LOGASTIC_SUPPORT = '/logastic-support';
  static const SUPERVISOR = '/supervisor';
  static const CAR_ROTATION = '/car-rotation';
  static const NOTIFICATIONS = '/notifications';
  static const WORKSHOP_FORM = '/workshop-form';
  static const TECHNICIAN = '/technician';
  static const PROFILE = '/profile';
  static const REPORTS = '/reports';
  static const MODERN_DASHBOARD = '/modern-dashboard';

  static const inventoryList = '/inventory';
  static const addEditProduct = '/inventory/add_edit_product';
  static const transaction = '/inventory/transaction';
  static const transactionHistory = '/inventory/transaction_history';

  // Problem tags management
  static const PROBLEM_TAGS_MANAGEMENT = '/problem-tags-management';
  static const PROBLEM_STATISTICS = '/problem-statistics';
  static const PROBLEM_REPORTS = '/problem-reports';

  // Car report
  static const CAR_REPORT = '/car-report';

  // Maintenance checklist
  static const MAINTENANCE_CHECKLIST = '/maintenance-checklist';
}
