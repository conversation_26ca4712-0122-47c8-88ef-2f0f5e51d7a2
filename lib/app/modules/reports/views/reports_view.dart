import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../../data/models/car_model.dart';
import '../../../routes/app_pages.dart';
import '../../../themes/app_colors.dart';
import '../controllers/reports_controller.dart';
import '../widgets/filters/report_filters.dart';
import '../widgets/tabs/dashboard_tab.dart';
import '../widgets/tabs/records_tab.dart';

class ReportsView extends GetView<ReportsController> {
  const ReportsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: DefaultTabController(
        length: 2,
        child: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                title: Text(
                  'التقارير'.tr,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                centerTitle: true,
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                pinned: true,
                floating: true,
                snap: true,
                actions: [
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () => _showFiltersDialog(context),
                    tooltip: 'تصفية'.tr,
                  ),
                  IconButton(
                    icon: const Icon(Icons.directions_car),
                    onPressed: () => Get.toNamed(Routes.CAR_REPORT),
                    tooltip: 'تقرير المركبة'.tr,
                  ),
                  IconButton(
                    icon: const Icon(Icons.summarize),
                    onPressed: () => _showSummaryReportDialog(context),
                    tooltip: 'تقرير ملخص الحالات'.tr,
                  ),
                  IconButton(
                    icon: const Icon(Icons.analytics),
                    onPressed: () => _showAdvancedReportDialog(context),
                    tooltip: 'تقرير متقدم'.tr,
                  ),
                  IconButton(
                    icon: const Icon(Icons.today),
                    onPressed: () => _showDailyReportDialog(context),
                    tooltip: 'تقرير يومي'.tr,
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () => controller.loadCars(),
                    tooltip: 'تحديث البيانات'.tr,
                  ),
                  _buildExportMenu(),
                ],
                bottom: TabBar(
                  tabs: [
                    Tab(
                      icon: const Icon(Icons.dashboard),
                      text: 'لوحة المعلومات'.tr,
                    ),
                    Tab(
                      icon: const Icon(Icons.list_alt),
                      text: 'السجلات'.tr,
                    ),
                  ],
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white.withOpacity(0.7),
                  indicatorColor: Colors.white,
                  onTap: (index) => controller.selectedTabIndex.value = index,
                ),
              ),
            ];
          },
          body: Obx(() {
            final tabIndex = controller.selectedTabIndex.value;
            return IndexedStack(
              index: tabIndex,
              children: [
                DashboardTab(controller: controller),
                RecordsTab(controller: controller),
              ],
            );
          }),
        ),
      ),
    );
  }

  void _showFiltersDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.filter_list, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تصفية التقارير'.tr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Flexible(
                child: SingleChildScrollView(
                  child: ReportFilters(controller: controller, inDialog: true),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      controller.clearFilters();
                      Navigator.pop(context);
                    },
                    child: Text('إعادة ضبط'.tr),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      controller.applyDateRange();
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                    ),
                    child: Text('تطبيق'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExportMenu() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.download),
      tooltip: 'تصدير التقرير'.tr,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'pdf',
          child: Row(
            children: [
              Icon(Icons.picture_as_pdf, color: Colors.red.shade700),
              const SizedBox(width: 8),
              Text('تصدير كملف PDF'.tr),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'excel',
          child: Row(
            children: [
              Icon(Icons.table_view, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text('تصدير كملف Excel'.tr),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'csv',
          child: Row(
            children: [
              Icon(Icons.table_chart, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text('تصدير كملف CSV'.tr),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'pdf':
            controller.exportToPDF();
            break;
          case 'excel':
            controller.exportToExcel();
            break;
          case 'csv':
            controller.exportToCSV();
            break;
        }
      },
    );
  }

  void _showSummaryReportDialog(BuildContext context) {
    // Create a temporary controller for the date range picker
    final dateRangeController = TextEditingController(
      text:
          '${DateFormat('yyyy-MM-dd').format(controller.startDate.value)} - ${DateFormat('yyyy-MM-dd').format(controller.endDate.value)}',
    );

    // Create a temporary selected sector
    final selectedSector = Rxn<String>(controller.selectedSector.value);

    // Create a set of fixed statuses with the specified meanings
    final selectedStatuses = RxSet<CarStatus>({
      CarStatus.active, // النشطة
      CarStatus.sentRequest, // السيارات العطلة
      CarStatus.pending, // في الورشة
      CarStatus.done, // خارجة من الورشة
    });

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(Icons.summarize, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تقرير ملخص الحالات'.tr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 8),
                      Text(
                        'نطاق التاريخ'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: dateRangeController,
                        readOnly: true,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.calendar_today),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.date_range),
                            onPressed: () async {
                              final result = await showDialog(
                                context: context,
                                builder: (context) => Dialog(
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        SfDateRangePicker(
                                          controller:
                                              DateRangePickerController(),
                                          view: DateRangePickerView.month,
                                          selectionMode:
                                              DateRangePickerSelectionMode
                                                  .range,
                                          initialSelectedRange: PickerDateRange(
                                            controller.startDate.value,
                                            controller.endDate.value,
                                          ),
                                          onSubmit: (value) {
                                            if (value is PickerDateRange) {
                                              final start = value.startDate ??
                                                  DateTime.now().subtract(
                                                      const Duration(days: 7));
                                              final end = value.endDate ??
                                                  DateTime.now();

                                              dateRangeController.text =
                                                  '${DateFormat('yyyy-MM-dd').format(start)} - ${DateFormat('yyyy-MM-dd').format(end)}';

                                              Navigator.pop(context, {
                                                'start': start,
                                                'end': end,
                                              });
                                            }
                                          },
                                          onCancel: () =>
                                              Navigator.pop(context),
                                          showActionButtons: true,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );

                              if (result != null) {
                                dateRangeController.text =
                                    '${DateFormat('yyyy-MM-dd').format(result['start'])} - ${DateFormat('yyyy-MM-dd').format(result['end'])}';
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'القطاع'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Obx(() => DropdownButtonFormField<String>(
                            value: selectedSector.value,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.location_city),
                            ),
                            items: [
                              DropdownMenuItem(
                                value: null,
                                child: Text('الكل'.tr),
                              ),
                              ...controller.availableSectors.map((sector) {
                                return DropdownMenuItem(
                                  value: sector,
                                  child: Text(sector),
                                );
                              }).toList(),
                            ],
                            onChanged: (value) {
                              selectedSector.value = value;
                            },
                          )),

                      // No status selection - using fixed statuses
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // Parse date range
                      final dateRange = dateRangeController.text.split(' - ');
                      final start =
                          DateFormat('yyyy-MM-dd').parse(dateRange[0]);
                      final end = DateFormat('yyyy-MM-dd').parse(dateRange[1]);

                      // Generate and export PDF report
                      controller.generateStatusSummaryReport(
                        start: start,
                        end: end,
                        sectorId: selectedSector.value,
                        selectedStatuses: selectedStatuses.toList(),
                        exportFormat: 'pdf',
                      );

                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.picture_as_pdf),
                    label: Text('تصدير PDF'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Parse date range
                      final dateRange = dateRangeController.text.split(' - ');
                      final start =
                          DateFormat('yyyy-MM-dd').parse(dateRange[0]);
                      final end = DateFormat('yyyy-MM-dd').parse(dateRange[1]);

                      // Generate and export Excel report
                      controller.generateStatusSummaryReport(
                        start: start,
                        end: end,
                        sectorId: selectedSector.value,
                        selectedStatuses: selectedStatuses.toList(),
                        exportFormat: 'excel',
                      );

                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.table_view),
                    label: Text('تصدير Excel'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAdvancedReportDialog(BuildContext context) {
    // Create a temporary controller for the date range picker
    final dateRangeController = TextEditingController(
      text:
          '${DateFormat('yyyy-MM-dd').format(controller.startDate.value)} - ${DateFormat('yyyy-MM-dd').format(controller.endDate.value)}',
    );

    // Create a temporary selected sector
    final selectedSector = Rxn<String>(controller.selectedSector.value);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(Icons.analytics, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تقرير متقدم للورشة'.tr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 8),
                      Text(
                        'نطاق التاريخ'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: dateRangeController,
                        readOnly: true,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.calendar_today),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.date_range),
                            onPressed: () async {
                              final result = await showDialog(
                                context: context,
                                builder: (context) => Dialog(
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        SfDateRangePicker(
                                          controller:
                                              DateRangePickerController(),
                                          view: DateRangePickerView.month,
                                          selectionMode:
                                              DateRangePickerSelectionMode
                                                  .range,
                                          initialSelectedRange: PickerDateRange(
                                            controller.startDate.value,
                                            controller.endDate.value,
                                          ),
                                          onSubmit: (value) {
                                            if (value is PickerDateRange) {
                                              final start = value.startDate ??
                                                  DateTime.now().subtract(
                                                      const Duration(days: 7));
                                              final end = value.endDate ??
                                                  DateTime.now();

                                              dateRangeController.text =
                                                  '${DateFormat('yyyy-MM-dd').format(start)} - ${DateFormat('yyyy-MM-dd').format(end)}';

                                              Navigator.pop(context, {
                                                'start': start,
                                                'end': end,
                                              });
                                            }
                                          },
                                          onCancel: () =>
                                              Navigator.pop(context),
                                          showActionButtons: true,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );

                              if (result != null) {
                                dateRangeController.text =
                                    '${DateFormat('yyyy-MM-dd').format(result['start'])} - ${DateFormat('yyyy-MM-dd').format(result['end'])}';
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'القطاع'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Obx(() => DropdownButtonFormField<String>(
                            value: selectedSector.value,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.location_city),
                            ),
                            items: [
                              DropdownMenuItem(
                                value: null,
                                child: Text('الكل'.tr),
                              ),
                              ...controller.availableSectors.map((sector) {
                                return DropdownMenuItem(
                                  value: sector,
                                  child: Text(sector),
                                );
                              }).toList(),
                            ],
                            onChanged: (value) {
                              selectedSector.value = value;
                            },
                          )),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // Parse date range
                      final dateRange = dateRangeController.text.split(' - ');
                      final start =
                          DateFormat('yyyy-MM-dd').parse(dateRange[0]);
                      final end = DateFormat('yyyy-MM-dd').parse(dateRange[1]);

                      // Generate and export PDF report
                      controller.generateAdvancedWorkshopReport(
                        start: start,
                        end: end,
                        sectorId: selectedSector.value,
                        exportFormat: 'pdf',
                      );

                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.picture_as_pdf),
                    label: Text('تصدير PDF'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Parse date range
                      final dateRange = dateRangeController.text.split(' - ');
                      final start =
                          DateFormat('yyyy-MM-dd').parse(dateRange[0]);
                      final end = DateFormat('yyyy-MM-dd').parse(dateRange[1]);

                      // Generate and export Excel report
                      controller.generateAdvancedWorkshopReport(
                        start: start,
                        end: end,
                        sectorId: selectedSector.value,
                        exportFormat: 'excel',
                      );

                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.table_view),
                    label: Text('تصدير Excel'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDailyReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Icon(Icons.today, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'تقرير يومي'.tr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      controller.generateDailyReport(format: 'pdf');
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.picture_as_pdf),
                    label: Text('تصدير PDF'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      controller.generateDailyReport(format: 'excel');
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.table_view),
                    label: Text('تصدير Excel'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
