import 'package:flutter/material.dart';

class AppColors {
  // Main brand colors
  static const Color primary = Color.fromARGB(255, 255, 0, 72); // Modern blue
  static const Color secondary = Color.fromARGB(255, 208, 84, 1); // Teal accent
  static const Color accent = Color(0xFFFF7043); // Orange accent

  // Background colors
  static const Color background = Color(0xFFF8F9FA); // Light gray background
  static const Color surface = Color(0xFFFFFFFF); // White surface
  static const Color surfaceVariant =
      Color(0xFFF0F4F8); // Light blue-gray surface

  // Text colors
  static const Color textPrimary = Color(0xFF212121); // Almost black
  static const Color textSecondary = Color(0xFF616161); // Dark gray
  static const Color textLight = Color(0xFF9E9E9E); // Medium gray
  static const Color onPrimary = Color(0xFFFFFFFF); // White text on primary

  // Status colors
  static const Color success = Color(0xFF43A047); // Green
  static const Color error = Color(0xFFE53935); // Red
  static const Color warning = Color(0xFFFFB300); // Amber
  static const Color info = Color(0xFF039BE5); // Light blue
  static const Color pending = Color(0xFFFF9800); // Orange
  static const Color done = Color(0xFF3949AB); // Indigo
  static const Color logistics = Color(0xFF8E24AA); // Purple
  static const Color delivery = Color(0xFF00897B); // Teal
  static const Color workshop = Color(0xFFFFB74D); // Amber light
  static const Color approved = Color(0xFF5E35B1); // Deep Purple

  // Border and divider colors
  static const Color border = Color(0xFFE0E0E0); // Light gray border
  static const Color divider = Color(0xFFEEEEEE); // Very light gray divider

  // Card and elevation colors
  static const Color cardBackground = Color(0xFFFFFFFF); // White card
  static const Color shadowColor = Color(0x1A000000); // Light shadow

  // Gradients
  static const List<Color> primaryGradient = [primary, Color(0xFF64B5F6)];
  static const List<Color> secondaryGradient = [secondary, Color(0xFF80CBC4)];

  // Modern Card Colors
  static const List<Color> cardGradient1 = [
    Color(0xFF4158D0),
    Color(0xFFC850C0),
    Color(0xFFFFCC70)
  ];
  static const List<Color> cardGradient2 = [
    Color(0xFF0093E9),
    Color(0xFF80D0C7)
  ];
  static const List<Color> cardGradient3 = [
    Color(0xFF8EC5FC),
    Color(0xFFE0C3FC)
  ];
  static const List<Color> cardGradient4 = [
    Color(0xFFA9C9FF),
    Color(0xFFFFBBEC)
  ];
  static const List<Color> cardGradient5 = [
    Color(0xFF21D4FD),
    Color(0xFFB721FF)
  ];

  // Status Card Colors
  static const List<Color> totalGradient = [
    Color(0xFF4A00E0),
    Color(0xFF8E2DE2)
  ];
  static const List<Color> pendingGradient = [
    Color(0xFFF9D423),
    Color(0xFFFF4E50)
  ];
  static const List<Color> doneGradient = [
    Color(0xFF00B4DB),
    Color(0xFF0083B0)
  ];
  static const List<Color> rejectedGradient = [
    Color(0xFFED213A),
    Color(0xFF93291E)
  ];
}
