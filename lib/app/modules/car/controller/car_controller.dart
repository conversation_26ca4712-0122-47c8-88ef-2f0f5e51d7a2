import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/sector_model.dart';
import '../../../controllers/auth_controller.dart';

class CarController extends GetxController {
  final _firestore = FirebaseFirestore.instance;
  final _auth = Get.find<AuthController>();

  // Observable states
  final cars = <Car>[].obs;
  final sectors = <Sector>[].obs;
  final isLoading = false.obs;
  final selectedStatus = Rxn<CarStatus>();
  final selectedSector = Rxn<Sector>();
  final searchQuery = ''.obs;
  final isSearchVisible = false.obs;

  // Computed property for filtered cars
  List<Car> get filteredCars {
    return cars.where((car) {
      bool matchesStatus = true;
      bool matchesSector = true;
      bool matchesSearch = true;

      if (selectedStatus.value != null) {
        matchesStatus = car.status == selectedStatus.value
            .toString()
            .split('.')
            .last;
      }

      if (selectedSector.value != null) {
        matchesSector = car.sectorId == selectedSector.value!.id;
      }
      
      if (searchQuery.value.isNotEmpty) {
        matchesSearch = car.plateNumber?.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false ||
                       car.encoding!.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false ||
                       car.carModel!.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false ||
                       car.carType!.toLowerCase().contains(searchQuery.value.toLowerCase()) ?? false;
      }

      return matchesStatus && matchesSector && matchesSearch;
    }).toList();
  }

  @override
  void onInit() {
    super.onInit();
    loadCars();
    loadSectors();
    _initializeFilters();

    // createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '', plateCharacters: '', encoding: '', carModel: '', sectorName: '', tempSectorName: '', carType: '', rotations: [], isInternal: , createAt: DateTime.now(), status: , workshopHistory: []));
  }

  // void processCarData() {
  //
  //   /*
  //   * العلاء id 18mxt5oqail2M6GqetRq
  //   *
  //   *
  //   * */
  //   // Data processing
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4069', plateCharacters: 'د س ل', encoding: 'MED013', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '1828', plateCharacters: 'د ط أ', encoding: 'MED006', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6009', plateCharacters: 'د ص ب', encoding: 'MED008', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '1815', plateCharacters: 'د ط ا', encoding: 'MED011', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: 'Q0qKx1pM3flFnt6pMNbc', tempSectorId: '', plateNumber: '6776', plateCharacters: 'ب ر  د', encoding: 'MED010', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: 'Q0qKx1pM3flFnt6pMNbc', tempSectorId: '', plateNumber: '6026', plateCharacters: 'د ص ب', encoding: 'MED019', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: 'Q0qKx1pM3flFnt6pMNbc', tempSectorId: '', plateNumber: '3159', plateCharacters: 'د ص ب', encoding: 'MED087', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: 'Q0qKx1pM3flFnt6pMNbc', tempSectorId: '', plateNumber: '8586', plateCharacters: 'ب س ص', encoding: 'MED092', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: 'Q0qKx1pM3flFnt6pMNbc', tempSectorId: '', plateNumber: '3619', plateCharacters: 'ب س ل', encoding: 'MED00001', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3869', plateCharacters: 'ب س ل', encoding: '', carModel: 'ترانزيت', sectorName: 'التدريب', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3527', plateCharacters: 'ب س ل', encoding: '', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4673', plateCharacters: 'ب س ح', encoding: 'MED005', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4659', plateCharacters: 'ب س ح', encoding: 'MED014', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '9922', plateCharacters: 'ب س ح', encoding: 'MED053', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6007', plateCharacters: 'د ص ب', encoding: 'MED015', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3158', plateCharacters: 'د ص ن', encoding: 'MED016', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7790', plateCharacters: 'ب س ل', encoding: 'MED00000', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6769', plateCharacters: 'ب ر د', encoding: 'MED020', carModel: 'ترانزيت', sectorName: 'الحناكية', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7097', plateCharacters: 'ا و ه', encoding: 'MED067', carModel: 'جي ام سي', sectorName: 'الحناكية', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6772', plateCharacters: 'ب ر د', encoding: 'MED032', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7067', plateCharacters: 'ا و هـ', encoding: 'MED028', carModel: 'جي ام سي', sectorName: 'الحناكية', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7587', plateCharacters: 'ا و هـ', encoding: 'MED029', carModel: 'جي ام سي', sectorName: 'الحناكية', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8683', plateCharacters: 'ب س ص', encoding: 'MED090', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4676', plateCharacters: 'ب س ح', encoding: 'MED017', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '1831', plateCharacters: 'د ط ا', encoding: 'MED001', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8590', plateCharacters: 'ب س ص', encoding: 'MED002', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '9925', plateCharacters: 'ب س ح', encoding: 'MED059', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '9918', plateCharacters: 'ب س ل', encoding: 'MED091', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4072', plateCharacters: 'د س ل', encoding: 'MED004', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '9917', plateCharacters: 'ب س ح', encoding: 'MED048', carModel: 'فورد فان', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3312', plateCharacters: 'ا ه ن', encoding: 'MED025', carModel: 'جي ام سي', sectorName: 'المهد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '2168', plateCharacters: 'ح س ح', encoding: 'MED061', carModel: 'فورد فان', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8730', plateCharacters: 'ا و هـ', encoding: 'MED 8730 HUA', carModel: 'جي ام سي', sectorName: 'الصيانة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3371', plateCharacters: 'ا و د', encoding: 'MED065', carModel: 'فورد فان', sectorName: 'الحناكية', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6765', plateCharacters: 'ب ر د', encoding: 'MED080', carModel: 'ترانزيت', sectorName: 'الصيانة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7083', plateCharacters: 'ا و هـ', encoding: 'MED083', carModel: 'جي ام سي', sectorName: 'المهد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4052', plateCharacters: 'د س ل', encoding: 'MED012', carModel: 'ترانزيت', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6020', plateCharacters: 'د ص ب', encoding: 'MED086', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4687', plateCharacters: 'ب س ح', encoding: 'MED007', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7895', plateCharacters: 'ب ر د', encoding: 'MED009', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4058', plateCharacters: 'د س ل', encoding: 'MED003', carModel: 'ترانزيت', sectorName: 'الحرم', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3326', plateCharacters: 'ا هـ ن', encoding: 'MED088', carModel: 'جي ام سي', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6022', plateCharacters: 'د ص ب', encoding: 'MED021', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4088', plateCharacters: 'د س ل', encoding: 'MED085', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3386', plateCharacters: 'ا و د', encoding: 'MED072', carModel: 'فورد فان', sectorName: 'العلا', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6781', plateCharacters: 'ب ر د', encoding: 'MED022', carModel: 'ترانزيت', sectorName: 'احد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3374', plateCharacters: 'ا ود', encoding: 'MED064', carModel: 'فورد فان', sectorName: 'العلا', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Plate chars 'ا ود'
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7891', plateCharacters: 'ب ر د', encoding: 'MED055', carModel: 'ترانزيت', sectorName: 'العلا', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4689', plateCharacters: 'ب س ح', encoding: 'MED058', carModel: 'ترانزيت', sectorName: 'العلا', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7590', plateCharacters: 'ا و ه', encoding: 'MED062', carModel: 'جي ام سي', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3365', plateCharacters: 'ا و د', encoding: 'MED057', carModel: 'فورد فان', sectorName: 'المهد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7586', plateCharacters: 'ا و ه', encoding: 'MED046', carModel: 'جي ام سي', sectorName: 'المهد', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3375', plateCharacters: 'ا و د', encoding: 'MED047', carModel: 'فورد فان', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8057', plateCharacters: 'ا و ه', encoding: 'MED044', carModel: 'جي ام  سي', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Model 'جي ام  سي'
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8725', plateCharacters: 'ا و م', encoding: 'MED049', carModel: 'هورتن', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8732', plateCharacters: 'ا و م', encoding: 'MED050', carModel: 'هورتن', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3342', plateCharacters: 'ا ه ن', encoding: 'MED052', carModel: 'جي ام سي', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3384', plateCharacters: 'ا و د', encoding: 'MED043', carModel: 'فورد فان', sectorName: 'بدر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3362', plateCharacters: 'ا و ه', encoding: 'MED038', carModel: 'فورد فان', sectorName: 'بدر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7787', plateCharacters: 'ب س ل', encoding: 'MED00003', carModel: 'ترانزيت', sectorName: 'الهجرة', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8736', plateCharacters: 'ا و م', encoding: 'MED039', carModel: 'فورد هورتن', sectorName: 'بدر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7073', plateCharacters: 'ا و هـ', encoding: 'MED018', carModel: 'جي ام سي', sectorName: 'بدر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7066', plateCharacters: 'ا و ه', encoding: 'MED040', carModel: 'جي ام سي', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4607', plateCharacters: 'ب ر ب', encoding: 'MED081', carModel: 'ترانزيت', sectorName: 'السلام', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '2168', plateCharacters: 'ح س ح', encoding: 'MED061', carModel: 'فورد فان', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Duplicate line? Same as line 64 original index
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3372', plateCharacters: 'ا و ه', encoding: 'MED070', carModel: 'فورد فان', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3376', plateCharacters: 'ا و د', encoding: 'MED030', carModel: 'فورد فان', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4875', plateCharacters: 'ب س ا', encoding: 'MED031', carModel: 'ترانزيت', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3351', plateCharacters: 'ا و د', encoding: 'MED033', carModel: 'فورد فان', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '4876', plateCharacters: 'ب ص ا', encoding: 'MED034', carModel: 'ترانزيت', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '6590', plateCharacters: 'ب ر د', encoding: 'MED035', carModel: 'ترانزيت', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7092', plateCharacters: 'ا و هـ', encoding: 'MED041', carModel: 'جي ام سي', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '7074', plateCharacters: 'ا و هـ', encoding: 'MED042', carModel: 'جي ام سي', sectorName: 'ينبع', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '8732', plateCharacters: 'ا و ه', encoding: 'MED 8732 HUA', carModel: 'جي ام سي', sectorName: 'خيبر', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '2167', plateCharacters: 'ح س ح', encoding: 'MED056', carModel: 'فورد فان', sectorName: 'العلا', tempSectorName: '', carType: 'اسعافية', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: []));
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3370', plateCharacters: 'ب ر ب', encoding: '', carModel: 'جي ام سي', sectorName: 'الحناكية', tempSectorName: '', carType: 'دعم لوجستي', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3798', plateCharacters: 'ب ر ك', encoding: '', carModel: 'نيسان باترول', sectorName: 'احد', tempSectorName: '', carType: 'طوارئ', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3799', plateCharacters: 'ب ر  ك', encoding: '', carModel: 'نيسان باترول', sectorName: 'السلام', tempSectorName: '', carType: 'طوارئ', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '1109', plateCharacters: 'ح ص ح', encoding: '', carModel: 'فورتشنر', sectorName: 'السلام', tempSectorName: '', carType: 'طوارئ', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding
  //   createCar(Car(id: Uuid().v4(), sectorId: '', tempSectorId: '', plateNumber: '3798', plateCharacters: 'ب ر  ك', encoding: '', carModel: 'نيسان باترول', sectorName: 'احد', tempSectorName: '', carType: 'طوارئ', rotations: [], isInternal: false, createAt: DateTime.now(), status: CarStatus.active, workshopHistory: [])); // Missing encoding, Duplicate Plate?
  // }

  void processCarDataWithSectors() {
    final uuid = Uuid();
    final now = DateTime.now();

    // --- Sector ID Mapping ---
    final Map<String, String> sectorIdMap = {
      'احد': 'V9XjEkFNgp2u3yyPf1z8',
      // ohd
      'العلا': 'flzf07UedFCJ37FfkSpf',
      // ola
      'بدر': 'fAzJeb5NU48GqA5vn0H0',
      // bdr
      'الحناكية': 'uE2ZkrTJdDGQahA5qbM4',
      // HNKEA
      'السلام': 'aNJp1Jjtmo3qVLWXxNk5',
      // salam
      'الهجرة': '6Tbjc4jBGD0avXkZhA8t',
      // hjr
      'الصيانة': 'by5X86M40e2p89Ci2dz5',
      // min (Assuming 'الصيانة' maps to 'min')
      'خيبر': 'jA9nYz3rmI4pLonmYKGT',
      // kbr
      'المهد': 'SiMuPTblsgJ7VzXQsXcA',
      // mhd
      'ينبع': 'PpZMb8Ey2ea1JmHEWIuQ',
      // ynb
      'الحرم': '1p1J2kDno56Yz3UmjQu4',
      // hrm
      // 'التدريب' is missing, will default to ''
    };

    // Helper function to get ID or default
    String getSectorId(String sectorName) {
      return sectorIdMap[sectorName.trim()] ?? ''; // Use trim() for safety
    }

    // --- Data Processing ---
    String sectorName;
    String sectorId;

    // Line 1
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4069',
        plateCharacters: 'د س ل',
        encoding: 'MED013',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 2
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '1828',
        plateCharacters: 'د ط أ',
        encoding: 'MED006',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 3
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6009',
        plateCharacters: 'د ص ب',
        encoding: 'MED008',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 4
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '1815',
        plateCharacters: 'د ط ا',
        encoding: 'MED011',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 5
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6776',
        plateCharacters: 'ب ر  د',
        encoding: 'MED010',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 6
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6026',
        plateCharacters: 'د ص ب',
        encoding: 'MED019',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 7
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3159',
        plateCharacters: 'د ص ب',
        encoding: 'MED087',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 8
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8586',
        plateCharacters: 'ب س ص',
        encoding: 'MED092',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 9
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3619',
        plateCharacters: 'ب س ل',
        encoding: 'MED00001',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 10 (First one)
    sectorName = 'التدريب'; // No ID provided for this sector
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3869',
        plateCharacters: 'ب س ل',
        encoding: '',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 10 (Second one)
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3527',
        plateCharacters: 'ب س ل',
        encoding: '',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 11
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4673',
        plateCharacters: 'ب س ح',
        encoding: 'MED005',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 12
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4659',
        plateCharacters: 'ب س ح',
        encoding: 'MED014',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 13
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '9922',
        plateCharacters: 'ب س ح',
        encoding: 'MED053',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 14
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6007',
        plateCharacters: 'د ص ب',
        encoding: 'MED015',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 15
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3158',
        plateCharacters: 'د ص ن',
        encoding: 'MED016',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 16
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7790',
        plateCharacters: 'ب س ل',
        encoding: 'MED00000',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 17
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6769',
        plateCharacters: 'ب ر د',
        encoding: 'MED020',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 18
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7097',
        plateCharacters: 'ا و ه',
        encoding: 'MED067',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 24 (Original Index) -> Renumbered to 19
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6772',
        plateCharacters: 'ب ر د',
        encoding: 'MED032',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 20 -> Renumbered to 20
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7067',
        plateCharacters: 'ا و هـ',
        encoding: 'MED028',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 21 -> Renumbered to 21
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7587',
        plateCharacters: 'ا و هـ',
        encoding: 'MED029',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 22 -> Renumbered to 22
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8683',
        plateCharacters: 'ب س ص',
        encoding: 'MED090',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 23 -> Renumbered to 23
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4676',
        plateCharacters: 'ب س ح',
        encoding: 'MED017',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 30 -> Renumbered to 24
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '1831',
        plateCharacters: 'د ط ا',
        encoding: 'MED001',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 31 -> Renumbered to 25
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8590',
        plateCharacters: 'ب س ص',
        encoding: 'MED002',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 26 -> Renumbered to 26
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '9925',
        plateCharacters: 'ب س ح',
        encoding: 'MED059',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 32 -> Renumbered to 27
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '9918',
        plateCharacters: 'ب س ل',
        encoding: 'MED091',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 28 -> Renumbered to 28
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4072',
        plateCharacters: 'د س ل',
        encoding: 'MED004',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 29 -> Renumbered to 29
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '9917',
        plateCharacters: 'ب س ح',
        encoding: 'MED048',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 50 -> Renumbered to 30
    sectorName = 'المهد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3312',
        plateCharacters: 'ا ه ن',
        encoding: 'MED025',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 64 -> Renumbered to 31
    sectorName = 'خيبر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '2168',
        plateCharacters: 'ح س ح',
        encoding: 'MED061',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 19 -> Renumbered to 32
    sectorName = 'الصيانة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8730',
        plateCharacters: 'ا و هـ',
        encoding: 'MED 8730 HUA',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 25 -> Renumbered to 33
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3371',
        plateCharacters: 'ا و د',
        encoding: 'MED065',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 34 -> Renumbered to 34
    sectorName = 'الصيانة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6765',
        plateCharacters: 'ب ر د',
        encoding: 'MED080',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 27 -> Renumbered to 35
    sectorName = 'المهد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7083',
        plateCharacters: 'ا و هـ',
        encoding: 'MED083',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 33 -> Renumbered to 36
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4052',
        plateCharacters: 'د س ل',
        encoding: 'MED012',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 37 -> Renumbered to 37
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6020',
        plateCharacters: 'د ص ب',
        encoding: 'MED086',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 35 -> Renumbered to 38
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4687',
        plateCharacters: 'ب س ح',
        encoding: 'MED007',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 36 -> Renumbered to 39
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7895',
        plateCharacters: 'ب ر د',
        encoding: 'MED009',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 40 -> Renumbered to 40
    sectorName = 'الحرم';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4058',
        plateCharacters: 'د س ل',
        encoding: 'MED003',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 41 -> Renumbered to 41
    sectorName = 'خيبر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3326',
        plateCharacters: 'ا هـ ن',
        encoding: 'MED088',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 38 -> Renumbered to 42
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6022',
        plateCharacters: 'د ص ب',
        encoding: 'MED021',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 43 -> Renumbered to 43
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4088',
        plateCharacters: 'د س ل',
        encoding: 'MED085',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 44 -> Renumbered to 44
    sectorName = 'العلا';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3386',
        plateCharacters: 'ا و د',
        encoding: 'MED072',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 45 -> Renumbered to 45
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6781',
        plateCharacters: 'ب ر د',
        encoding: 'MED022',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 46 -> Renumbered to 46
    sectorName = 'العلا';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3374',
        plateCharacters: 'ا ود',
        encoding: 'MED064',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 47 -> Renumbered to 47
    sectorName = 'العلا';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7891',
        plateCharacters: 'ب ر د',
        encoding: 'MED055',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 49 -> Renumbered to 48
    sectorName = 'العلا';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4689',
        plateCharacters: 'ب س ح',
        encoding: 'MED058',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 39 -> Renumbered to 49
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7590',
        plateCharacters: 'ا و ه',
        encoding: 'MED062',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 51 -> Renumbered to 50
    sectorName = 'المهد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3365',
        plateCharacters: 'ا و د',
        encoding: 'MED057',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 52 -> Renumbered to 51
    sectorName = 'المهد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7586',
        plateCharacters: 'ا و ه',
        encoding: 'MED046',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 53 -> Renumbered to 52
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3375',
        plateCharacters: 'ا و د',
        encoding: 'MED047',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 54 -> Renumbered to 53
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8057',
        plateCharacters: 'ا و ه',
        encoding: 'MED044',
        carModel: 'جي ام  سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 55 -> Renumbered to 54
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8725',
        plateCharacters: 'ا و م',
        encoding: 'MED049',
        carModel: 'هورتن',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 56 -> Renumbered to 55
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8732',
        plateCharacters: 'ا و م',
        encoding: 'MED050',
        carModel: 'هورتن',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 57 -> Renumbered to 56
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3342',
        plateCharacters: 'ا ه ن',
        encoding: 'MED052',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 58 -> Renumbered to 57
    sectorName = 'بدر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3384',
        plateCharacters: 'ا و د',
        encoding: 'MED043',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 59 -> Renumbered to 58
    sectorName = 'بدر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3362',
        plateCharacters: 'ا و ه',
        encoding: 'MED038',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 60 -> Renumbered to 59
    sectorName = 'الهجرة';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7787',
        plateCharacters: 'ب س ل',
        encoding: 'MED00003',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 61 -> Renumbered to 60
    sectorName = 'بدر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8736',
        plateCharacters: 'ا و م',
        encoding: 'MED039',
        carModel: 'فورد هورتن',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 62 -> Renumbered to 61
    sectorName = 'بدر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7073',
        plateCharacters: 'ا و هـ',
        encoding: 'MED018',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 63 -> Renumbered to 62
    sectorName = 'خيبر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7066',
        plateCharacters: 'ا و ه',
        encoding: 'MED040',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 42 -> Renumbered to 63
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4607',
        plateCharacters: 'ب ر ب',
        encoding: 'MED081',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 65 -> Renumbered to 64
    sectorName = 'خيبر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3372',
        plateCharacters: 'ا و ه',
        encoding: 'MED070',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 66 -> Renumbered to 65
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3376',
        plateCharacters: 'ا و د',
        encoding: 'MED030',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 67 -> Renumbered to 66
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4875',
        plateCharacters: 'ب س ا',
        encoding: 'MED031',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 68 -> Renumbered to 67
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3351',
        plateCharacters: 'ا و د',
        encoding: 'MED033',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 69 -> Renumbered to 68
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '4876',
        plateCharacters: 'ب ص ا',
        encoding: 'MED034',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 70 -> Renumbered to 69
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '6590',
        plateCharacters: 'ب ر د',
        encoding: 'MED035',
        carModel: 'ترانزيت',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 71 -> Renumbered to 70
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7092',
        plateCharacters: 'ا و هـ',
        encoding: 'MED041',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 72 -> Renumbered to 71
    sectorName = 'ينبع';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '7074',
        plateCharacters: 'ا و هـ',
        encoding: 'MED042',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 73 -> Renumbered to 72
    sectorName = 'خيبر';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '8732',
        plateCharacters: 'ا و ه',
        encoding: 'MED 8732 HUA',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 48 -> Renumbered to 73
    sectorName = 'العلا';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '2167',
        plateCharacters: 'ح س ح',
        encoding: 'MED056',
        carModel: 'فورد فان',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'اسعافية',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 74 -> Renumbered to 74
    sectorName = 'الحناكية';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3370',
        plateCharacters: 'ب ر ب',
        encoding: '',
        carModel: 'جي ام سي',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'دعم لوجستي',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 75 -> Renumbered to 75
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3798',
        plateCharacters: 'ب ر ك',
        encoding: '',
        carModel: 'نيسان باترول',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'طوارئ',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 76 -> Renumbered to 76
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3799',
        plateCharacters: 'ب ر  ك',
        encoding: '',
        carModel: 'نيسان باترول',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'طوارئ',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 77 -> Renumbered to 77
    sectorName = 'السلام';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '1109',
        plateCharacters: 'ح ص ح',
        encoding: '',
        carModel: 'فورتشنر',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'طوارئ',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));

    // Line 78 -> Renumbered to 78
    sectorName = 'احد';
    sectorId = getSectorId(sectorName);
    createCar(Car(id: uuid.v4(),
        sectorId: sectorId,
        tempSectorId: '',
        plateNumber: '3798',
        plateCharacters: 'ب ر  ك',
        encoding: '',
        carModel: 'نيسان باترول',
        sectorName: sectorName,
        tempSectorName: '',
        carType: 'طوارئ',
        rotations: [],
        isInternal: false,
        createAt: now,
        status: CarStatus.active,
        workshopHistory: []));
  }

  void _initializeFilters() {
    final status = Get.parameters['status'];
    if (status != null && status.isNotEmpty) {
      try {
        final statusEnum = CarStatus.values.firstWhere(
          (e) => e.toString().split('.').last.toLowerCase() == status.toLowerCase()
        );
        selectedStatus.value = statusEnum;
      } catch (e) {
        print('Invalid status parameter: $status');
      }
    }
  }

  Future<void> loadCars() async {
    try {
      isLoading.value = true;
      final snapshot = await _firestore.collection('cars').get();
      cars.value = snapshot.docs
          .map((doc) => Car.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'failed_to_load_cars'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadSectors() async {
    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('sectors')
          .get();

      sectors.value = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Sector.fromJson({...data, 'id': doc.id});
      }).toList();
      
      print('Loaded ${sectors.length} sectors');
    } catch (e) {
      print('Error loading sectors: $e');
      Get.snackbar(
        'Error',
        'Failed to load sectors: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> createCar(Car car) async {
    try {
      isLoading.value = true;
      // final carData = {
      //   'sectorId': sectorId,
      //   'plateNumber': plateNumber,
      //   'plateCharacters': plateCharacters,
      //   'encoding': encoding,
      //   'carModel': carModel,
      //   'carType': carType,
      //   'isInternal': isInternal,
      //   'createAt': Timestamp.now(),
      //   'status': CarStatus.active.toString().split('.').last,
      //   'transactions': [],
      //   'workshopHistory': [],
      //   'metadata': {},
      // };
      //
      await FirebaseFirestore.instance
          .collection('cars')
      .doc(car.id)
          .set(car.toJson());
          
      // await loadCars();
      // Get.back();
      // Get.snackbar(
      //   'Success',
      //   'Car created successfully!',
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    } catch (e) {
      print('Error creating car: $e');
      throw e; // Re-throw to handle in the form
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateCar(Car car) async {
    try {
      // isLoading.value = true;
      // final updates = <String, dynamic>{
      //   if (sectorId != null) 'sectorId': sectorId,
      //   if (plateNumber != null) 'plateNumber': plateNumber,
      //   if (plateCharacters != null) 'plateCharacters': plateCharacters,
      //   if (encoding != null) 'encoding': encoding,
      //   if (carModel != null) 'carModel': carModel,
      //   if (carType != null) 'carType': carType,
      //   if (isInternal != null) 'isInternal': isInternal,
      //   if (status != null) 'status': status.toString().split('.').last,
      //   'lastUpdated': FieldValue.serverTimestamp(),
      //   'lastUpdatedBy': _auth.uid,
      //   'lastUpdatedByName': _auth.userName,
      // };
      //
      await FirebaseFirestore.instance
          .collection('cars')
          .doc(car.id)
          .update(car.toJson());

      Logger().i(car.toJson());
          
      await loadCars();
      Get.snackbar('Success', 'Car updated successfully!');
    } catch (e) {
      print('Error updating car: $e');
      Get.snackbar('Error', 'Failed to update car: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteCar(String carId) async {
    try {
      isLoading.value = true;
      await FirebaseFirestore.instance
          .collection('cars')
          .doc(carId)
          .delete();
          
      await loadCars();
      Get.snackbar('Success', 'Car deleted successfully!');
    } catch (e) {
      print('Error deleting car: $e');
      Get.snackbar('Error', 'Failed to delete car: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> transferCar(String carId, String fromSectorId, String toSectorId, {String? notes}) async {
    try {
      isLoading.value = true;
      // final transaction = CarTransaction(
      //   id: DateTime.now().millisecondsSinceEpoch.toString(),
      //   fromSectorId: fromSectorId,
      //   toSectorId: toSectorId,
      //   date: DateTime.now(),
      //   notes: notes,
      //   type: 'transfer',
      // );

      // final carDoc = await FirebaseFirestore.instance.collection('cars').doc(carId).get();
      // final currentTransactions = List<Map<String, dynamic>>.from(carDoc.data()?['transactions'] ?? []);
      // currentTransactions.add(transaction.toJson());
      //
      // await FirebaseFirestore.instance.collection('cars').doc(carId).update({
      //   'sectorId': toSectorId,
      //   'transactions': currentTransactions,
      //   'lastUpdated': FieldValue.serverTimestamp(),
      //   'lastUpdatedBy': _auth.uid,
      //   'lastUpdatedByName': _auth.userName,
      // });

      await loadCars();
      Get.snackbar('Success', 'Car transferred successfully!');
    } catch (e) {
      print('Error transferring car: $e');
      Get.snackbar('Error', 'Failed to transfer car: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addWorkshopEntry(String carId,List<WorkshopEntry> entries,CarStatus carStatus) async {
    print(carId);
    final carRef = _firestore.collection('cars').doc(carId);
    
    await _firestore.runTransaction((transaction) async {
      final carDoc = await transaction.get(carRef);
      if (!carDoc.exists) {
        throw Exception('Car not found');
      }

      transaction.update(carRef, {
        'workshopEntries': entries.map((WorkshopEntry e) => e.toJson()).toList(),
        'lastUpdated': FieldValue.serverTimestamp(),
        'lastUpdatedBy': _auth.uid,
        'status': carStatus.toString().split('.').last,
        'lastUpdatedByName': _auth.userName,
      });

      Get.back();

    });

    Get.snackbar(
      'success'.tr,
      'entry_saved'.tr,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  Future<void> updateWorkshopEntry(String carId, List<WorkshopEntry> entries) async {
    final carRef = _firestore.collection('cars').doc(carId);
    
    await _firestore.runTransaction((transaction) async {
      final carDoc = await transaction.get(carRef);
      if (!carDoc.exists) {
        throw Exception('Car not found');
      }

      final car = Car.fromJson({...carDoc.data()!, 'id': carDoc.id});

      transaction.update(carRef, {
        'workshopEntries': entries.map((e) => e.toJson()).toList(),
        'lastUpdated': FieldValue.serverTimestamp(),
        'lastUpdatedBy': _auth.uid,
        'lastUpdatedByName': _auth.userName,
      });
    });

    Get.snackbar(
      'success'.tr,
      'entry_saved'.tr,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  CarStatus _getCarStatusFromWorkshopStatus(WorkshopStatus status) {
    switch (status) {
      case WorkshopStatus.maintenance:
        return CarStatus.maintenance;
      case WorkshopStatus.inWorkshop:
        return CarStatus.inWorkshop;
      case WorkshopStatus.completed:
        return CarStatus.active;
      case WorkshopStatus.pending:
        // TODO: Handle this case.
        return CarStatus.active;
      case WorkshopStatus.inProgress:
        // TODO: Handle this case.
        return CarStatus.active;
    }
  }
}
