import 'package:cars_app/app/data/models/user_model.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AppUsersController extends GetxController {
  final RxList<UserModel> users = <UserModel>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchUsers(); // Load once when controller is initialized
  }

  Future<void> fetchUsers() async {
    try {
      isLoading.value = true;
      //get doucmnet id as
      final snapshot = await FirebaseFirestore.instance.collection('users').get();
      final userList = snapshot.docs
            .map((doc) => UserModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
      users.assignAll(userList);
    } catch (e) {
      Get.snackbar("Error", "Failed to load users: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // Reactive computed list of all users
  RxList<UserModel> get allUsers => users;

  // Reactive filtered list of technicians
  RxList<UserModel> get technicians =>
      users.where((user) => user.role == UserRole.technician||user.role == UserRole.admin).toList().obs;

  // Optional: Reactive single user by ID
  Rx<UserModel?> getUserById(String id) {
    final user = users.firstWhereOrNull((user) => user.id == id);
    return Rx<UserModel?>(user);
  }
}
