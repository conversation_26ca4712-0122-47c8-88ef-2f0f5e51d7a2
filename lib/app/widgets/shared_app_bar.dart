import 'package:flutter/material.dart';
import '../themes/app_colors.dart';
import 'package:animated_text_kit/animated_text_kit.dart'; // Import the package

class SharedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? customTitle;
  final VoidCallback? onSettingsPressed;
  final List<Widget>? actions;
  final Widget? bottom;
  final bool showBackButton;

  const SharedAppBar({
    Key? key,
    required this.title,
    this.customTitle,
    this.actions,
    this.bottom,
    this.onSettingsPressed,
    this.showBackButton = false,
  }) : super(key: key);

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 20);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: preferredSize.height,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(10), // Rounded corners at the bottom
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // Subtle shadow
              blurRadius: 10,
              offset: const Offset(0, 5), // Shadow below the app bar
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side with back button and title
              Row(
                children: [
                  if (showBackButton)
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'رجوع',
                    ),
                  // Title or Custom Title
                  customTitle ??
                      SizedBox(
                        child: Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                ],
              ),

              // Actions or Settings Icon
              Row(
                children: [
                  if (actions != null) ...actions!,
                  if (onSettingsPressed != null)
                    IconButton(
                      onPressed: onSettingsPressed,
                      icon: const Icon(
                        Icons.settings,
                        color: AppColors.background,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
