import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/car_parts_selector_controller.dart';
import '../models/car_part.dart';
import 'car_parts_painter.dart';

class CarSelectorPartsWidget extends StatelessWidget {
  final CarPartsSelectorController controller;
  final Function(CarPart)? onPartSelected;

  const CarSelectorPartsWidget({
    Key? key,
    required this.controller,
    this.onPartSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildViewSelector(),
        const SizedBox(height: 16),
        Expanded(
          child: _buildCarPartsView(),
        ),
        const SizedBox(height: 16),
        _buildControls(),
      ],
    );
  }

  Widget _buildViewSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildViewButton(CarView.front, 'front_view'.tr, Icons.arrow_upward),
        _buildViewButton(CarView.leftSide, 'left_side'.tr, Icons.arrow_back),
        _buildViewButton(
            CarView.rightSide, 'right_side'.tr, Icons.arrow_forward),
        _buildViewButton(CarView.back, 'back_view'.tr, Icons.arrow_downward),
        _buildViewButton(CarView.top, 'top_view'.tr, Icons.arrow_drop_down),
      ],
    );
  }

  Widget _buildViewButton(CarView view, String label, IconData icon) {
    // Use GetX instead of Obx for more specific reactivity
    return GetX<CarPartsSelectorController>(builder: (_) {
      final isSelected = controller.currentView.value == view;
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: () => controller.setView(view),
          icon: Icon(
            icon,
            color: isSelected ? Colors.white : null,
          ),
          label: Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : null,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isSelected ? Theme.of(Get.context!).primaryColor : null,
            foregroundColor: isSelected ? Colors.white : null,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildCarPartsView() {
    return GetX<CarPartsSelectorController>(builder: (_) {
      controller.allParts.value;
      return GestureDetector(
        onTapDown: (details) => _handleTap(details.localPosition),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return CustomPaint(
                  size: Size(constraints.maxWidth, constraints.maxHeight),
                  painter: CarPartsPainter(
                    parts: controller.allParts,
                    currentView: controller.currentView.value,
                    showLabels: controller.showLabels.value,
                  ),
                );
              },
            ),
          ),
        ),
      );
    });
  }

  Widget _buildControls() {
    return GetX<CarPartsSelectorController>(builder: (_) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Toggle labels button
          ElevatedButton.icon(
            onPressed: () => controller.toggleLabels(),
            icon: Icon(
              controller.showLabels.value ? Icons.label_off : Icons.label,
            ),
            label: Text(
              controller.showLabels.value ? 'hide_labels'.tr : 'show_labels'.tr,
            ),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Clear selections button
          ElevatedButton.icon(
            onPressed: () => controller.clearSelections(),
            icon: const Icon(Icons.clear_all),
            label: Text('clear_all'.tr),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade100,
              foregroundColor: Colors.red.shade900,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      );
    });
  }

  void _handleTap(Offset position) {
    // Get the context and size
    final context = Get.context;
    if (context == null) return;

    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final size = renderBox.size;

    // Create a painter to do hit testing
    final painter = CarPartsPainter(
      parts: controller.allParts,
      currentView: controller.currentView.value,
      showLabels: controller.showLabels.value,
    );

    // Do hit testing
    final hitPart = painter.findHitPart(position, size);
    if (hitPart != null) {
      // Toggle selection
      controller.togglePartSelection(hitPart.id);

      // Call callback if provided
      onPartSelected?.call(hitPart);

      // Show part details dialog
      _showPartDetailsDialog(hitPart);
    }
  }

  void _showPartDetailsDialog(CarPart part) {
    final TextEditingController notesController = TextEditingController(
      text: part.notes,
    );

    Get.dialog(
      AlertDialog(
        title: Text(part.name.tr),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Damage level slider
              Text('damage_level'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              GetX<CarPartsSelectorController>(builder: (_) {
                final damageLevel = controller.damageLevels[part.id] ?? 0;
                return Column(
                  children: [
                    Slider(
                      value: damageLevel.toDouble(),
                      min: 0,
                      max: 5,
                      divisions: 5,
                      label: _getDamageLevelLabel(damageLevel),
                      onChanged: (value) {
                        controller.setDamageLevel(part.id, value.toInt());
                      },
                    ),
                    Text(
                      _getDamageLevelLabel(damageLevel),
                      style: TextStyle(
                        color: DamageLevel.values[damageLevel].color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              }),
              const SizedBox(height: 16),
              // Notes text field
              Text('notes'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              TextField(
                controller: notesController,
                decoration: InputDecoration(
                  hintText: 'enter_notes'.tr,
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (value) {
                  controller.setPartNotes(part.id, value);
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  String _getDamageLevelLabel(int level) {
    switch (level) {
      case 0:
        return 'none'.tr;
      case 1:
        return 'minor'.tr;
      case 2:
        return 'moderate'.tr;
      case 3:
        return 'major'.tr;
      case 4:
        return 'severe'.tr;
      case 5:
        return 'totaled'.tr;
      default:
        return 'unknown'.tr;
    }
  }
}
