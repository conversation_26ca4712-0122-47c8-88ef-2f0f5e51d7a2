import 'package:get/get.dart';
import '../controllers/reports_controller.dart';
import '../controller/problem_reports_controller.dart';
import '../../../services/car_service.dart';
import '../../../services/sector_service.dart';
import '../../../services/problem_tag_service.dart';
import '../../../services/export/export.dart';
import '../../../services/export/pdf_export_service.dart';
import '../../../services/export/excel_export_service.dart';

class ReportsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ReportsController());
    Get.lazyPut(() => ProblemReportsController(), fenix: true);
    Get.lazyPut(() => CarService());
    Get.lazyPut(() => SectorService());
    Get.lazyPut(() => ProblemTagService());
    Get.lazyPut(() => FileExportService());
    Get.lazyPut(() => PdfExportService());
    Get.lazyPut(() => ExcelExportService());
  }
}
