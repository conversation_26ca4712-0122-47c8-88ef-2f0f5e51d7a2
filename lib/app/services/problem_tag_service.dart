import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../data/models/problem_tag_model.dart';

class ProblemTagService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RxList<ProblemTag> tags = <ProblemTag>[].obs;
  final RxBool isLoading = false.obs;
  
  // Collection reference
  CollectionReference get tagsCollection => _firestore.collection('problem_tags');
  
  // Initialize the service
  Future<ProblemTagService> init() async {
    await loadTags();
    return this;
  }
  
  // Load all tags
  Future<void> loadTags() async {
    isLoading.value = true;
    try {
      final snapshot = await tagsCollection
          .where('isActive', isEqualTo: true)
          .orderBy('category')
          .orderBy('name')
          .get();
      
      tags.value = snapshot.docs.map((doc) {
        return ProblemTag.fromJson({...doc.data() as Map<String, dynamic>, 'id': doc.id});
      }).toList();
    } catch (e) {
      print('Error loading problem tags: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // Add a new tag
  Future<String> addTag(ProblemTag tag) async {
    try {
      final docRef = await tagsCollection.add(tag.toJson());
      await loadTags(); // Refresh the list
      return docRef.id;
    } catch (e) {
      print('Error adding problem tag: $e');
      return '';
    }
  }
  
  // Update a tag
  Future<bool> updateTag(ProblemTag tag) async {
    try {
      await tagsCollection.doc(tag.id).update({
        'name': tag.name,
        'category': tag.category,
        'isActive': tag.isActive,
        'updatedAt': DateTime.now(),
      });
      await loadTags(); // Refresh the list
      return true;
    } catch (e) {
      print('Error updating problem tag: $e');
      return false;
    }
  }
  
  // Delete a tag (soft delete by setting isActive to false)
  Future<bool> deleteTag(String tagId) async {
    try {
      await tagsCollection.doc(tagId).update({
        'isActive': false,
        'updatedAt': DateTime.now(),
      });
      await loadTags(); // Refresh the list
      return true;
    } catch (e) {
      print('Error deleting problem tag: $e');
      return false;
    }
  }
  
  // Increment usage count for a tag
  Future<bool> incrementUsageCount(String tagId) async {
    try {
      await tagsCollection.doc(tagId).update({
        'usageCount': FieldValue.increment(1),
        'updatedAt': DateTime.now(),
      });
      return true;
    } catch (e) {
      print('Error incrementing usage count: $e');
      return false;
    }
  }
  
  // Get tags by category
  List<ProblemTag> getTagsByCategory(String category) {
    return tags.where((tag) => tag.category == category).toList();
  }
  
  // Get all categories
  List<String> getAllCategories() {
    return tags.map((tag) => tag.category).toSet().toList();
  }
  
  // Get most used tags
  List<ProblemTag> getMostUsedTags({int limit = 10}) {
    final sortedTags = List<ProblemTag>.from(tags);
    sortedTags.sort((a, b) => b.usageCount.compareTo(a.usageCount));
    return sortedTags.take(limit).toList();
  }
  
  // Search tags by name
  List<ProblemTag> searchTags(String query) {
    if (query.isEmpty) return tags;
    return tags.where((tag) => 
      tag.name.toLowerCase().contains(query.toLowerCase()) ||
      tag.category.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}
