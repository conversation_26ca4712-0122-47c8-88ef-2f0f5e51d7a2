import 'package:cars_app/app/modules/settings/controllers/settings_controller.dart';
import 'package:cars_app/app/services/notification_service.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../controllers/home_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {

    Get.lazyPut(() => SettingsController(),);
    Get.lazyPut(() => NotificationService(),);
    Get.lazyPut<HomeController>(
      () => HomeController(),
    );
  }
}
