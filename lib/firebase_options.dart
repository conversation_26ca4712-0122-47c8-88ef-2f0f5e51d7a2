// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBORFN2IT7sjQphieDiEaEeoOE5Te5lo-Y',
    appId: '1:569131583537:android:51de8e4c9b3eb1a8426d10',
    messagingSenderId: '569131583537',
    projectId: 'srca-car-maintenance',
    storageBucket: 'srca-car-maintenance.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDknEl3hvjk-wcEyUSw2UP_zybnTOnxPlI',
    appId: '1:569131583537:ios:7e26c4156b66337b426d10',
    messagingSenderId: '569131583537',
    projectId: 'srca-car-maintenance',
    storageBucket: 'srca-car-maintenance.firebasestorage.app',
    iosBundleId: 'com.example.carsApp',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB9eqs8gjr1BdOrOuN74nD7glureOB5Lpg',
    appId: '1:569131583537:web:d4fc201f55cc91ef426d10',
    messagingSenderId: '569131583537',
    projectId: 'srca-car-maintenance',
    authDomain: 'srca-car-maintenance.firebaseapp.com',
    storageBucket: 'srca-car-maintenance.firebasestorage.app',
    measurementId: 'G-E4FNXXDY67',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDknEl3hvjk-wcEyUSw2UP_zybnTOnxPlI',
    appId: '1:569131583537:ios:7e26c4156b66337b426d10',
    messagingSenderId: '569131583537',
    projectId: 'srca-car-maintenance',
    storageBucket: 'srca-car-maintenance.firebasestorage.app',
    iosBundleId: 'com.example.carsApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyB9eqs8gjr1BdOrOuN74nD7glureOB5Lpg',
    appId: '1:569131583537:web:05b9a43503496ba4426d10',
    messagingSenderId: '569131583537',
    projectId: 'srca-car-maintenance',
    authDomain: 'srca-car-maintenance.firebaseapp.com',
    storageBucket: 'srca-car-maintenance.firebasestorage.app',
    measurementId: 'G-8W3VVHP5XS',
  );

}