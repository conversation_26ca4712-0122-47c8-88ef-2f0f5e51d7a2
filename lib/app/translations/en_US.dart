final Map<String, String> enUS = {
  'login': 'Login',
  'email': 'Email',
  'password': 'Password',
  'welcome': 'Welcome',
  'home': 'Home',
  'cars': 'Cars',
  'reports': 'Reports',
  'settings': 'Settings',
  'profile': 'Profile',
  'users': 'Users',
  'sectors': 'Sectors',
  'logout': 'Logout',
  'change_language': 'Change Language',
  'language': 'Language',
  'appearance': 'Appearance',
  'light_theme': 'Light Theme',
  'dark_theme': 'Dark Theme',
  'system_theme': 'System Theme',
  'light_theme_desc': 'Use light colors',
  'dark_theme_desc': 'Use dark colors',
  'system_theme_desc': 'Follow system setting',
  'preview': 'Preview',
  'search': 'Search',
  'sendToLogisticsSupport': 'Send To Logistics Support',
  'no_data': 'No Data Available',
  'error': 'Error',
  'rotations':'Rotations',
  'deliveryToSector': 'Delivery To Sector',
  'sentRequest': 'Sent Request',
  'success': 'Success',
  'confirm': 'Confirm',
  'history': 'History',
  'send_to_workshop': 'Send To Workshop',
  'cancel': 'Cancel',
  'problem_description': 'Problem Description',
  'save': 'Save',
  'edit': 'Edit',
  'delete': 'Delete',
  'add': 'Add',
  'refresh': 'Refresh',
  
  // Splash Screen
  'powered_by': 'Powered by',
  'copyright': 'All rights reserved',
  
  // Dashboard
  'dashboard': 'Dashboard',
  'filters': 'Filters',
  'sector': 'Sector',
  'status': 'Status',
  'reset': 'Reset',
  'total_cars': 'Total Cars',
  'in_maintenance': 'In Maintenance',
  'in_workshop': 'In Workshop',
  'active_cars': 'Active Cars',
  'cars_by_status': 'Cars by Status',
  'cars_by_sector': 'Cars by Sector',
  'users_by_role': 'Users by Role',
  'recent_activities': 'Recent Activities',
  'maintenance': 'Maintenance',
  'workshop': 'Workshop',
  'active': 'Active',
  'inactive': 'Inactive',
  'admin': 'Admin',
  'manager': 'Manager',
  'user': 'User',
  'unknown': 'Unknown',
  'unassigned': 'Unassigned',
  'all': 'All',


  'has_replacement': 'Is there a replacement?',
  'yes': 'Yes',
  'no': 'No',
  'maintenance_type': 'Maintenance Type',
  'urgent': 'Urgent',
  'routine_maintenance': 'Routine Maintenance',
  'breakdown_repair': 'Breakdown Repair',
  'accident_repair': 'Accident Repair',
  'edit_workshop_entry': 'Edit Workshop Entry',
  'new_workshop_entry': 'New Workshop Entry',
  'car_details': 'Car Details',
  'plate': 'Plate',
  'model': 'Model',
  'type': 'Type',
  'status': 'Status',
  'current_status': 'Current Status',
  'maintenance': 'Maintenance',
  'in_workshop': 'In Workshop',
  'completed': 'Completed',
  'services': 'Services',
  'add_service': 'Add Service',
  'cost': 'Cost',
  'receiver_information': 'Receiver Information',
  'select_receiver': 'Select Receiver',
  'please_select_receiver': 'Please select a receiver',
  'update_entry': 'Update Entry',
  'create_entry': 'Create Entry',
  'date': 'Date',
  'maintenance_notes': 'Maintenance Notes',
  'in_workshop_notes': 'In Workshop Notes',
  'completed_notes': 'Completed Notes',
  'please_enter_maintenance_notes': 'Please enter maintenance notes',
  'service_name': 'Service Name',
  'description': 'Description',
  'pending': 'Pending',
  'rejected_via': 'Rejected via',
  'in_progress': 'In Progress',
  'callToWorkshop': 'Call to Workshop',

  'cancel': 'Cancel',
  'add': 'Add',
  'error': 'Error',
  'failed_to_save_entry': 'Failed to save workshop entry',
  'success': 'Success',
  'entry_saved': 'Workshop entry saved successfully',

};
