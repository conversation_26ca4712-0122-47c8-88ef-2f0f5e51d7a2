import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/services/car_rotation_service.dart';
import 'package:cars_app/app/services/car_service.dart';
import 'package:cars_app/app/widgets/awesome_snackbar_content.dart';
import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/car_model.dart';
import '../modules/car/view/car_history_view.dart';
import '../routes/app_pages.dart';
import '../themes/app_colors.dart';
import 'chat_button.dart';

class SectorCarCard extends StatelessWidget {
   SectorCarCard({super.key, required this.car, required this.confirmAction, required this.cancelAction, required this.confirmCallCarToWorkshop, required this.rejectCallCarToWorkshop, required this.uid, required this.sectorId});
  final Car car;
  final String uid;
  final String sectorId;
  final VoidCallback  confirmAction;
  final VoidCallback  confirmCallCarToWorkshop;
  final VoidCallback  rejectCallCarToWorkshop;
  final VoidCallback  cancelAction;
  @override
  Widget build(BuildContext context) {
    bool isTempCar = car.tempSectorId.isNotEmpty&&sectorId!=car.tempSectorId;
    return Card(
      color:isTempCar?Colors.blueGrey[100]: AppColors.background,
      elevation: 4,
      shadowColor: Colors.black.withOpacity(0.1),
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.withOpacity(0.1)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Hero Animation for Status Icon
                Hero(
                  tag: 'car_status_${car.plateNumber}',
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getStatusColor(car.status).withOpacity(0.15),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: _getStatusColor(car.status).withOpacity(0.2),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: _buildStatusIcon(car),
                  ),
                ),
                Text(
                  '${car.plateCharacters}-${car.plateNumber}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    letterSpacing: 0.5,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(width: 16), // Spacing between content and popup menu
                Visibility(
                    visible: !isTempCar,
                    child: _buildPopupMenu()),
              ],
            ),
            const SizedBox(height: 8),


            Visibility(
                visible: car.status!=CarStatus.active,
                child: ChatButton(car: car,receiverId:uid,senderId: '',)),
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plate Number
                // if car status is active, show the process time line else show the car status

                // Chips for Car Details
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [

                      _buildChip(
                        icon: Icons.color_lens,
                        label: car.status.name.tr,
                        color: Colors.pink,
                      ),
                      const SizedBox(width: 8),
                      _buildChip(
                        icon: Icons.category,
                        label: car.carType,
                        color: Colors.teal,
                      ),
                      const SizedBox(width: 8),

                      _buildChip(
                        icon: Icons.directions_car,
                        label: car.carModel,
                        color: Theme.of(context).primaryColor,
                      ),
                    ],
                  ),
                ),

                Visibility(
                  visible: car.status == CarStatus.deliveryToSector,
                  child: SizedBox(
                    child: Column(
                      children: [
                        Divider(),
                        Text('طلب استلام المركبة من الدعم اللوجستي',style: TextStyle(fontWeight: FontWeight.bold),),
                        CustomButton(
                          isSwipe: true,
                          text: 'تأكيد استلام المركبة', onPressed:confirmAction

                          ,),
                        SizedBox(height: 10,),
                        CustomButton(
                          isSwipe: true,
                          text: 'رفض استلام المركبة', onPressed:cancelAction,),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: car.status == CarStatus.callToWorkshop,
                  child: SizedBox(
                    child: Column(
                      children: [
                        Divider(),

                        Text('طلب استدعاء المركبة من الورشة',style: TextStyle(fontWeight: FontWeight.bold),),
                        CustomButton(
                          isSwipe: true,
                          text: 'موافق', onPressed:confirmCallCarToWorkshop

                          ,),
                        SizedBox(height: 10,),
                        CustomButton(
                          isSwipe: true,
                          text: 'غير موافق', onPressed:rejectCallCarToWorkshop,),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 10,),


                Visibility(
                  visible: isTempCar,
                  child: car.action!=CarAction.restore? CustomButton(
                    isSwipe: true,
                    text: 'اعاده مركبة'.tr, onPressed: () async {

                    CarService carService = Get.find();
                    CarRotationService carRotationService = Get.find<CarRotationService>();
                    await carService.updateCar(car.copyWith(
                      action: CarAction.restore
                    ));
                    await carRotationService.createCarRotation(
                      car: car,
                      cause: '',
                      action: RotateAction.restore,
                      toSector: RotateStatus.accepted,
                      isActive: false,
                      fromSectorId: car.tempSectorId,
                      fromSectorName: car.tempSectorName,
                      toSectorId: car.sectorId,
                      toSectorName: car.sectorName,
                      type:RotateType.permanent,
                    );
                    showCustomSnackBar(
                        title: 'ارسال الطلب',
                        message: 'تم أرسال الطلب بنجاح', contentType: ContentType.success);

                    // Get.toNamed(Routes.CarRotationDashboard,arguments: sectorId);

                  },):Container(
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.green,width: 2),
                    ),
                      child: Text('تم أرسال الطلب بنجاح',style: TextStyle(color: Colors.black),)),
                ),

                // car.tempSectorId.isNotEmpty&&sectorId!=car.tempSectorId?Text('data'):SizedBox(),
                // buildRotationNotificationCard(car,uid)
              ],
            ),

          ],
        ),
      ),
    );
  }
   Widget buildRotationNotificationCard(Car car, String currentUserSectorId) {
     if (car.tempSectorId.isEmpty || car.rotations.isEmpty) return SizedBox();

     final lastRotation = car.rotations.last;
     final isFromSector = lastRotation.fromSectorId == currentUserSectorId;
     final isToSector = lastRotation.toSectorId == currentUserSectorId;

     final fromAgree = lastRotation.isFromSectorMangerAgree;
     final toAgree = lastRotation.isToSectorMangerAgree;

     RotateStatus otherStatus = isFromSector ? toAgree : fromAgree;
     RotateStatus myStatus = isFromSector ? fromAgree : toAgree;

     String otherSectorName = isFromSector ? lastRotation.toSectorName : lastRotation.fromSectorName;

     // Messages based on both statuses:
     if (myStatus == RotateStatus.accepted && otherStatus == RotateStatus.accepted) {
       return buildNotificationCard("Rotation fully approved by both sectors ✅");
     } else if (myStatus == RotateStatus.rejected) {
       return buildNotificationCard("You have rejected the rotation ❌");
     } else if (otherStatus == RotateStatus.rejected) {
       return buildNotificationCard("$otherSectorName manager has rejected the rotation ❌");
     } else if (myStatus == RotateStatus.accepted && otherStatus == RotateStatus.pending) {
       return buildNotificationCard("You approved, waiting for $otherSectorName manager’s approval ⏳");
     } else if (myStatus == RotateStatus.pending && otherStatus == RotateStatus.accepted) {
       return buildActionCard(
         "Rotation approved by $otherSectorName manager, awaiting your decision",
         onAccept: () async {
           List<CarRotate> rotations = car.rotations;
           // rotations[rotations.length - 1] = rotations[rotations.length - 1].copyWith(
           //   isFromSectorMangerAgree: RotateStatus.accepted,
           //   isToSectorMangerAgree: RotateStatus.accepted,
           // );

         },

         onReject: () async {


           // await FirebaseFirestore.instance.collection('cars').doc(car.id).update({
           //   'rotations.${car.rotations.length - 1}.${isFromSector ? 'isFromSectorMangerAgree' : 'isToSectorMangerAgree'}': RotateStatus.rejected,
           // });
         },

       );
     } else if (myStatus == RotateStatus.pending && otherStatus == RotateStatus.pending) {
       return buildActionCard(
         "Rotation request is pending, waiting for your response",
         onAccept: () {
           // Add your agree logic here
         },
         onReject: () {
           // Add your reject logic here
         },
       );
     }

     return SizedBox();
   }

   Widget buildNotificationCard(String message) {
     return Card(
       margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
       color: Colors.grey.shade100,
       child: Padding(
         padding: const EdgeInsets.all(12),
         child: Row(
           children: [
             Icon(Icons.notifications, color: Colors.blue),
             SizedBox(width: 10),
             Expanded(child: Text(message, style: TextStyle(fontSize: 16))),
           ],
         ),
       ),
     );
   }

   Widget buildActionCard(String message, {required VoidCallback onAccept, required VoidCallback onReject}) {
     return Card(
       margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
       color: Colors.orange.shade50,
       child: Padding(
         padding: const EdgeInsets.all(12),
         child: Column(
           crossAxisAlignment: CrossAxisAlignment.start,
           children: [
             Row(
               children: [
                 Icon(Icons.notifications_active, color: Colors.orange),
                 SizedBox(width: 10),
                 Expanded(child: Text(message, style: TextStyle(fontSize: 16))),
               ],
             ),
             SizedBox(height: 10),
             Row(
               mainAxisAlignment: MainAxisAlignment.end,
               children: [
                 TextButton.icon(
                   onPressed: onReject,
                   icon: Icon(Icons.cancel, color: Colors.red),
                   label: Text("Reject", style: TextStyle(color: Colors.red)),
                 ),
                 SizedBox(width: 10),
                 ElevatedButton.icon(
                   onPressed: onAccept,
                   icon: Icon(Icons.check, color: Colors.white),
                   label: Text("Accept"),
                   style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                 ),
               ],
             )
           ],
         ),
       ),
     );
   }


  // Popup Menu for Actions
  Widget _buildPopupMenu() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: AppColors.textSecondary),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'workshop',
          enabled: car.status == CarStatus.active,
          child:  Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.build, color: AppColors.textSecondary),
              SizedBox(width: 8),
              Text('send_to_workshop'.tr, style: TextStyle(color: AppColors.textPrimary)),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'history',
          enabled: car.workshopHistory.isNotEmpty&&car.workshopHistory.first.statuses.isNotEmpty,
          child:  Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.edit, color: AppColors.textSecondary),
              SizedBox(width: 8),
              Text('history'.tr, style: TextStyle(color: AppColors.textPrimary)),
            ],
          ),
        ),
      ],
      onSelected: (value) async {
        switch (value) {
          case 'workshop':
            Get.toNamed(Routes.WORKSHOP_FORM, arguments: {'car': car,'isMaintenance':true});
            break;
          case 'history':
            Get.to(CarHistoryView(car: car));
            break;
        }
      },
    );
  }

  // Chip Widget for Car Details
  Widget _buildChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Status Color Based on Car Status
  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return Colors.green;
      case CarStatus.maintenance:
        return Colors.orange;
      case CarStatus.inWorkshop:
        return Colors.red;
      case CarStatus.destroyed:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }


  // Status Icon Based on Car Status
  Widget _buildStatusIcon(Car car) {
    IconData icon;
    Color color;
    CarStatus status = car.status;
    if(car.tempSectorId.isNotEmpty){
      icon = Icons.location_on;
      color = Colors.blue;
      return Icon(icon, color: color);
    }
    switch (status) {
      case CarStatus.active:
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case CarStatus.maintenance:
        icon = Icons.build;
        color = Colors.orange;
        break;
        case CarStatus.sentRequest:
        icon = Icons.request_page;
        color = Colors.pinkAccent;
        case CarStatus.sendGroup:
        icon = Icons.group;
        color = Colors.black;
        break;
        case CarStatus.pending:
        icon = Icons.accessibility_new;
        color = Colors.white;
        break;
      case CarStatus.inWorkshop:
        icon = Icons.car_repair;
        color = Colors.red;
        break;
        case CarStatus.receipt:
        icon = Icons.input;
        color = Colors.brown;
        break;
      case CarStatus.destroyed:
        icon = Icons.dangerous;
        color = Colors.red[900]!;
        break;
      default:
        icon = Icons.error;
        color = Colors.grey;
    }
    return Icon(icon, color: color);
  }
}