import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/car_model.dart';
import '../data/models/car_rotate.dart';
import '../themes/app_colors.dart';
import 'package:intl/intl.dart';

class CarRotationCard extends StatelessWidget {
  final Car? car;
  final CarRotate rotation;
  final bool isFromSectorManager;
  final bool isToSectorManager;
  final Function(CarRotate, RotateStatus) onStatusUpdate;

  const CarRotationCard({
    Key? key,
    required this.car,
    required this.rotation,
    required this.isFromSectorManager,
    required this.isToSectorManager,
    required this.onStatusUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool canTakeAction = (isFromSectorManager && rotation.isFromSectorMangerAgree == RotateStatus.pending) ||
        (isToSectorManager && rotation.isToSectorMangerAgree == RotateStatus.pending);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.grey.shade50,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildRotationTypeChip(),
                  const Spacer(),
                  Text(
                    DateFormat('dd/MM/yyyy HH:mm').format(rotation.agreedByFromSectorManagerAt ?? DateTime.now()),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              car != null ? _buildCarInfo() : const SizedBox.shrink(),
              const SizedBox(height: 16),
              _buildSectorInfo(),
              if (canTakeAction) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                _buildActionButtons(),
              ] else ...[
                const SizedBox(height: 16),
                _buildStatusInfo(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRotationTypeChip() {
    final bool isTemporary = rotation.type == RotateType.temporary;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: (isTemporary ? AppColors.warning : AppColors.primary).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isTemporary ? Icons.timer : Icons.swap_horiz,
            size: 16,
            color: isTemporary ? AppColors.warning : AppColors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            isTemporary ? 'temporary_rotation'.tr : 'permanent_rotation'.tr,
            style: TextStyle(
              color: isTemporary ? AppColors.warning : AppColors.primary,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'car_info'.tr,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.directions_car, size: 20, color: AppColors.primary.withOpacity(0.7)),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${car!.plateNumber} - ${car!.carType}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectorInfo() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'from_sector'.tr,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                rotation.fromSectorName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              _buildStatusChip(rotation.isFromSectorMangerAgree),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Icon(
            Icons.arrow_forward,
            color: AppColors.primary.withOpacity(0.7),
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'to_sector'.tr,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                rotation.toSectorName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              _buildStatusChip(rotation.isToSectorMangerAgree),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(RotateStatus status) {
    Color color;
    String text;

    switch (status) {
      case RotateStatus.pending:
        color = AppColors.warning;
        text = 'pending'.tr;
        break;
      case RotateStatus.accepted:
        color = AppColors.success;
        text = 'accepted'.tr;
        break;
      case RotateStatus.rejected:
        color = Colors.red;
        text = 'rejected'.tr;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () => onStatusUpdate(rotation, RotateStatus.accepted),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('accept'.tr),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextButton(
            onPressed: () => onStatusUpdate(rotation, RotateStatus.rejected),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: const BorderSide(color: Colors.red),
              ),
            ),
            child: Text('reject'.tr),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusInfo() {
    final bool isCompleted = rotation.isFromSectorMangerAgree == RotateStatus.accepted &&
        rotation.isToSectorMangerAgree == RotateStatus.accepted;

    final bool isRejected = rotation.isFromSectorMangerAgree == RotateStatus.rejected ||
        rotation.isToSectorMangerAgree == RotateStatus.rejected ||
        rotation.managerAgreeStatus == RotateStatus.rejected;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (isCompleted ? AppColors.success : isRejected ? Colors.red : AppColors.warning).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: (isCompleted ? AppColors.success : isRejected ? Colors.red : AppColors.warning).withOpacity(0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            isCompleted ? Icons.check_circle : isRejected ? Icons.cancel : Icons.pending,
            color: isCompleted ? AppColors.success : isRejected ? Colors.red : AppColors.warning,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isCompleted
                      ? 'rotation_completed'.tr
                      : isRejected
                      ? 'rotation_rejected'.tr
                      : 'waiting_for_approval'.tr,
                  style: TextStyle(
                    color: isCompleted ? AppColors.success : isRejected ? Colors.red : AppColors.warning,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (isRejected)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      getRejectSource(rotation),
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  String getRejectSource(CarRotate rotation) {
    if (rotation.managerAgreeStatus == RotateStatus.rejected) {
      return '${'rejected_via'.tr} ${'المشرف'.tr}';
    } else if (rotation.isFromSectorMangerAgree == RotateStatus.rejected) {
      return '${'rejected_via'.tr} ${rotation.fromSectorName.tr}';
    } else {
      return '${'rejected_via'.tr} ${rotation.toSectorName.tr}';
    }
  }
} 