import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';

class MaintenanceCard extends StatelessWidget {
  final Car car;
  final WorkshopEntry maintenance;
  final Function()? onTap;
  final bool isRTL;

  const MaintenanceCard({
    Key? key,
    required this.car,
    required this.maintenance,
    this.onTap,
    this.isRTL = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isCompleted = maintenance.status==CarStatus.active;
    final latestStatus = maintenance.statuses.isNotEmpty
        ? maintenance.statuses.last
        : null;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: isCompleted
                ? AppColors.success.withOpacity(0.3)
                : AppColors.warning.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            Row(
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              children: [
                _buildStatusIcon(isCompleted),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: isRTL
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.start,
                    children: [
                      Text(
                        car.plateCharacters+' - '+car.plateNumber,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${car.carModel} - ${car.plateNumber}',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(isCompleted),
              ],
            ),
            const SizedBox(height: 12),
            Divider(color: Colors.grey[200]),
            const SizedBox(height: 12),
            Row(
              textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildInfoItem(
                  Icons.build,
                  maintenance.statuses.last.notes ?? 'maintenance'.tr,
                  AppColors.primary,
                ),
                // if (maintenance.cost != null && maintenance.cost! > 0)
                //   _buildInfoItem(
                //     Icons.attach_money,
                //     '${maintenance.cost} SAR',
                //     AppColors.success,
                //   ),
                if (latestStatus != null)
                  _buildInfoItem(
                    Icons.access_time,
                    timeago.format(latestStatus.createAt),
                    AppColors.info,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon(bool isCompleted) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: isCompleted
            ? AppColors.success.withOpacity(0.1)
            : AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Icon(
        isCompleted ? Icons.check_circle : Icons.pending,
        color: isCompleted ? AppColors.success : AppColors.warning,
        size: 24,
      ),
    );
  }

  Widget _buildStatusBadge(bool isCompleted) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isCompleted
            ? AppColors.success.withOpacity(0.1)
            : AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted
              ? AppColors.success.withOpacity(0.3)
              : AppColors.warning.withOpacity(0.3),
        ),
      ),
      child: Text(
        isCompleted ? 'completed'.tr : 'in_progress'.tr,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: isCompleted ? AppColors.success : AppColors.warning,
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}
