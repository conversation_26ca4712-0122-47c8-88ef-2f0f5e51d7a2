import 'package:cars_app/app/controllers/auth_controller.dart';
import 'package:cars_app/app/modules/notifications/controllers/notifications_controller.dart';
import 'package:cars_app/app/services/problem_tag_service.dart';
import 'package:cars_app/app/services/user_service.dart';
import 'package:cars_app/app/services/user_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'car_service.dart';
import 'storage_service.dart';
import 'firestore_service.dart';
import 'notification_service_selector.dart';

class ServiceBindings extends Bindings {
  @override
  void dependencies() async {
    Get.lazyPut(() => AuthController(), fenix: false);

    await Get.putAsync<LocalStorageService>(permanent: true, () async {
      final localStorageService = LocalStorageService();
      return localStorageService;
    });

    // Initialize StorageService if not already initialized
    if (!Get.isRegistered<StorageService>()) {
      Get.putAsync<StorageService>(() async {
        final storageService = StorageService();
        await storageService.init();
        return storageService;
      }, permanent: true);
    }

    Get.lazyPut(() => FirebaseFirestore.instance, fenix: true);
    Get.lazyPut<CarService>(() => CarService(), fenix: true);
    Get.lazyPut(() => NotificationsController(), fenix: true);
    // Initialize FirestoreService if not already initialized
    if (!Get.isRegistered<FirestoreService>()) {
      Get.put<FirestoreService>(
        FirestoreService(),
        permanent: true,
      );
    }

    // Services
    Get.lazyPut(() => NotificationService().init(), fenix: true);

    // Initialize ProblemTagService
    Get.putAsync<ProblemTagService>(() async {
      final tagService = ProblemTagService();
      await tagService.init();
      return tagService;
    }, permanent: true);
  }
}
