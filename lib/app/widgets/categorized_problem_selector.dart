import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/problem_tag_model.dart';
import '../services/problem_tag_service.dart';
import '../themes/app_colors.dart';

// State controller for the problem selector
class CategorySelectorController extends GetxController {
  final RxString selectedCategory = ''.obs;

  void setCategory(String category) {
    selectedCategory.value = category;
  }
}

class CategorizedProblemSelector extends StatefulWidget {
  final RxList<String> selectedTagIds;
  final Function(List<String>) onTagsChanged;
  final String hintText;

  const CategorizedProblemSelector({
    Key? key,
    required this.selectedTagIds,
    required this.onTagsChanged,
    this.hintText = 'اختر المشاكل الشائعة',
  }) : super(key: key);

  @override
  State<CategorizedProblemSelector> createState() =>
      _CategorizedProblemSelectorState();
}

class _CategorizedProblemSelectorState
    extends State<CategorizedProblemSelector> {
  final ProblemTagService _tagService = Get.find<ProblemTagService>();
  final RxString _selectedCategory = RxString('');
  final RxBool _isAddingNewTag = false.obs;
  final TextEditingController _newTagController = TextEditingController();
  final TextEditingController _newCategoryController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Load tags if not already loaded
    if (_tagService.tags.isEmpty) {
      _tagService.loadTags();
    }
  }

  @override
  void dispose() {
    _newTagController.dispose();
    _newCategoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProblemTagService>(
      init: _tagService,
      builder: (tagService) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Selected Tags Section
            _buildSelectedTagsSection(),

            // Categories Section
            if (tagService.isLoading.value)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (tagService.getAllCategories().isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    'لا توجد فئات متاحة',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ),
              )
            else
              _buildCategoriesSection(tagService.getAllCategories()),
          ],
        );
      },
    );
  }

  Widget _buildCategoriesSection(List<String> categories) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 12, top: 8),
          child: Text(
            'اختر الفئة:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),

        // Categories as horizontal scrollable chips
        SizedBox(
          height: 50,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length + 1, // +1 for "Add new" button
            separatorBuilder: (context, index) => const SizedBox(width: 8),
            itemBuilder: (context, index) {
              if (index == categories.length) {
                // Add new category button
                return ActionChip(
                  avatar: const Icon(Icons.add, color: AppColors.primary),
                  label: const Text('فئة جديدة'),
                  onPressed: _showAddCategoryDialog,
                  backgroundColor: Colors.white,
                  side: const BorderSide(color: AppColors.primary),
                  labelStyle: const TextStyle(color: AppColors.primary),
                );
              }

              final category = categories[index];
              final isSelected = _selectedCategory.value == category;

              return ChoiceChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (selected) {
                  _selectedCategory.value = selected ? category : '';
                },
                backgroundColor: Colors.white,
                selectedColor: AppColors.primary.withOpacity(0.2),
                labelStyle: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.black87,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Show subcategories/tags for selected category
        GetX<CategorizedProblemSelectorState>(
          init: CategorizedProblemSelectorState(_selectedCategory),
          builder: (state) {
            return state.selectedCategory.value.isNotEmpty
                ? _buildSubcategoriesSection()
                : const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'اختر فئة لعرض المشاكل المتعلقة بها',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  );
          },
        ),
      ],
    );
  }

  Widget _buildSelectedTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.primary.withOpacity(0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle,
                      color: AppColors.primary, size: 18),
                  const SizedBox(width: 8),
                  const Text(
                    'المشاكل المحددة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  if (widget.selectedTagIds.isNotEmpty)
                    TextButton.icon(
                      icon: const Icon(Icons.clear_all, size: 16),
                      label: const Text('مسح الكل'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      onPressed: () {
                        widget.selectedTagIds.clear();
                        widget.onTagsChanged(widget.selectedTagIds);
                      },
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.selectedTagIds.map((tagId) {
                  final tag =
                      _tagService.tags.firstWhereOrNull((t) => t.id == tagId) ??
                          ProblemTag(
                            id: tagId,
                            name: tagId, // Use ID as name if tag not found
                            category: 'custom',
                            createdAt: DateTime.now(),
                          );

                  return Chip(
                    label: Text(tag.name),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      widget.selectedTagIds.remove(tagId);
                      widget.onTagsChanged(widget.selectedTagIds);
                    },
                    backgroundColor: Colors.white,
                    side: BorderSide(color: AppColors.primary.withOpacity(0.3)),
                    labelStyle: const TextStyle(color: AppColors.primary),
                    deleteIconColor: AppColors.primary,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSubcategoriesSection() {
    final categoryTags = _tagService.tags
        .where((tag) => tag.category == _selectedCategory.value)
        .toList();

    if (categoryTags.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'لا توجد مشاكل في فئة "${_selectedCategory.value}"',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('إضافة مشكلة جديدة'),
            onPressed: () => _showAddTagDialog(_selectedCategory.value),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المشاكل في فئة "${_selectedCategory.value}":',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add_circle, color: AppColors.primary),
              onPressed: () => _showAddTagDialog(_selectedCategory.value),
              tooltip: 'إضافة مشكلة جديدة',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: categoryTags.map((tag) {
              final isSelected = widget.selectedTagIds.contains(tag.id);

              return FilterChip(
                label: Text(tag.name),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    widget.selectedTagIds.add(tag.id);
                    // Increment usage count in the background
                    _tagService.incrementUsageCount(tag.id);
                  } else {
                    widget.selectedTagIds.remove(tag.id);
                  }
                  widget.onTagsChanged(widget.selectedTagIds);
                },
                backgroundColor: Colors.grey[100],
                selectedColor: AppColors.primary.withOpacity(0.2),
                checkmarkColor: AppColors.primary,
                labelStyle: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.black87,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _showAddTagDialog(String category) {
    _newCategoryController.text = category;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مشكلة جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _newTagController,
              decoration: const InputDecoration(
                labelText: 'اسم المشكلة',
                hintText: 'أدخل اسم المشكلة',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newCategoryController,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                hintText: 'أدخل اسم الفئة',
              ),
              enabled: false, // Category is pre-selected
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _newTagController.clear();
              _isAddingNewTag.value = false;
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_newTagController.text.trim().isNotEmpty) {
                final newTag = ProblemTag(
                  id: '', // Will be set by Firestore
                  name: _newTagController.text.trim(),
                  category: _newCategoryController.text,
                  usageCount: 1, // Start with 1 since we're using it
                  createdAt: DateTime.now(),
                );

                // Store context before async gap
                final navigatorContext = context;

                final tagId = await _tagService.addTag(newTag);

                if (tagId.isNotEmpty) {
                  widget.selectedTagIds.add(tagId);
                  widget.onTagsChanged(widget.selectedTagIds);
                }

                _newTagController.clear();
                _isAddingNewTag.value = false;

                // Check if widget is still mounted before using context
                if (navigatorContext.mounted) {
                  Navigator.of(navigatorContext).pop();
                }
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showAddCategoryDialog() {
    final TextEditingController categoryController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة فئة جديدة'),
        content: TextField(
          controller: categoryController,
          decoration: const InputDecoration(
            labelText: 'اسم الفئة',
            hintText: 'أدخل اسم الفئة الجديدة',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (categoryController.text.trim().isNotEmpty) {
                _selectedCategory.value = categoryController.text.trim();
                Navigator.of(context).pop();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    ).then((_) {
      categoryController.dispose();
    });
  }
}
