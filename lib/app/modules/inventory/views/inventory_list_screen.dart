// inventory_list_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../themes/app_colors.dart';
import '../../../data/language_controller.dart';
import '../controllers/inventory_controller.dart';
import '../models/transaction_model.dart';
import '../widgets/error_display.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/product_list_item.dart';
import '../../../widgets/app_drawer.dart';

class InventoryListScreen extends GetView<InventoryController> {
  const InventoryListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return LayoutBuilder(
      builder: (context, constraints) {
        bool isDesktop = constraints.maxWidth > 900;
        bool isWide = constraints.maxWidth > 700;

        return Directionality(
          textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
          child: Scaffold(
            appBar: AppBar(
              title: Text('inventory_management'.tr),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              elevation: 0,
              centerTitle: true,
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.search),
                  onPressed: () {
                    // Implement search functionality
                  },
                ),
                IconButton(
                  icon: Icon(Icons.refresh),
                  onPressed: () => controller.refreshProducts(),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.background,
            // drawer: !isDesktop ? AppDrawer(selectedRoute: '/inventory') : null,
            body: Row(
              children: [
                // if (isDesktop)
                //   SizedBox(
                //     width: 240,
                //     child: AppDrawer(selectedRoute: '/inventory'),
                //   ),
                // if (isDesktop) const VerticalDivider(width: 1),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
                    child: Obx(() {
                      if (controller.isLoading.value) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                color: AppColors.primary,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'loading'.tr,
                                style: TextStyle(color: AppColors.textSecondary),
                              ),
                            ],
                          ),
                        );
                      }
                      if (controller.error.value.isNotEmpty) {
                        return ErrorDisplay(
                          errorMessage: controller.error.value,
                          onRetry: () => controller.refreshProducts(),
                        );
                      }
                      if (controller.products.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.network(
                                'https://assets5.lottiefiles.com/packages/lf20_ydo1amjm.json',
                                width: 180,
                                height: 180,
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 16,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      'no_products_found'.tr,
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'add_your_first_product'.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    ElevatedButton.icon(
                                      onPressed: () => controller.goToAddProduct(),
                                      icon: const Icon(Icons.add),
                                      label: Text('add_new_product'.tr),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppColors.primary,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                      // Main content: Filters, Stats, Product Grid/List
                      return SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildFilters(context, isWide),
                            const SizedBox(height: 24),
                            _buildStatCards(context, isWide),
                            const SizedBox(height: 24),
                            AnimatedSwitcher(
                              duration: const Duration(milliseconds: 350),
                              child: isWide
                                  ? _buildInventoryGrid(context)
                                  : _buildInventoryList(context),
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
            floatingActionButton: FloatingActionButton.extended(
              onPressed: () => controller.goToAddProduct(),
              icon: const Icon(Icons.add),
              label: Text('add_new_product'.tr),
              backgroundColor: AppColors.primary,
            ),
          ),
        );
      },
    );
  }

  // Inventory stats section
  Widget _buildInventoryStats() {
    // Determine if we need to use a responsive layout based on screen width
    final isSmallScreen = MediaQuery.of(Get.context!).size.width < 360;

    return Container(
      margin: EdgeInsets.all(16.r),
      child: StaggeredGrid.count(
        crossAxisCount: isSmallScreen ? 1 : 2,
        mainAxisSpacing: 12.h,
        crossAxisSpacing: 12.w,
        children: [
          StaggeredGridTile.count(
            crossAxisCellCount: 1,
            mainAxisCellCount: 1,
            child: _buildStatCard(
              title: 'total_products'.tr,
              value: '${controller.products.length}',
              icon: Icons.inventory_2,
              color: AppColors.primary,
            ),
          ),
          StaggeredGridTile.count(
            crossAxisCellCount: 1,
            mainAxisCellCount: 1,
            child: _buildStatCard(
              title: 'low_stock'.tr,
              value:
                  '${controller.products.where((p) => p.lowStockThreshold != null && p.currentQuantity <= p.lowStockThreshold!).length}',
              icon: Icons.warning_amber_rounded,
              color: AppColors.warning,
            ),
          ),
          StaggeredGridTile.count(
            crossAxisCellCount: isSmallScreen ? 1 : 2,
            mainAxisCellCount: isSmallScreen ? 1 : 0.6,
            child: _buildStatCard(
              title: 'total_inventory_value'.tr,
              value: '\$${_calculateInventoryValue().toStringAsFixed(2)}',
              icon: Icons.attach_money,
              color: AppColors.success,
              isWide: !isSmallScreen,
            ),
          ),
        ],
      ),
    );
  }

  // Helper to calculate total inventory value
  double _calculateInventoryValue() {
    double total = 0;
    for (var product in controller.products) {
      if (product.sellingPrice != null) {
        total += product.sellingPrice! * product.currentQuantity;
      }
    }
    return total;
  }

  // Stat card widget
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool isWide = false,
  }) {
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: AppColors.border, width: 1),
      ),
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
        children: [
          Row(
            textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
            children: [
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: isRTL ? TextAlign.right : TextAlign.left,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            value,
            style: TextStyle(
              fontSize: isWide ? 24.sp : 20.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: isRTL ? TextAlign.right : TextAlign.left,
          ),
        ],
      ),
    );
  }

  // Animated Floating Action Button
  Widget _buildSpeedDialFab(BuildContext context) {
    return SpeedDial(
      icon: Icons.add,
      activeIcon: Icons.close,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      overlayColor: Colors.black,
      overlayOpacity: 0.4,
      spacing: 12,
      spaceBetweenChildren: 12,
      animatedIcon: AnimatedIcons.menu_close,
      animatedIconTheme: const IconThemeData(size: 22.0),
      children: [
        SpeedDialChild(
          child: const Icon(Icons.add_shopping_cart),
          backgroundColor: AppColors.success,
          foregroundColor: Colors.white,
          label: 'Record Purchase',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToRecordPurchase(),
        ),
        SpeedDialChild(
          child: const Icon(Icons.construction),
          backgroundColor: AppColors.warning,
          foregroundColor: Colors.white,
          label: 'Record Usage',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToRecordUsage(),
        ),
        SpeedDialChild(
          child: const Icon(Icons.point_of_sale),
          backgroundColor: AppColors.info,
          foregroundColor: Colors.white,
          label: 'Record Sale',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToRecordSale(),
        ),
        SpeedDialChild(
          child: const Icon(Icons.tune),
          backgroundColor: AppColors.accent,
          foregroundColor: Colors.white,
          label: 'Adjust Stock',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToRecordAdjustment(),
        ),
        SpeedDialChild(
          child: const Icon(Icons.history),
          backgroundColor: AppColors.textSecondary,
          foregroundColor: Colors.white,
          label: 'View History',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToTransactionHistory(),
        ),
        SpeedDialChild(
          child: const Icon(Icons.add_box),
          backgroundColor: AppColors.secondary,
          foregroundColor: Colors.white,
          label: 'Add New Product',
          labelStyle: const TextStyle(fontWeight: FontWeight.w500),
          labelBackgroundColor: Colors.white,
          onTap: () => controller.goToAddProduct(),
        ),
      ],
    );
  }

  // Add this method for filters
  Widget _buildFilters(BuildContext context, bool isWide) {
    final colorScheme = Theme.of(context).colorScheme;
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return Card(
      color: colorScheme.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(Icons.filter_alt_rounded, color: AppColors.primary),
                const SizedBox(width: 12),
                Text(
                  'filter'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                // Search button
                IconButton(
                  icon: Icon(Icons.search, color: AppColors.primary),
                  onPressed: () {
                    // Implement search functionality
                  },
                ),
                // Reset filters button
                TextButton.icon(
                  onPressed: controller.resetFilters,
                  icon: Icon(Icons.refresh, color: AppColors.primary),
                  label: Text('reset_filter'.tr, style: TextStyle(color: AppColors.primary)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Add this method for stat cards
  Widget _buildStatCards(BuildContext context, bool isWide) {
    final colorScheme = Theme.of(context).colorScheme;
    final stats = controller.stats;
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _statCard(context, 'total_items'.tr, stats['total'] ?? 0, Icons.inventory_2, AppColors.primary),
        _statCard(context, 'in_stock'.tr, stats['inStock'] ?? 0, Icons.check_circle, AppColors.success),
        _statCard(context, 'out_of_stock'.tr, stats['outOfStock'] ?? 0, Icons.remove_circle, AppColors.error),
      ],
    );
  }

  // Helper for stat cards
  Widget _statCard(BuildContext context, String title, int value, IconData icon, Color color) {
    return Card(
      color: Colors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Row(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontWeight: FontWeight.w600, color: AppColors.textSecondary)),
                Text('$value', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20, color: color)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Add this method for inventory grid (desktop/tablet)
  Widget _buildInventoryGrid(BuildContext context) {
    final items = controller.filteredItems;
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;
    
    return Column(
      crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Text(
            'inventory'.tr,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        MasonryGridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 1200 ? 4 : 3,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          itemCount: items.length,
          itemBuilder: (context, index) {
            final item = items[index];
            final isLowStock = item.lowStockThreshold != null && 
                              item.currentQuantity <= item.lowStockThreshold!;
            
            return Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              elevation: 2,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () => controller.goToEditProduct(item),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.inventory_2_rounded, 
                              color: AppColors.primary, 
                              size: 24
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.name,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (item.sku != null && item.sku!.isNotEmpty)
                                  Text(
                                    'SKU: ${item.sku}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: isRTL ? MainAxisAlignment.end : MainAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                            children: [
                              Text(
                                'qty'.tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              Text(
                                '${item.currentQuantity}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isLowStock ? AppColors.error : AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          if (isLowStock)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.error.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: AppColors.error),
                              ),
                              child: Text(
                                'low_stock_warning'.tr,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.error,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // Add this method for inventory list (mobile)
  Widget _buildInventoryList(BuildContext context) {
    final items = controller.filteredItems;
    final languageController = Get.find<LanguageController>();
    final isRTL = languageController.isArabic;
    
    return Column(
      crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Text(
            'inventory'.tr,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          separatorBuilder: (_, __) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            final item = items[index];
            final isLowStock = item.lowStockThreshold != null && 
                              item.currentQuantity <= item.lowStockThreshold!;
                              
            return Card(
              color: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              elevation: 2,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () => controller.goToEditProduct(item),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.inventory_2_rounded, 
                          color: AppColors.primary, 
                          size: 24
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.name,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: isRTL ? MainAxisAlignment.end : MainAxisAlignment.start,
                              children: [
                                Text(
                                  '${"qty".tr}: ${item.currentQuantity}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: isLowStock ? AppColors.error : AppColors.textSecondary,
                                    fontWeight: isLowStock ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                                if (item.sku != null && item.sku!.isNotEmpty) ...[  
                                  Text(
                                    ' • ',
                                    style: TextStyle(color: AppColors.textSecondary),
                                  ),
                                  Text(
                                    'SKU: ${item.sku}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (isLowStock)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.error.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: AppColors.error),
                          ),
                          child: Text(
                            'low_stock_warning'.tr,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: AppColors.error,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

}
