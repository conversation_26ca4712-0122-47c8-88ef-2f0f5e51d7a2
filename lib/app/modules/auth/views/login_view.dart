import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/custom_text_field.dart';
import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding:
                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
            child: FormBuilder(
              key: controller.formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Logo with container and shadow
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: const [
                        BoxShadow(
                          color: AppColors.shadowColor,
                          blurRadius: 15,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Image.asset(
                      'assets/images/logo.png',
                      height: 80,
                      width: 80,
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Welcome text with gradient
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: AppColors.primaryGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                    child: Text(
                      'welcome_back'.tr,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors
                            .white, // This color is used as the base for the gradient
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 8),
                  Text(
                    'sign_in_to_continue'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),

                  // Username field
                  CustomTextField(
                    name: 'username_or_email',
                    hintText: 'enter_your_username'.tr,
                    labelText: 'username'.tr,
                    prefixIcon:
                        const Icon(Icons.person, color: AppColors.primary),
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(
                          errorText: 'please_enter_your_username'.tr),
                    ]),
                    textInputAction: TextInputAction.next,
                    suffixIcon: const Tooltip(
                      message: 'سيتم إضافة @helal.com تلقائياً',
                      child: Icon(Icons.info_outline, color: AppColors.info),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Password field
                  CustomTextField(
                    name: 'password',
                    hintText: 'enter_your_password'.tr,
                    labelText: 'password'.tr,
                    prefixIcon:
                        const Icon(Icons.lock, color: AppColors.primary),
                    obscureText: true,
                    textInputAction: TextInputAction.done,
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(
                          errorText: 'please_enter_your_password'.tr),
                    ]),
                  ),

                  // Forgot password link

                  // Error message
                  Obx(() => controller.errorMessage.isNotEmpty
                      ? Container(
                          margin: const EdgeInsets.only(bottom: 16, top: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          decoration: BoxDecoration(
                            color: AppColors.error.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: AppColors.error.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.error_outline,
                                  color: AppColors.error, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  controller.errorMessage.value,
                                  style: const TextStyle(
                                    color: AppColors.error,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox.shrink()),

                  const SizedBox(height: 24),

                  // Login button
                  Obx(() => CustomButton(
                        onPressed: controller.login,
                        isLoading: controller.isLoading.value,
                        text: 'login'.tr,
                        icon: Icons.login,
                      )),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
