
// quantity_selector_widget.dart - A basic structure
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For input formatters
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../themes/app_colors.dart';

class QuantitySelectorWidget extends StatefulWidget {
  final int initialQuantity;
  final ValueChanged<int> onQuantityChanged;
  final int minValue; // Often 1 for usage/sale, can be negative for adjustments?
  final int incrementStep; // Step value for increment/decrement

  const QuantitySelectorWidget({
    Key? key,
    required this.initialQuantity,
    required this.onQuantityChanged,
    this.minValue = 1, // Default minimum
    this.incrementStep = 1, // Default step
  }) : super(key: key);

  @override
  _QuantitySelectorWidgetState createState() => _QuantitySelectorWidgetState();
}

class _QuantitySelectorWidgetState extends State<QuantitySelectorWidget> {
  late TextEditingController _controller;
  late int _currentQuantity;

  @override
  void initState() {
    super.initState();
    _currentQuantity = widget.initialQuantity;
    _controller = TextEditingController(text: _currentQuantity.toString());
    _controller.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    final int? parsedValue = int.tryParse(_controller.text);
    if (parsedValue != null && parsedValue != _currentQuantity && parsedValue >= widget.minValue) {
      setState(() {
        _currentQuantity = parsedValue;
      });
      widget.onQuantityChanged(_currentQuantity);
    }
    // Handle invalid input if needed (e.g., revert, show error)
  }


  void _increment() {
    setState(() {
      _currentQuantity += widget.incrementStep;
      _controller.text = _currentQuantity.toString(); // Update text field
      widget.onQuantityChanged(_currentQuantity);
    });
  }

  void _decrement() {
    if (_currentQuantity - widget.incrementStep >= widget.minValue) {
      setState(() {
        _currentQuantity -= widget.incrementStep;
        _controller.text = _currentQuantity.toString();
        widget.onQuantityChanged(_currentQuantity);
      });
    }
  }

  void _showQuantityDialog() {
    final TextEditingController dialogController = TextEditingController(text: _currentQuantity.toString());

    Get.dialog(
      AlertDialog(
        title: Text('adjust_quantity'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: dialogController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                labelText: 'quantity'.tr,
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _quickSetButton(dialogController, 5),
                _quickSetButton(dialogController, 10),
                _quickSetButton(dialogController, 20),
                _quickSetButton(dialogController, 50),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () {
              final int? value = int.tryParse(dialogController.text);
              if (value != null && value >= widget.minValue) {
                setState(() {
                  _currentQuantity = value;
                  _controller.text = _currentQuantity.toString();
                });
                widget.onQuantityChanged(_currentQuantity);
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
            child: Text('confirm'.tr),
          ),
        ],
      ),
    );
  }

  Widget _quickSetButton(TextEditingController controller, int value) {
    return InkWell(
      onTap: () => controller.text = value.toString(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          value.toString(),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }


  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // For very small containers, use even more compact layout
        final bool isVerySmall = constraints.maxWidth < 120;

        // Use fixed sizes instead of screenutil for critical UI elements
        final double buttonWidth = isVerySmall ? 28 : 32;
        final double buttonHeight = 32;
        final double quantityWidth = isVerySmall ? 32 : 40;
        final double iconSize = isVerySmall ? 18 : 20;
        final double splashRadius = isVerySmall ? 16 : 18;
        final double fontSize = isVerySmall ? 14 : 15;

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Material( // <--- Add Material widget here
            type: MaterialType.transparency, // Use transparency to avoid drawing background
            child: Row( // This Row and its children (IconButton, InkWell, IconButton)
              mainAxisSize: MainAxisSize.min, // require the Material context
              textDirection: TextDirection.ltr, // Ensure consistent direction
              children: [
                // Decrease button
                SizedBox(
                  width: buttonWidth,
                  height: buttonHeight,
                  child: IconButton( // This uses InkResponse
                    icon: Icon(
                      Icons.remove_circle_outline,
                      size: iconSize,
                    ),
                    onPressed: _currentQuantity > widget.minValue ? _decrement : null,
                    tooltip: 'decrease_quantity'.tr,
                    splashRadius: splashRadius,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),

                // Quantity display
                InkWell( // This requires a Material ancestor
                  onTap: _showQuantityDialog,
                  child: Container(
                    width: quantityWidth,
                    height: buttonHeight,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Text(
                        _currentQuantity.toString(),
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),

                // Increase button
                SizedBox(
                  width: buttonWidth,
                  height: buttonHeight,
                  child: IconButton( // This uses InkResponse
                    icon: Icon(
                      Icons.add_circle_outline,
                      size: iconSize,
                    ),
                    onPressed: _increment,
                    tooltip: 'increase_quantity'.tr,
                    splashRadius: splashRadius,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),
              ],
            ),
          ), // <--- End Material widget
        );
      },
    );
  }
}