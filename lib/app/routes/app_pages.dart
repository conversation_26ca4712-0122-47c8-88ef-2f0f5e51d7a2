import 'package:cars_app/app/modules/car/bindings/workshop_form_binding.dart';
import 'package:cars_app/app/modules/car_report/bindings/car_report_binding.dart';
import 'package:cars_app/app/modules/car_report/views/car_report_view.dart';
import 'package:cars_app/app/modules/dashboard/bindings/dashboard_binding.dart';
import 'package:cars_app/app/modules/home/<USER>/home_binding.dart';
import 'package:cars_app/app/modules/logastic_support/bindings/logistics_binding.dart';
import 'package:cars_app/app/modules/logastic_support/view/logistics_view.dart';
import 'package:cars_app/app/modules/modern_dashboard/bindings/modern_dashboard_binding.dart';
import 'package:cars_app/app/modules/modern_dashboard/views/modern_dashboard_view.dart';
import 'package:cars_app/app/modules/profile/bindings/profile_binding.dart';
import 'package:cars_app/app/modules/profile/views/profile_view.dart';
import 'package:cars_app/app/modules/sector/view/sector_manger_view.dart';
import 'package:cars_app/app/widgets/car_rotation_dashboard.dart';
import 'package:get/get.dart';
import '../middleware/auth_middleware.dart';
import '../modules/car/view/workshop_form.dart';
import '../modules/chat/bindings/chat_binding.dart';
import '../modules/chat/views/chat_view.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/car/view/car_view.dart';
import '../modules/inventory/bindings/inventory_binding.dart';
import '../modules/inventory/views/add_edit_product_screen.dart';
import '../modules/inventory/views/inventory_list_screen.dart';
import '../modules/inventory/views/transaction_history_screen.dart';
import '../modules/inventory/views/transaction_screen.dart';
import '../modules/sector/view/sector_view.dart';
import '../modules/user/view/user_view.dart';
import '../modules/car/bindings/car_binding.dart';
import '../modules/sector/bindings/sector_binding.dart';
import '../modules/user/bindings/user_binding.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/settings/views/settings_view.dart';
import '../modules/settings/bindings/settings_binding.dart';
import '../modules/splash/views/splash_view.dart';
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/auth/bindings/login_binding.dart';
import '../modules/vehicle_inspection/views/vehicle_inspection_view_new.dart';
import '../modules/vehicle_inspection/bindings/vehicle_inspection_binding.dart';
import '../modules/supervisor/bindings/supervisor_binding.dart';
import '../modules/supervisor/views/supervisor_view.dart';
import '../modules/car_rotation/bindings/car_rotation_binding.dart';
import '../modules/car_rotation/views/car_rotation_view.dart';
import '../modules/technician/views/technician_view.dart';
import '../modules/technician/bindings/technician_binding.dart';
import '../modules/reports/views/reports_view.dart';
import '../modules/reports/bindings/reports_binding.dart';
import '../modules/vehicle_inspection/widgets/car_selector_parts_widget.dart';
import '../views/notifications_view.dart';
import '../modules/admin/views/problem_tags_management_view.dart';
import '../modules/dashboard/views/problem_statistics_view.dart';
import '../modules/reports/view/problem_reports_view.dart';
import '../modules/maintenance/views/maintenance_checklist_view.dart';
import '../modules/maintenance/bindings/maintenance_checklist_binding.dart';
import '../modules/theme_demo/views/theme_demo_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    // GetPage(
    //   name: Routes.MODERN_DASHBOARD,
    //   page: () => const ModernDashboardView(),
    //   binding: ModernDashboardBinding(),
    //   transition: Transition.fadeIn,
    // ),
    GetPage(
      name: Routes.HOME,
      page: () => HomeView(),
      bindings: [
        HomeBinding(),
        CarBinding(),
        DashboardBinding(),
        UserBinding(),
        CarBinding(),
        SectorBinding(),
        CarRotationBinding(),
        ReportsBinding(),
        InventoryBinding(),
        VehicleInspectionBinding(),
        TechnicianBinding(),
        SupervisorBinding()
      ],
    ),
    GetPage(
      name: Routes.CARS,
      page: () => const CarView(),
      binding: CarBinding(),
    ),
    GetPage(
      name: Routes.CHAT,
      page: () => const ChatView(),
      binding: ChatBinding(),
    ),
    GetPage(
      name: Routes.CarRotationDashboard,
      page: () => CarRotationDashboard(),
    ),
    GetPage(
      name: Routes.SECTOR_MANAGER,
      page: () => const SectorMangerView(),
      bindings: [
        CarBinding(),
        // NotificationsBinding(),
        SectorBinding()
      ],
    ),
    GetPage(
      name: Routes.LOGASTIC_SUPPORT,
      page: () => const LogisticsView(),
      bindings: [
        CarBinding(),
        // NotificationsBinding(),
        LoginBinding(),
        LogisticsBinding()
      ],
    ),
    GetPage(
      name: Routes.SECTORS,
      page: () => SectorView(),
      binding: SectorBinding(),
    ),
    GetPage(
      name: Routes.USERS,
      page: () => UserView(),
      binding: UserBinding(),
    ),
    GetPage(
      name: Routes.SETTINGS,
      page: () => const SettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: Routes.VEHICLE_INSPECTION,
      page: () => const VehicleInspectionView(),
      bindings: [VehicleInspectionBinding(), MaintenanceChecklistBinding()],
    ),
    GetPage(
      name: Routes.SUPERVISOR,
      page: () => const SupervisorView(),
      binding: SupervisorBinding(),
    ),
    GetPage(
      name: Routes.CAR_ROTATION,
      page: () => const CarRotationView(),
      binding: CarRotationBinding(),
    ),
    GetPage(
      name: Routes.NOTIFICATIONS,
      page: () => const NotificationsView(),
      // binding: NotificationsBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
        name: Routes.WORKSHOP_FORM,
        page: () => const WorkshopForm(),
        bindings: [CarBinding(), WorkshopFormBinding()]),
    GetPage(
      name: Routes.TECHNICIAN,
      page: () => const TechnicianView(),
      bindings: [
        TechnicianBinding(),
        InventoryBinding(),
      ],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: Routes.REPORTS,
      page: () => const ReportsView(),
      binding: ReportsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.inventoryList,
      page: () => const InventoryListScreen(),
      binding:
          InventoryBinding(), // Bind controllers for this route and sub-routes
      transition: Transition.fadeIn, // Example animation
    ),
    GetPage(
      name: Routes.addEditProduct,
      page: () => const AddEditProductScreen(),
      binding: InventoryBinding(), // Or specific binding if needed
      transition: Transition.rightToLeftWithFade,
    ),
    GetPage(
      name: Routes.transaction,
      page: () => const TransactionScreen(),
      binding: InventoryBinding(),
      transition: Transition.downToUp, // Example
    ),
    GetPage(
      name: Routes.transactionHistory,
      page: () => const TransactionHistoryScreen(),
      binding: InventoryBinding(),
      transition: Transition.zoom, // Example
    ),
    GetPage(
      name: Routes.PROBLEM_TAGS_MANAGEMENT,
      page: () => const ProblemTagsManagementView(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.PROBLEM_STATISTICS,
      page: () => const ProblemStatisticsView(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.PROBLEM_REPORTS,
      page: () => const ProblemReportsView(),
      binding: ReportsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.CAR_REPORT,
      page: () => const CarReportView(),
      binding: CarReportBinding(),
      transition: Transition.fadeIn,
    ),
    // GetPage(
    //   name: Routes.MAINTENANCE_CHECKLIST,
    //   page: () =>  InteractiveAmbulanceSvg(
    //     initialSvgData: 'assets/mbulance.svg',
    //   ),
    //   binding: MaintenanceChecklistBinding(),
    //   transition: Transition.fadeIn,
    // ),
    GetPage(
      name: Routes.THEME_DEMO,
      page: () => const ThemeDemoView(),
      transition: Transition.fadeIn,
    ),
  ];
}
