import 'package:get/get.dart';
import '../controllers/inventory_controller.dart';
import '../controllers/transaction_controller.dart';
import '../controllers/transaction_history_controller.dart';
import '../../../data/providers/inventory_firestore_provider.dart';

class InventoryBinding extends Bindings {
  @override
  void dependencies() {
    // Register the Provider first (singleton usually)
    Get.lazyPut<InventoryFirestoreProvider>(() => InventoryFirestoreProvider());

    // Register controllers that depend on the provider
    Get.lazyPut<InventoryController>(() => InventoryController());
    Get.lazyPut<TransactionController>(() => TransactionController());
    Get.lazyPut<TransactionHistoryController>(() => TransactionHistoryController());
  }
}