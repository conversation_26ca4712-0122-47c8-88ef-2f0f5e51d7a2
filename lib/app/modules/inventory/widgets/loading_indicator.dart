// loading_indicator.dart
import 'package:flutter/material.dart';
// Optional: Use a fancier indicator like flutter_spinkit
// import 'package:flutter_spinkit/flutter_spinkit.dart';

class LoadingIndicator extends StatelessWidget {
  final double size;
  const LoadingIndicator({Key? key, this.size = 40.0}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: size,
        height: size,
        // child: SpinKitFoldingCube( // Example using SpinKit
        //   color: Theme.of(context).colorScheme.primary,
        //   size: size,
        // ),
        child: CircularProgressIndicator(
          strokeWidth: 3.0,
          valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
        ),
      ),
    );
  }
}
