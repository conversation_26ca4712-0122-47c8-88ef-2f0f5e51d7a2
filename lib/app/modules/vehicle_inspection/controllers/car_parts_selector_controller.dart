import 'package:get/get.dart';
import '../models/car_part.dart';
import '../models/van_parts_definitions.dart';

class CarPartsSelectorController extends GetxController {
  // All car parts
  final RxList<CarPart> allParts = <CarPart>[].obs;
  
  // Currently selected view
  final Rx<CarView> currentView = CarView.front.obs;
  
  // Currently selected parts
  final RxList<String> selectedPartIds = <String>[].obs;
  
  // Show part labels
  final RxBool showLabels = false.obs;
  
  // Part damage levels
  final RxMap<String, int> damageLevels = <String, int>{}.obs;
  
  // Part notes
  final RxMap<String, String> partNotes = <String, String>{}.obs;
  
  @override
  void onInit() {
    super.onInit();
    // Initialize with all van parts
    allParts.value = VanPartsFactory.createAllParts();
  }
  
  // Change the current view
  void setView(CarView view) {
    currentView.value = view;
  }
  
  // Toggle part selection
  void togglePartSelection(String partId) {
    if (selectedPartIds.contains(partId)) {
      selectedPartIds.remove(partId);
    } else {
      selectedPartIds.add(partId);
    }
    
    // Update the part in the list
    final index = allParts.indexWhere((part) => part.id == partId);
    if (index != -1) {
      final part = allParts[index];
      allParts[index] = part.copyWith(
        isSelected: !part.isSelected,
      );
    }
  }
  
  // Set damage level for a part
  void setDamageLevel(String partId, int level) {
    if (level < 0) level = 0;
    if (level > 5) level = 5;
    
    damageLevels[partId] = level;
    
    // Update the part in the list
    final index = allParts.indexWhere((part) => part.id == partId);
    if (index != -1) {
      final part = allParts[index];
      allParts[index] = part.copyWith(
        damageLevel: level,
      );
    }
    
    // Automatically select the part if damage level is set
    if (level > 0 && !selectedPartIds.contains(partId)) {
      selectedPartIds.add(partId);
      
      // Update selection state
      final partIndex = allParts.indexWhere((part) => part.id == partId);
      if (partIndex != -1) {
        final part = allParts[partIndex];
        allParts[partIndex] = part.copyWith(
          isSelected: true,
        );
      }
    }
  }
  
  // Set notes for a part
  void setPartNotes(String partId, String notes) {
    partNotes[partId] = notes;
    
    // Update the part in the list
    final index = allParts.indexWhere((part) => part.id == partId);
    if (index != -1) {
      final part = allParts[index];
      allParts[index] = part.copyWith(
        notes: notes,
      );
    }
  }
  
  // Toggle showing labels
  void toggleLabels() {
    showLabels.value = !showLabels.value;
  }
  
  // Get parts for the current view
  List<CarPart> get currentViewParts {
    return allParts.where((part) => part.view == currentView.value).toList();
  }
  
  // Get a part by ID
  CarPart? getPartById(String id) {
    final index = allParts.indexWhere((part) => part.id == id);
    if (index != -1) {
      return allParts[index];
    }
    return null;
  }
  
  // Clear all selections
  void clearSelections() {
    selectedPartIds.clear();
    
    // Update all parts
    for (var i = 0; i < allParts.length; i++) {
      final part = allParts[i];
      allParts[i] = part.copyWith(
        isSelected: false,
      );
    }
  }
  
  // Export data for saving
  Map<String, dynamic> exportData() {
    final selectedParts = allParts.where((part) => part.isSelected).toList();
    
    return {
      'selectedPartIds': selectedPartIds.toList(),
      'damageLevels': damageLevels.map((key, value) => MapEntry(key, value)),
      'partNotes': partNotes.map((key, value) => MapEntry(key, value)),
      'parts': selectedParts.map((part) => {
        'id': part.id,
        'name': part.name,
        'view': part.view.toString().split('.').last,
        'isSelected': part.isSelected,
        'damageLevel': part.damageLevel,
        'notes': part.notes,
      }).toList(),
    };
  }
  
  // Import data from saved state
  void importData(Map<String, dynamic> data) {
    // Clear current state
    clearSelections();
    damageLevels.clear();
    partNotes.clear();
    
    // Import selected part IDs
    if (data.containsKey('selectedPartIds')) {
      final ids = List<String>.from(data['selectedPartIds']);
      selectedPartIds.addAll(ids);
    }
    
    // Import damage levels
    if (data.containsKey('damageLevels')) {
      final levels = Map<String, int>.from(data['damageLevels']);
      damageLevels.addAll(levels);
    }
    
    // Import part notes
    if (data.containsKey('partNotes')) {
      final notes = Map<String, String>.from(data['partNotes']);
      partNotes.addAll(notes);
    }
    
    // Update all parts
    for (var i = 0; i < allParts.length; i++) {
      final part = allParts[i];
      allParts[i] = part.copyWith(
        isSelected: selectedPartIds.contains(part.id),
        damageLevel: damageLevels[part.id],
        notes: partNotes[part.id],
      );
    }
  }
}
