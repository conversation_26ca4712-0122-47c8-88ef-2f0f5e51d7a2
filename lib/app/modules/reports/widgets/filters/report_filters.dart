import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../../../data/models/car_model.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';

class ReportFilters extends StatelessWidget {
  final ReportsController controller;
  final bool inDialog;

  const ReportFilters({
    Key? key,
    required this.controller,
    this.inDialog = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return inDialog ? _buildDialogFilters() : _buildAppBarFilters();
  }

  // Filters for the dialog
  Widget _buildDialogFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildDateRangeFilter(),
        const SizedBox(height: 16),
        _buildStatusFilter(),
        const Si<PERSON><PERSON><PERSON>(height: 16),
        _buildSectorFilter(),
        const SizedBox(height: 16),
        _buildCarTypeFilter(),
        const SizedBox(height: 16),
        _buildSearchField(),
      ],
    );
  }

  // Compact filters for the app bar
  Widget _buildAppBarFilters() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          Row(
            children: [
              const Icon(Icons.filter_list, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                'تصفية التقارير'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () => controller.clearFilters(),
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: Text(
                  'إعادة ضبط'.tr,
                  style: const TextStyle(color: Colors.white),
                ),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Compact filters
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildCompactDateFilter(),
              _buildCompactStatusFilter(),
              _buildCompactSectorFilter(),
              _buildCompactCarTypeFilter(),
              _buildCompactSearchField(),
            ],
          ),
        ],
      ),
    );
  }

  // Regular filter widgets for dialog
  Widget _buildDateRangeFilter() {
    return InkWell(
      onTap: () => _showDateRangePicker(),
      borderRadius: BorderRadius.circular(12),
      child: AbsorbPointer(
        child: TextField(
          controller: controller.dateRangeController,
          decoration: InputDecoration(
            labelText: 'الفترة الزمنية'.tr,
            prefixIcon: Icon(Icons.calendar_today, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Obx(() => DropdownButtonFormField<CarStatus>(
          value: controller.selectedStatus.value,
          decoration: InputDecoration(
            labelText: 'الحالة'.tr,
            prefixIcon: Icon(Icons.flag, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          items: [
            DropdownMenuItem(
              value: null,
              child: Text('الكل'.tr),
            ),
            ...CarStatus.values.map((status) {
              return DropdownMenuItem(
                value: status,
                child: Text(_getStatusText(status)),
              );
            }).toList(),
          ],
          onChanged: (value) {
            controller.selectedStatus.value = value;
            controller.applyDateRange();
          },
        ));
  }

  Widget _buildSectorFilter() {
    return Obx(() => DropdownButtonFormField<String>(
          value: controller.selectedSector.value,
          decoration: InputDecoration(
            labelText: 'القطاع'.tr,
            prefixIcon: Icon(Icons.location_city, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          items: [
            DropdownMenuItem(
              value: null,
              child: Text('الكل'.tr),
            ),
            ...controller.availableSectors.map((sector) {
              return DropdownMenuItem(
                value: sector,
                child: Text(sector),
              );
            }).toList(),
          ],
          onChanged: (value) {
            controller.selectedSector.value = value;
            controller.applyDateRange();
          },
        ));
  }

  Widget _buildCarTypeFilter() {
    return Obx(() => DropdownButtonFormField<String>(
          value: controller.selectedCarType.value,
          decoration: InputDecoration(
            labelText: 'نوع المركبة'.tr,
            prefixIcon: Icon(Icons.directions_car, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          items: [
            DropdownMenuItem(
              value: null,
              child: Text('الكل'.tr),
            ),
            ...controller.availableCarTypes.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type),
              );
            }).toList(),
          ],
          onChanged: (value) {
            controller.selectedCarType.value = value;
            controller.applyDateRange();
          },
        ));
  }

  Widget _buildSearchField() {
    return TextField(
      onChanged: (value) => controller.searchQuery.value = value,
      decoration: InputDecoration(
        labelText: 'بحث'.tr,
        hintText: 'رقم اللوحة، الموديل، الملاحظات'.tr,
        prefixIcon: Icon(Icons.search, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  controller.searchQuery.value = '';
                },
              )
            : const SizedBox.shrink()),
      ),
    );
  }

  // Compact filter widgets for SliverAppBar
  Widget _buildCompactDateFilter() {
    return InkWell(
      onTap: () => _showDateRangePicker(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.calendar_today, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Obx(() => Text(
                  '${controller.startDate.value.day}/${controller.startDate.value.month} - ${controller.endDate.value.day}/${controller.endDate.value.month}',
                  style: const TextStyle(color: Colors.white),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactStatusFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: DropdownButton<CarStatus>(
        value: controller.selectedStatus.value,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: const SizedBox(),
        dropdownColor: AppColors.primary,
        style: const TextStyle(color: Colors.white),
        hint: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.flag, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text('الحالة'.tr, style: const TextStyle(color: Colors.white)),
          ],
        ),
        items: [
          DropdownMenuItem(
            value: null,
            child: Text('الكل'.tr, style: TextStyle(color: AppColors.primary)),
          ),
          ...CarStatus.values.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Text(_getStatusText(status),
                  style: TextStyle(color: AppColors.primary)),
            );
          }).toList(),
        ],
        onChanged: (value) {
          controller.selectedStatus.value = value;
          controller.applyDateRange();
        },
      ),
    );
  }

  Widget _buildCompactSectorFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: DropdownButton<String>(
        value: controller.selectedSector.value,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: const SizedBox(),
        dropdownColor: AppColors.primary,
        style: const TextStyle(color: Colors.white),
        hint: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.location_city, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text('القطاع'.tr, style: const TextStyle(color: Colors.white)),
          ],
        ),
        items: [
          DropdownMenuItem(
            value: null,
            child: Text('الكل'.tr, style: TextStyle(color: AppColors.primary)),
          ),
          ...controller.availableSectors.map((sector) {
            return DropdownMenuItem(
              value: sector,
              child: Text(sector, style: TextStyle(color: AppColors.primary)),
            );
          }).toList(),
        ],
        onChanged: (value) {
          controller.selectedSector.value = value;
          controller.applyDateRange();
        },
      ),
    );
  }

  Widget _buildCompactCarTypeFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: DropdownButton<String>(
        value: controller.selectedCarType.value,
        icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
        underline: const SizedBox(),
        dropdownColor: AppColors.primary,
        style: const TextStyle(color: Colors.white),
        hint: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.directions_car, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text('نوع المركبة'.tr, style: const TextStyle(color: Colors.white)),
          ],
        ),
        items: [
          DropdownMenuItem(
            value: null,
            child: Text('الكل'.tr, style: TextStyle(color: AppColors.primary)),
          ),
          ...controller.availableCarTypes.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type, style: TextStyle(color: AppColors.primary)),
            );
          }).toList(),
        ],
        onChanged: (value) {
          controller.selectedCarType.value = value;
          controller.applyDateRange();
        },
      ),
    );
  }

  Widget _buildCompactSearchField() {
    return Container(
      width: 150,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: TextField(
        onChanged: (value) => controller.searchQuery.value = value,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: 'بحث'.tr,
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
          prefixIcon: const Icon(Icons.search, color: Colors.white, size: 16),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8),
          isDense: true,
        ),
      ),
    );
  }

  void _showDateRangePicker() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          width: Get.width * 0.9,
          height: Get.height * 0.6,
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    'اختر الفترة الزمنية'.tr,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SfDateRangePicker(
                  view: DateRangePickerView.month,
                  selectionMode: DateRangePickerSelectionMode.range,
                  initialSelectedRange: PickerDateRange(
                    controller.startDate.value,
                    controller.endDate.value,
                  ),
                  onSelectionChanged:
                      (DateRangePickerSelectionChangedArgs args) {
                    if (args.value is PickerDateRange) {
                      final range = args.value as PickerDateRange;
                      if (range.startDate != null && range.endDate != null) {
                        controller.setDateRange(
                            range.startDate!, range.endDate!);
                      }
                    }
                  },
                  headerStyle: const DateRangePickerHeaderStyle(
                    textAlign: TextAlign.center,
                  ),
                  monthViewSettings: const DateRangePickerMonthViewSettings(
                    firstDayOfWeek: 6, // Saturday
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('إلغاء'.tr),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      controller.applyDateRange();
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('تطبيق'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'نشط'.tr;
      case CarStatus.pending:
        return 'قيد المعالجة'.tr;
      case CarStatus.done:
        return 'مكتمل'.tr;
      case CarStatus.rejected:
        return 'مرفوض'.tr;
      case CarStatus.sendToLogisticsSupport:
        return 'تم الإرسال للدعم اللوجستي'.tr;
      case CarStatus.deliveryToSector:
        return 'تم التسليم للقطاع'.tr;
      case CarStatus.callToWorkshop:
        return 'تم الاستدعاء للورشة'.tr;
      case CarStatus.agreeDeliveryToWorkShop:
        return 'تمت الموافقة على التسليم للورشة'.tr;
      case CarStatus.sentRequest:
        return 'تم إرسال الطلب'.tr;
      default:
        return status.name.tr;
    }
  }
}
