import 'package:cars_app/app/controllers/auth_controller.dart';
import 'package:cars_app/app/themes/app_colors.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/models/user_model.dart';
import '../../../services/user_storage_service.dart';

class ProfileController extends GetxController with GetSingleTickerProviderStateMixin {
  final FirebaseFirestore firebaseFirestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  // Form controllers
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  
  // Form validation
  final isCurrentPasswordVisible = false.obs;
  final isNewPasswordVisible = false.obs;
  final isConfirmPasswordVisible = false.obs;
  final isLoading = false.obs;

  final RxString userName = ''.obs;
  final RxString role = ''.obs;
  final LocalStorageService localStorageService =LocalStorageService();

  // Animation controllers
  late final animationController = Get.put(
    GetxAnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    ),
  );

  late final slideAnimation = Tween<Offset>(
    begin: const Offset(0, 0.5),
    end: Offset.zero,
  ).animate(CurvedAnimation(
    parent: animationController,
    curve: Curves.easeOutQuart,
  ));

  late final fadeAnimation = Tween<double>(
    begin: 0.0,
    end: 1.0,
  ).animate(CurvedAnimation(
    parent: animationController,
    curve: Curves.easeInOut,
  ));

  @override
  void onInit() {
    super.onInit();
    animationController.forward();
    loadDate();
  }

  @override
  void onClose() {
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    animationController.dispose();
    super.onClose();
  }


  void toggleCurrentPasswordVisibility() {
    isCurrentPasswordVisible.value = !isCurrentPasswordVisible.value;
  }

  void toggleNewPasswordVisibility() {
    isNewPasswordVisible.value = !isNewPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  Future<void> resetPassword() async {
    if (!_validatePasswords()) return;

    try {
      isLoading.value = true;
      // TODO: Implement actual password reset logic with your auth system
      // await Future.delayed(const Duration(seconds: 2)); // Simulated API call

      //check old password
      // firebaseFirestore.collection('users').doc(authController.uid).get();

      await FirebaseFirestore.instance.collection('users').doc().update({
        'password': newPasswordController.text
      });
      Get.back(); // Close bottom sheet
      Get.snackbar(
        'نجاح',
        'تم تغيير كلمة المرور بنجاح',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );

      // Clear form
      currentPasswordController.clear();
      newPasswordController.clear();
      confirmPasswordController.clear();
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل تغيير كلمة المرور',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  bool _validatePasswords() {
    if (
        newPasswordController.text.isEmpty ||
        confirmPasswordController.text.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى ملء جميع الحقول',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    if (newPasswordController.text != confirmPasswordController.text) {
      Get.snackbar(
        'خطأ',
        'كلمة المرور الجديدة غير متطابقة',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    if (newPasswordController.text.length < 3) {
      Get.snackbar(
        'خطأ',
        'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    return true;
  }
  Future<UserModel?> getUserFromLocalStorage() async {
    final localStorage = LocalStorageService();

    String? id = await localStorage.getUserId();
    String? username = await localStorage.getUsername();
    String? roleString = await localStorage.getUserRole();
    String? statusString = await localStorage.getUserStatus();
    String? email = await localStorage.getEmail();
    String? phoneNumber = await localStorage.getPhoneNumber();

    if (id == null || username == null || roleString == null || statusString == null) {
      return null;
    }

    return UserModel(
      id: id,
      username: username,
      name: '', // Fetch from Firestore if needed
      password: '', // Handle securely
      role: UserRole.values.firstWhere((role) => role.toString().split('.').last == roleString),
      status: UserStatus.values.firstWhere((status) => status.toString().split('.').last == statusString),
      createdAt: DateTime.now(),
      email: email,
      phoneNumber: phoneNumber,
    );
  }

  Future<void> loadDate() async{
    role.value =(await localStorageService.getUserRole())!;
    userName.value =_auth.currentUser!.email!;


  }
}

class GetxAnimationController extends AnimationController {
  GetxAnimationController({
    required Duration duration,
    required TickerProvider vsync,
  }) : super(duration: duration, vsync: vsync);
}
