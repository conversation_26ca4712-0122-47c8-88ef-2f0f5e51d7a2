import 'package:flutter/material.dart';

class AppColors {
  // ============ LIGHT THEME COLORS ============

  // Main brand colors
  static const Color primary = Color(0xFF6366F1); // Modern indigo
  static const Color primaryVariant = Color(0xFF4F46E5); // Darker indigo
  static const Color secondary = Color(0xFF06B6D4); // Cyan accent
  static const Color secondaryVariant = Color(0xFF0891B2); // Darker cyan
  static const Color accent = Color(0xFFF59E0B); // Amber accent

  // Light theme background colors
  static const Color lightBackground = Color(0xFFFAFAFA); // Very light gray
  static const Color lightSurface = Color(0xFFFFFFFF); // Pure white
  static const Color lightSurfaceVariant = Color(0xFFF1F5F9); // Light blue-gray
  static const Color lightCard = Color(0xFFFFFFFF); // White card

  // Light theme text colors
  static const Color lightTextPrimary = Color(0xFF1E293B); // Dark slate
  static const Color lightTextSecondary = Color(0xFF64748B); // Slate
  static const Color lightTextTertiary = Color(0xFF94A3B8); // Light slate
  static const Color lightOnPrimary = Color(0xFFFFFFFF); // White

  // Light theme border colors
  static const Color lightBorder = Color(0xFFE2E8F0); // Light slate border
  static const Color lightDivider = Color(0xFFF1F5F9); // Very light divider

  // ============ DARK THEME COLORS ============

  // Dark theme background colors
  static const Color darkBackground = Color(0xFF0F172A); // Very dark slate
  static const Color darkSurface = Color(0xFF1E293B); // Dark slate
  static const Color darkSurfaceVariant = Color(0xFF334155); // Medium slate
  static const Color darkCard = Color(0xFF1E293B); // Dark slate card

  // Dark theme text colors
  static const Color darkTextPrimary = Color(0xFFF8FAFC); // Very light slate
  static const Color darkTextSecondary = Color(0xFFCBD5E1); // Light slate
  static const Color darkTextTertiary = Color(0xFF94A3B8); // Medium slate
  static const Color darkOnPrimary = Color(0xFFFFFFFF); // White

  // Dark theme border colors
  static const Color darkBorder = Color(0xFF334155); // Medium slate border
  static const Color darkDivider = Color(0xFF1E293B); // Dark divider

  // ============ UNIVERSAL COLORS ============

  // Status colors (same for both themes)
  static const Color success = Color(0xFF10B981); // Emerald
  static const Color error = Color(0xFFEF4444); // Red
  static const Color warning = Color(0xFFF59E0B); // Amber
  static const Color info = Color(0xFF3B82F6); // Blue
  static const Color pending = Color(0xFFFF9800); // Orange
  static const Color done = Color(0xFF8B5CF6); // Violet
  static const Color logistics = Color(0xFFEC4899); // Pink
  static const Color delivery = Color(0xFF06B6D4); // Cyan
  static const Color workshop = Color(0xFFF59E0B); // Amber
  static const Color approved = Color(0xFF8B5CF6); // Violet

  // Shadow colors
  static const Color lightShadow = Color(0x0A000000); // Very light shadow
  static const Color darkShadow = Color(0x40000000); // Medium shadow

  // ============ GRADIENTS ============

  // Primary gradients
  static const List<Color> primaryGradient = [primary, primaryVariant];
  static const List<Color> secondaryGradient = [secondary, secondaryVariant];

  // Modern card gradients
  static const List<Color> cardGradient1 = [
    Color(0xFF667EEA),
    Color(0xFF764BA2)
  ];
  static const List<Color> cardGradient2 = [
    Color(0xFF06B6D4),
    Color(0xFF3B82F6)
  ];
  static const List<Color> cardGradient3 = [
    Color(0xFF8B5CF6),
    Color(0xFFEC4899)
  ];
  static const List<Color> cardGradient4 = [
    Color(0xFFF59E0B),
    Color(0xFFEF4444)
  ];
  static const List<Color> cardGradient5 = [
    Color(0xFF10B981),
    Color(0xFF06B6D4)
  ];

  // Status gradients
  static const List<Color> successGradient = [
    Color(0xFF10B981),
    Color(0xFF059669)
  ];
  static const List<Color> errorGradient = [
    Color(0xFFEF4444),
    Color(0xFFDC2626)
  ];
  static const List<Color> warningGradient = [
    Color(0xFFF59E0B),
    Color(0xFFD97706)
  ];
  static const List<Color> infoGradient = [
    Color(0xFF3B82F6),
    Color(0xFF2563EB)
  ];

  // ============ HELPER METHODS ============

  /// Get background color based on theme
  static Color getBackground(bool isDark) {
    return isDark ? darkBackground : lightBackground;
  }

  /// Get surface color based on theme
  static Color getSurface(bool isDark) {
    return isDark ? darkSurface : lightSurface;
  }

  /// Get card color based on theme
  static Color getCard(bool isDark) {
    return isDark ? darkCard : lightCard;
  }

  /// Get primary text color based on theme
  static Color getTextPrimary(bool isDark) {
    return isDark ? darkTextPrimary : lightTextPrimary;
  }

  /// Get secondary text color based on theme
  static Color getTextSecondary(bool isDark) {
    return isDark ? darkTextSecondary : lightTextSecondary;
  }

  /// Get tertiary text color based on theme
  static Color getTextTertiary(bool isDark) {
    return isDark ? darkTextTertiary : lightTextTertiary;
  }

  /// Get border color based on theme
  static Color getBorder(bool isDark) {
    return isDark ? darkBorder : lightBorder;
  }

  /// Get divider color based on theme
  static Color getDivider(bool isDark) {
    return isDark ? darkDivider : lightDivider;
  }

  /// Get shadow color based on theme
  static Color getShadow(bool isDark) {
    return isDark ? darkShadow : lightShadow;
  }

  // ============ BACKWARD COMPATIBILITY ============
  // These are for backward compatibility with existing code

  static const Color background = lightBackground;
  static const Color surface = lightSurface;
  static const Color surfaceVariant = lightSurfaceVariant;
  static const Color cardBackground = lightCard;
  static const Color textPrimary = lightTextPrimary;
  static const Color textSecondary = lightTextSecondary;
  static const Color textLight = lightTextTertiary;
  static const Color onPrimary = lightOnPrimary;
  static const Color border = lightBorder;
  static const Color divider = lightDivider;
  static const Color shadowColor = lightShadow;

  // Missing gradient properties for backward compatibility
  static const List<Color> totalGradient = [
    Color(0xFF4A00E0),
    Color(0xFF8E2DE2)
  ];
  static const List<Color> pendingGradient = [
    Color(0xFFF9D423),
    Color(0xFFFF4E50)
  ];
  static const List<Color> doneGradient = [
    Color(0xFF00B4DB),
    Color(0xFF0083B0)
  ];
  static const List<Color> rejectedGradient = [
    Color(0xFFED213A),
    Color(0xFF93291E)
  ];
}
