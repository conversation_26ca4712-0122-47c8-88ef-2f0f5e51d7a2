import 'package:cloud_firestore/cloud_firestore.dart';

import '../../routes/app_pages.dart';

enum UserRole {
  admin,
  technician,
  manager,
  supervisor,
  logisticsSupport,
  user
}

enum UserStatus {
  active,
  inactive,
  suspended,
  pending
}

class UserModel {
  final String id;
  final String username;
  final String name;
  final String password;
  final UserRole role;
  final UserStatus status;
  final DateTime createdAt;
  final String? email;
  final String? phoneNumber;

  UserModel({
    required this.id,
    required this.username,
    required this.name,
    required this.password,
    required this.role,
    required this.status,
    required this.createdAt,
    this.email,
    this.phoneNumber,
  });

  //fromFirestore       final userList = snapshot.docs.map((doc) => UserModel.fromFirestore(doc.data(), doc.id)).toList();

  factory UserModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return UserModel(
      id: doc.id,
      username: data['username'],
      name: data['name'],
      password: data['password'],
      role: UserRole.values.firstWhere(
        (role) => role.toString() == 'UserRole.${data['role']}',
        orElse: () => UserRole.user,
      ),
      status: UserStatus.values.firstWhere(
        (status) => status.toString() == 'UserStatus.${data['status']}',
        orElse: () => UserStatus.pending,
      ),
      createdAt: data['createdAt'] != null ? (data['createdAt'] as Timestamp).toDate() : DateTime.now(),
      email: data['email'],
      phoneNumber: data['phoneNumber'],
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      name: json['name'] ?? json['username'] ?? '',
      password: json['password'] ?? '',
      role: UserRole.values.firstWhere(
        (role) => role.toString() == 'UserRole.${json['role'] ?? 'user'}',
        orElse: () => UserRole.user,
      ),
      status: UserStatus.values.firstWhere(
        (status) => status.toString() == 'UserStatus.${json['status'] ?? 'pending'}',
        orElse: () => UserStatus.pending,
      ),
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      email: json['email'],
      phoneNumber: json['phoneNumber'],
    );
  }

  String get roleString => role.toString().split('.').last;
  String getCurrentRoute(){

    if(role == UserRole.admin){
      return Routes.HOME;
    }else if(role == UserRole.manager){
      return Routes.SECTOR_MANAGER;
    }else if(role == UserRole.supervisor){
      return Routes.SUPERVISOR;
    }else if(role == UserRole.technician){
      return Routes.TECHNICIAN;
    }else if(role == UserRole.user){
      return Routes.LOGIN;
    }else if(role == UserRole.logisticsSupport){
      return Routes.LOGASTIC_SUPPORT;
    }

    return Routes.LOGIN;
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'password': password,
      'role': role.toString().split('.').last,
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'email': email,
      'phoneNumber': phoneNumber,
    };

  }

  UserModel copyWith({
    String? id,
    String? username,
    String? name,
    String? password,
    UserRole? role,
    UserStatus? status,
    DateTime? createdAt,
    String? email,
    String? phoneNumber,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      password: password ?? this.password,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }
}
