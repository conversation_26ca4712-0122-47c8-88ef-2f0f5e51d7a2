import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cars_app/app/services/user_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/user_model.dart';
import '../../../services/firestore_service.dart';
import '../../inventory/controllers/inventory_controller.dart';
import '../../inventory/models/transaction_model.dart';

class TechnicianController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth auth = Get.find();
  // final LocalStorageService localStorageService = Get.find();
  final RxBool isLoading = false.obs;
  final RxString uid = ''.obs;
  final RxString userName = ''.obs;
  final RxString userEmail = ''.obs;
  final LocalStorageService _storageService = LocalStorageService();
  final RxString selectedRequestId = ''.obs;
  final RxString notes = ''.obs;
  final RxInt totalCars = 0.obs;
  final RxInt pendingCars = 0.obs;
  final RxInt completedCars = 0.obs;
  final RxInt inProgressCars = 0.obs;
  final RxBool isAdmin = false.obs;
  final FirestoreService _service = Get.find();

  Stream<List<Car>> getCarsByStatus(String status, String technicianId) {
    Logger().e(technicianId);
    return _firestore
        .collection('cars')
        .where('status', isEqualTo: status)
        // .where('technicianId', isEqualTo: technicianId)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Car.fromJson({
                  'id': doc.id,
                  ...doc.data(),
                }))
            .toList());
  }

  // Using hardcoded email as requested
  Stream<List<Car>> get pendingRequestsStream =>
      getCarsByStatus('pending', isAdmin.value ? '<EMAIL>' : '${userName.value}@hilal.com');
  @override
  void onInit() {
    super.onInit();

    isAdmin.value = Get.arguments['isAdmin'] ?? false;
    // Log the hardcoded email that we're using for filtering
  }


  // Legacy method for backwards compatibility
  Future<void> updateMaintenanceRequest(Car car, String notes) async {
    await updateMaintenanceRequestWithProducts(car, notes, []);
  }

  // Updated method that handles product usage
  Future<void> updateMaintenanceRequestWithProducts(
      Car car, String notes, List<Map<String, dynamic>> usedProducts) async {
    try {
      // Start a batch to handle both car update and inventory transactions
      final batch = _firestore.batch();

      // 1. Update the car maintenance status
      List<WorkshopEntry> updatedWorkshopHistory =
          List.from(car.workshopHistory);

      // Create the new status entry with used products
      final newStatus = WorkshopEntryStatus(
        status: CarStatus.done,
        createAt: DateTime.now(),
        senderId: uid.value,
        senderName: userName.value,
        notes: notes,
        usedProducts: usedProducts,
      );

      // Add the new status to the workshop history
      updatedWorkshopHistory.last.statuses = [
        ...updatedWorkshopHistory.last.statuses,
        newStatus,
      ];
      _service.createNoticiation(UserRole.supervisor.name, [], car, 'newRequest');

      // Prepare the car update
      Logger().d(car.id);
      final carRef = _firestore.collection('cars').doc(car.id);
      batch.update(carRef, {
        'status': 'done',
        'notes': notes,
        'workshopEntries':
            updatedWorkshopHistory.map((entry) => entry.toJson()).toList(),
        'completionDate': FieldValue.serverTimestamp(),
      });

      // 2. Process inventory transactions for used products
      if (usedProducts.isNotEmpty) {
        try {
          // Get inventory controller to access product data
          final inventoryController = Get.find<InventoryController>();

          // Create usage transactions for each product
          for (final productData in usedProducts) {
            final productId = productData['productId'] as String?;
            final productName = productData['productName'] as String;
            final quantity = productData['quantity'] as int;

            if (productId != null && quantity > 0) {
              // Find the product in inventory
              final product = inventoryController.products
                  .firstWhereOrNull((p) => p.id == productId);

              if (product != null) {
                // Create a transaction document
                final transactionRef =
                    _firestore.collection('transactions').doc();

                // Prepare transaction data
                final transactionData = {
                  'productId': productId,
                  'productName': productName,
                  'quantity': -quantity, // Negative for usage
                  'type': TransactionType.usage.name,
                  'notes':
                      'Used for car maintenance: ${car.carModel} (${car.plateNumber})',
                  'previousQuantity': product.currentQuantity,
                  'newQuantity': product.currentQuantity - quantity,
                  'createdBy': uid.value,
                  'createdByName': userName.value,
                  'timestamp': FieldValue.serverTimestamp(),
                  'maintenanceId': car.id,
                };

                // Add transaction to batch
                batch.set(transactionRef, transactionData);

                // Update product quantity
                final productRef =
                    _firestore.collection('products').doc(productId);
                batch.update(productRef, {
                  'currentQuantity': FieldValue.increment(-quantity),
                  'updatedAt': FieldValue.serverTimestamp(),
                });
              }
            }
          }
        } catch (e) {
          Logger().e('Error processing inventory: $e');
          // Continue with car update even if inventory update fails
        }
      }

      // Commit all changes
      await batch.commit();

      // Success message
      Get.snackbar(
        'success'.tr,
        'maintenance_request_updated'.tr,
        backgroundColor: Get.theme.colorScheme.surface,
        colorText: Get.theme.colorScheme.onSurface,
      );
    } catch (e) {
      Logger().e('Error updating maintenance request: $e');
      Get.snackbar(
        'error'.tr,
        'failed_to_update_request'.tr,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }
}
