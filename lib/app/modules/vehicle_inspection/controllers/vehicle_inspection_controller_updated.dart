import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/vehicle_inspection.dart';
import '../../../data/models/inspection_status.dart';
import '../../../controllers/auth_controller.dart';
import '../models/car_part.dart';

class VehicleInspectionController extends GetxController {
  final inspection = VehicleInspection().obs;
  final isLoading = false.obs;
  final TextEditingController noteController = TextEditingController();
  final TextEditingController meterReadingController = TextEditingController();
  
  // Selected car parts
  final RxList<CarPart> selectedParts = <CarPart>[].obs;
  
  @override
  void onInit() {
    super.onInit();
    // Initialize with a new inspection
    inspection.value = VehicleInspection();
  }

  bool validateForm() {
    // No need to validate all items anymore
    return true;
  }
  
  // Add a selected part
  void addSelectedPart(CarPart part) {
    // Check if part is already in the list
    final existingIndex = selectedParts.indexWhere((p) => p.id == part.id);
    
    if (existingIndex >= 0) {
      // Update existing part
      selectedParts[existingIndex] = part;
    } else {
      // Add new part
      selectedParts.add(part);
    }
  }

  Future<void> saveInspection() async {
    if (!validateForm()) {
      Get.snackbar(
        'error'.tr,
        'allFieldsRequired'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      isLoading.value = true;
      
      // Update inspection data
      inspection.value.note = noteController.text;
      
      // Add meter reading if provided
      if (meterReadingController.text.isNotEmpty) {
        inspection.value.meterReading = int.tryParse(meterReadingController.text) ?? 0;
      }
      
      // Add selected parts data to inspection
      final partsData = selectedParts.map((part) => {
        'id': part.id,
        'name': part.name,
        'view': part.view.toString(),
        'damageLevel': part.damageLevel ?? 0,
        'notes': part.notes ?? '',
      }).toList();
      
      inspection.value.damagedParts = partsData;
      
      // Return the inspection data
      Get.back(result: inspection.value);
      
      // Show success message
      Get.snackbar(
        'success'.tr,
        'inspection_saved'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  @override
  void dispose() {
    noteController.dispose();
    meterReadingController.dispose();
    super.dispose();
  }
}
