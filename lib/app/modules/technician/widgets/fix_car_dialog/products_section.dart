import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../themes/app_colors.dart';
import '../../../inventory/controllers/inventory_controller.dart';
import '../../../inventory/models/product_model.dart';
import 'ProductSelectionDialog.dart';
import 'selected_product_item.dart'; // Import the new widget
import 'product_selection_dialog.dart'; // Import the selection dialog

/// Products selection header and list widget for the main dialog
class ProductsSection extends StatelessWidget {
  final InventoryController inventoryController;
  final RxList<Map<String, dynamic>> selectedProducts; // RxList of maps
  final RxBool isLoadingProducts;

  const ProductsSection({
    Key? key,
    required this.inventoryController,
    required this.selectedProducts,
    required this.isLoadingProducts,
  }) : super(key: key);

  // Shows the product selection dialog
  void _showProductSelectionDialog(BuildContext context) {
    Get.dialog(
      ProductSelectionDialog(
        inventoryController: inventoryController,
        selectedProducts: selectedProducts, // Pass the shared list
      ),
      // barrierDismissible: false, // Or true, depending on desired behavior
    );
  }

  // Builds the add product button
  Widget _buildAddProductButton(BuildContext context) {
    // Observe isLoadingProducts to disable the button while loading
    return Obx(() => ElevatedButton.icon(
      onPressed: isLoadingProducts.value ? null : () => _showProductSelectionDialog(context),
      icon: const Icon(Icons.add, size: 18),
      label: Text(
        'add_products'.tr,
        style: TextStyle(fontSize: 14.sp),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r)),
        elevation: 2,
        disabledBackgroundColor: AppColors.primary.withOpacity(0.5), // Style for disabled state
        disabledForegroundColor: Colors.white.withOpacity(0.5),
      ),
    ));
  }

  // Builds the list of selected products
  Widget _buildProductsList(BuildContext context) {
    // Observe both loading state and the selectedProducts list
    return Obx(() {
      if (isLoadingProducts.value) {
        return Center(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: const CircularProgressIndicator(),
          ),
        );
      } else if (selectedProducts.isEmpty) {
        return Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            color: AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: AppColors.border),
          ),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 40.r,
                  color: AppColors.textSecondary,
                ),
                SizedBox(height: 8.h),
                Text(
                  'no_products_selected'.tr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      } else {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: selectedProducts.length,
            separatorBuilder: (_, __) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final item = selectedProducts[index];
              final product = item['product'] as ProductModel;
              final quantityRx = item['quantity'] as RxInt; // Get the RxInt

              // Use the dedicated widget for list items
              return SelectedProductItem(
                product: product,
                quantity: quantityRx, // Pass the RxInt directly
                onRemove: () {
                  selectedProducts.removeAt(index);
                },
              );
            },
          ),
        );
      }
    });
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // For very small screens, use a column layout instead of row
              if (constraints.maxWidth < 300) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'used_products'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    SizedBox(
                      width: double.infinity,
                      child: _buildAddProductButton(context),
                    ),
                  ],
                );
              }

              // For normal screens, use a row layout with flexible spacing
              return Row(
                children: [
                  Expanded(
                    child: Text(
                      'used_products'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  _buildAddProductButton(context),
                ],
              );
            },
          ),
        ),
        SizedBox(height: 8.h),
        _buildProductsList(context),
      ],
    );
  }
}