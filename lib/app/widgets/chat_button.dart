import 'package:cars_app/app/data/models/car_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../routes/app_pages.dart';
import '../themes/app_colors.dart';

class ChatButton extends StatelessWidget {
   ChatButton({super.key, required this.car, required this.senderId, required this.receiverId});
  final Car car;
  final String senderId;
  final String receiverId;
  @override
  Widget build(BuildContext context) {

      return InkWell(
        onTap: () {
          Get.toNamed(
            Routes.CHAT,
            arguments: {
              'currentRoomId': car.workshopHistory.last.id,
              'currentUserId': senderId,
              'otherUserId': receiverId,
            },
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.primary.withOpacity(0.2)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.chat_bubble_outline, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'فتح المحادثة'.tr,
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      );
    }

  }

