import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../themes/app_colors.dart';

class CustomSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? activeTrackColor;
  final Color? inactiveTrackColor;
  final double? width;
  final double? height;
  final Duration animationDuration;
  final bool showLabels;
  final String? activeLabel;
  final String? inactiveLabel;

  const CustomSwitch({
    Key? key,
    required this.value,
    required this.onChanged,
    this.activeColor,
    this.inactiveColor,
    this.activeTrackColor,
    this.inactiveTrackColor,
    this.width,
    this.height,
    this.animationDuration = const Duration(milliseconds: 200),
    this.showLabels = false,
    this.activeLabel,
    this.inactiveLabel,
  }) : super(key: key);

  @override
  State<CustomSwitch> createState() => _CustomSwitchState();
}

class _CustomSwitchState extends State<CustomSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _thumbAnimation;
  late Animation<Color?> _trackColorAnimation;
  late Animation<Color?> _thumbColorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _thumbAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _updateAnimations();

    if (widget.value) {
      _animationController.value = 1.0;
    }
  }

  void _updateAnimations() {
    final isDark = Get.isDarkMode;
    
    final activeTrackColor = widget.activeTrackColor ?? 
        AppColors.primary.withOpacity(0.5);
    final inactiveTrackColor = widget.inactiveTrackColor ?? 
        AppColors.getBorder(isDark);
    
    final activeThumbColor = widget.activeColor ?? AppColors.primary;
    final inactiveThumbColor = widget.inactiveColor ?? 
        AppColors.getTextTertiary(isDark);

    _trackColorAnimation = ColorTween(
      begin: inactiveTrackColor,
      end: activeTrackColor,
    ).animate(_animationController);

    _thumbColorAnimation = ColorTween(
      begin: inactiveThumbColor,
      end: activeThumbColor,
    ).animate(_animationController);
  }

  @override
  void didUpdateWidget(CustomSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.value != widget.value) {
      if (widget.value) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
    
    _updateAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    widget.onChanged(!widget.value);
  }

  @override
  Widget build(BuildContext context) {
    final switchWidth = widget.width ?? 50.w;
    final switchHeight = widget.height ?? 28.h;
    final thumbSize = switchHeight - 4.w;

    if (widget.showLabels) {
      return _buildLabeledSwitch(switchWidth, switchHeight, thumbSize);
    }

    return _buildSwitch(switchWidth, switchHeight, thumbSize);
  }

  Widget _buildSwitch(double width, double height, double thumbSize) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(height / 2),
              color: _trackColorAnimation.value,
              border: Border.all(
                color: AppColors.getBorder(Get.isDarkMode),
                width: 1,
              ),
            ),
            child: Stack(
              children: [
                AnimatedPositioned(
                  duration: widget.animationDuration,
                  curve: Curves.easeInOut,
                  left: _thumbAnimation.value * (width - thumbSize - 2.w) + 2.w,
                  top: 2.h,
                  child: Container(
                    width: thumbSize,
                    height: thumbSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _thumbColorAnimation.value,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLabeledSwitch(double width, double height, double thumbSize) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.inactiveLabel != null) ...[
          Text(
            widget.inactiveLabel!,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.value 
                  ? AppColors.getTextTertiary(Get.isDarkMode)
                  : AppColors.getTextPrimary(Get.isDarkMode),
              fontWeight: widget.value ? FontWeight.normal : FontWeight.w500,
            ),
          ),
          SizedBox(width: 12.w),
        ],
        _buildSwitch(width, height, thumbSize),
        if (widget.activeLabel != null) ...[
          SizedBox(width: 12.w),
          Text(
            widget.activeLabel!,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.value 
                  ? AppColors.getTextPrimary(Get.isDarkMode)
                  : AppColors.getTextTertiary(Get.isDarkMode),
              fontWeight: widget.value ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ],
      ],
    );
  }
}

// Theme Switch Widget for switching between light and dark themes
class ThemeSwitch extends StatelessWidget {
  final bool isDarkMode;
  final ValueChanged<bool> onChanged;

  const ThemeSwitch({
    Key? key,
    required this.isDarkMode,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomSwitch(
      value: isDarkMode,
      onChanged: onChanged,
      showLabels: true,
      activeLabel: "🌙",
      inactiveLabel: "☀️",
      width: 60.w,
      height: 32.h,
      activeColor: AppColors.primary,
      activeTrackColor: AppColors.primary.withOpacity(0.3),
    );
  }
}

// Settings Switch Widget with title and description
class SettingsSwitch extends StatelessWidget {
  final String title;
  final String? description;
  final bool value;
  final ValueChanged<bool> onChanged;
  final IconData? icon;

  const SettingsSwitch({
    Key? key,
    required this.title,
    this.description,
    required this.value,
    required this.onChanged,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 24.r,
              color: AppColors.getTextSecondary(Get.isDarkMode),
            ),
            SizedBox(width: 16.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.getTextPrimary(Get.isDarkMode),
                  ),
                ),
                if (description != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    description!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.getTextSecondary(Get.isDarkMode),
                    ),
                  ),
                ],
              ],
            ),
          ),
          SizedBox(width: 16.w),
          CustomSwitch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}
