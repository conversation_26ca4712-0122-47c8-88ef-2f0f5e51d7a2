import 'package:cars_app/app/controllers/app_users_controller.dart';
import 'package:cars_app/app/controllers/theme_controller.dart';
import 'package:cars_app/app/services/car_rotation_service.dart';
import 'package:cars_app/app/utils/connection_utils.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'app/modules/notifications/controllers/notifications_controller.dart';
import 'app/routes/app_pages.dart';
import 'app/services/notification_service_selector.dart';
import 'app/services/sector_service.dart';
import 'app/services/service_bindings.dart';
import 'app/services/storage_service.dart';
import 'app/services/firestore_service.dart';
import 'app/data/language_controller.dart';
import 'app/translations/app_translations.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage
  await GetStorage.init();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize connection monitoring
  ConnectionUtils.initConnectionListener();
  // Initialize Notification Service
  await Get.putAsync<NotificationService>(() async {
    final service = NotificationService();
    await service.init();
    return service;
  });
  Get.lazyPut(() => NotificationsController(), fenix: true);
  Get.lazyPut(() => CarRotationService(), fenix: true);
  // Initialize Services
  await Get.putAsync<StorageService>(() async {
    final service = StorageService();
    await service.init();
    return service;
  });

  // Initialize Theme Controller
  final themeController = Get.put(ThemeController());

  // Initialize Language Controller
  final languageController = Get.put(LanguageController());
  languageController.loadSelectedLanguage();

  // Initialize Firestore Service
  Get.put<FirestoreService>(FirestoreService());
  Get.lazyPut(
    () => SectorService(),
  );
  Get.putAsync(
    () => CarRotationService().init(),
  );

  Get.lazyPut<FirebaseAuth>(() => FirebaseAuth.instance, fenix: true);
  Get.lazyPut(() => AppUsersController(), fenix: true);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetBuilder<ThemeController>(
          builder: (themeController) => GetBuilder<LanguageController>(
            builder: (languageController) => GetMaterialApp(
              title: "Cars App",
              initialRoute: AppPages.INITIAL,
              initialBinding: ServiceBindings(),
              getPages: AppPages.routes,
              theme: languageController.getTheme,
              darkTheme: languageController.getTheme,
              themeMode: themeController.themeMode,
              debugShowCheckedModeBanner: false,
              defaultTransition: Transition.fadeIn,
              opaqueRoute: true,
              enableLog: true,
              popGesture: true,
              translations: AppTranslations(),
              locale: languageController.isArabic
                  ? const Locale('ar', 'SA')
                  : const Locale('en', 'US'),
              fallbackLocale: const Locale('ar', 'SA'),
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en', 'US'),
                Locale('ar', 'SA'),
              ],
            ),
          ),
        );
      },
    );
  }
}
