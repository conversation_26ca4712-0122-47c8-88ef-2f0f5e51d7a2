import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../data/models/car_model.dart';
import '../../modules/reports/controllers/reports_controller.dart';

/// Helper class containing common methods for PDF generation
class PdfHelpers {
  /// Load the Arabic font
  static Future<pw.Font> loadArabicFont() async {
    final arabicFont = await rootBundle.load('assets/fonts/NotoSansArabic.ttf');
    return pw.Font.ttf(arabicFont);
  }

  /// Define common PDF colors
  static const PdfColor primaryColor =
      PdfColor.fromInt(0xFFFF0048); // AppColors.primary
  static const PdfColor accentColor =
      PdfColor.fromInt(0xFFFF7043); // AppColors.accent
  static const PdfColor backgroundColor =
      PdfColor.fromInt(0xFFF8F9FA); // AppColors.background
  static const PdfColor textColor =
      PdfColor.fromInt(0xFF212121); // AppColors.textPrimary
  static const PdfColor textSecondaryColor =
      PdfColor.fromInt(0xFF616161); // AppColors.textSecondary
  static const PdfColor borderColor =
      PdfColor.fromInt(0xFFE0E0E0); // AppColors.border
  static const PdfColor successColor =
      PdfColor.fromInt(0xFF43A047); // AppColors.success
  static const PdfColor errorColor =
      PdfColor.fromInt(0xFFE53935); // AppColors.error
  static const PdfColor warningColor =
      PdfColor.fromInt(0xFFFFB300); // AppColors.warning
  static const PdfColor infoColor =
      PdfColor.fromInt(0xFF039BE5); // AppColors.info

  /// Create a section header
  static pw.Widget sectionHeader(String title, pw.Font arabicTtf) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
      decoration: const pw.BoxDecoration(
        color: primaryColor,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(4)),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          color: PdfColors.white,
          fontSize: 16,
          fontWeight: pw.FontWeight.bold,
          font: arabicTtf,
        ),
      ),
    );
  }

  /// Create an info card
  static pw.Widget infoCard(String title, String value, pw.Font arabicTtf,
      {PdfColor? color}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
        border: pw.Border.all(color: color ?? borderColor, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              color: color ?? textSecondaryColor,
              fontSize: 10,
              font: arabicTtf,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            value,
            style: pw.TextStyle(
              color: color ?? textColor,
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              font: arabicTtf,
            ),
          ),
        ],
      ),
    );
  }

  /// Create a compact info row
  static pw.Widget compactInfoRow(
      String label, String value, pw.Font font, PdfColor textColor) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          '$label:',
          style: pw.TextStyle(
            color: textColor.shade(50),
            fontSize: 8,
            font: font,
          ),
        ),
        pw.SizedBox(width: 4),
        pw.Expanded(
          child: pw.Text(
            value,
            style: pw.TextStyle(
              color: textColor,
              fontSize: 8,
              font: font,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// Build a status legend item
  static pw.Widget buildStatusLegendItem(
      pw.Font font, String label, PdfColor color) {
    return pw.Row(
      children: [
        pw.Container(
          width: 8,
          height: 8,
          decoration: pw.BoxDecoration(
            color: color,
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(1)),
          ),
        ),
        pw.SizedBox(width: 2),
        pw.Text(
          label,
          style: pw.TextStyle(
            font: font,
            fontSize: 8,
          ),
        ),
      ],
    );
  }

  /// Generate status distribution data for PDF
  static List<List<String>> generateStatusDistributionData(
      List<StatusCount> statusDistribution) {
    final total =
        statusDistribution.fold<int>(0, (sum, status) => sum + status.count);

    return statusDistribution.map((status) {
      final percentage = (status.count / total * 100).toStringAsFixed(1);
      return [
        status.status.toString().split('.').last,
        status.count.toString(),
        '$percentage%',
      ];
    }).toList();
  }

  /// Create a standard PDF document header
  static pw.Widget buildHeader(
      pw.Context context, pw.Font arabicTtf, String title) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: const pw.Border(
            bottom: pw.BorderSide(color: borderColor, width: 1)),
      ),
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.only(bottom: 10),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'نظام إدارة المركبات',
                style: pw.TextStyle(
                  color: primaryColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                  font: arabicTtf,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                title,
                style: pw.TextStyle(
                  color: textSecondaryColor,
                  fontSize: 12,
                  font: arabicTtf,
                ),
              ),
            ],
          ),
          pw.Text(
            'تاريخ التقرير: ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
            style: pw.TextStyle(
              color: textSecondaryColor,
              fontSize: 10,
              font: arabicTtf,
            ),
          ),
        ],
      ),
    );
  }

  /// Create a standard PDF document footer
  static pw.Widget buildFooter(pw.Context context, pw.Font arabicTtf) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(top: 10),
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: pw.BoxDecoration(
        border:
            const pw.Border(top: pw.BorderSide(color: borderColor, width: 0.5)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: pw.TextStyle(
              color: textSecondaryColor,
              fontSize: 10,
              font: arabicTtf,
            ),
          ),
          pw.Text(
            'تم إنشاء التقرير بواسطة نظام إدارة المركبات',
            style: pw.TextStyle(
              color: textSecondaryColor,
              fontSize: 10,
              font: arabicTtf,
            ),
          ),
        ],
      ),
    );
  }
}
