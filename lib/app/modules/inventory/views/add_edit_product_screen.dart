// add_edit_product_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../data/language_controller.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/custom_button.dart';
import '../controllers/inventory_controller.dart';
import '../models/product_model.dart';

class AddEditProductScreen extends StatefulWidget {
  const AddEditProductScreen({Key? key}) : super(key: key);

  @override
  AddEditProductScreenState createState() => AddEditProductScreenState();
}

class AddEditProductScreenState extends State<AddEditProductScreen> {
  final InventoryController controller = Get.find<InventoryController>();
  final _formKey = GlobalKey<FormState>();
  final LanguageController _languageController = Get.find<LanguageController>();

  // Text Editing Controllers
  final _nameController = TextEditingController();
  final _skuController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _lowStockThresholdController = TextEditingController();
  // Quantity is managed via transactions, not directly here unless adding initial stock
  final _initialQuantityController =
      TextEditingController(text: '0'); // For adding new

  ProductModel? _editingProduct; // Store the product being edited
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    // Check if editing an existing product
    if (Get.arguments is ProductModel) {
      _editingProduct = Get.arguments as ProductModel;
      _isEditMode = true;
      _populateFields();
    }
  }

  void _populateFields() {
    if (_editingProduct != null) {
      _nameController.text = _editingProduct!.name;
      _skuController.text = _editingProduct!.sku ?? '';
      _descriptionController.text = _editingProduct!.description ?? '';
      _purchasePriceController.text =
          _editingProduct!.purchasePrice?.toString() ?? '';
      _sellingPriceController.text =
          _editingProduct!.sellingPrice?.toString() ?? '';
      _lowStockThresholdController.text =
          _editingProduct!.lowStockThreshold?.toString() ?? '';
      // Don't populate initial quantity in edit mode, quantity changes via transactions
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _skuController.dispose();
    _descriptionController.dispose();
    _purchasePriceController.dispose();
    _sellingPriceController.dispose();
    _lowStockThresholdController.dispose();
    _initialQuantityController.dispose();
    super.dispose();
  }

  void _saveProduct() {
    if (_formKey.currentState!.validate()) {
      final productData = ProductModel(
        id: _isEditMode ? _editingProduct!.id : null, // Keep ID if editing
        name: _nameController.text.trim(),
        sku: _skuController.text.trim().isNotEmpty
            ? _skuController.text.trim()
            : null,
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : null,
        // Only set initial quantity if adding new product
        currentQuantity: _isEditMode
            ? _editingProduct!.currentQuantity
            : (int.tryParse(_initialQuantityController.text) ?? 0),
        purchasePrice: double.tryParse(_purchasePriceController.text),
        sellingPrice: double.tryParse(_sellingPriceController.text),
        lowStockThreshold: int.tryParse(_lowStockThresholdController.text),
        // createdAt/updatedAt handled by Firestore provider/model
      );

      if (_isEditMode) {
        // Call controller's update method (needs implementation in controller)
        controller.editProduct(productData);
      } else {
        // Call controller's add method
        controller.addProduct(productData);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_isEditMode ? 'edit_product'.tr : 'add_new_product'.tr),
        elevation: 0,
        actions: [
          if (_isEditMode)
            IconButton(
              icon: const Icon(Icons.delete_outline),
              tooltip: 'delete_product'.tr,
              onPressed: () {
                // Show confirmation dialog before deleting
                Get.dialog(
                  AlertDialog(
                    title: Text('delete_product'.tr),
                    content: Text('delete_confirmation'.tr),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                    actions: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: Text('cancel'.tr),
                      ),
                      TextButton(
                        onPressed: () {
                          Get.back();
                          // Implement delete functionality in controller
                          if (_editingProduct?.id != null) {
                            // controller.deleteProduct(_editingProduct!.id!);
                            Get.snackbar('not_implemented'.tr,
                                'delete_not_implemented'.tr,
                                snackPosition: SnackPosition.BOTTOM);
                          }
                        },
                        style: TextButton.styleFrom(
                            foregroundColor: AppColors.error),
                        child: Text('delete_product'.tr),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: GetBuilder<LanguageController>(
        builder: (languageCtrl) {
          final isRtl = languageCtrl.isArabic;
          return Directionality(
            textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Product Info Card
                    Card(
                      elevation: 2,
                      shadowColor: AppColors.shadowColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16)),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Title
                            Text(
                              'basic_information'.tr,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Product Name Field
                            _buildTextField(
                              controller: _nameController,
                              label: 'product_name'.tr,
                              hint: 'enter_product_name'.tr,
                              icon: Icons.inventory_2,
                              isRequired: true,
                              validator: (value) =>
                                  (value == null || value.trim().isEmpty)
                                      ? 'Name is required'.tr
                                      : null,
                            ),
                            const SizedBox(height: 16),

                            // Initial Quantity Field (only for new products)
                            if (!_isEditMode) ...[
                              _buildTextField(
                                controller: _initialQuantityController,
                                label: 'initial_quantity'.tr,
                                hint: 'enter_initial_quantity'.tr,
                                icon: Icons.add_shopping_cart,
                                isRequired: true,
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Initial quantity required'.tr;
                                  }
                                  if (int.tryParse(value) == null) {
                                    return 'Enter a valid number'.tr;
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                            ],

                            // SKU Field
                            _buildTextField(
                              controller: _skuController,
                              label: 'sku'.tr,
                              hint: 'enter_sku'.tr,
                              icon: Icons.qr_code,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Details Card
                    Card(
                      elevation: 2,
                      shadowColor: AppColors.shadowColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16)),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Title
                            Text(
                              'product_details'.tr,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Description Field
                            _buildTextField(
                              controller: _descriptionController,
                              label: 'description'.tr,
                              hint: 'enter_description'.tr,
                              icon: Icons.description,
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),

                            // Pricing Section Title
                            Text(
                              'pricing'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Price Fields (side by side)
                            Row(
                              children: [
                                // Purchase Price
                                Expanded(
                                  child: _buildTextField(
                                    controller: _purchasePriceController,
                                    label: 'purchase_price'.tr,
                                    hint: '0.00',
                                    icon: Icons.attach_money,
                                    prefixText: '\$ ',
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Selling Price
                                Expanded(
                                  child: _buildTextField(
                                    controller: _sellingPriceController,
                                    label: 'selling_price'.tr,
                                    hint: '0.00',
                                    icon: Icons.sell,
                                    prefixText: '\$ ',
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Low Stock Threshold
                            _buildTextField(
                              controller: _lowStockThresholdController,
                              label: 'low_stock_threshold'.tr,
                              hint: 'low_stock_threshold_hint'.tr,
                              icon: Icons.warning_amber,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              helperText: 'low_stock_helper'.tr,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Save Button
                    Obx(() => CustomButton(
                          text: controller.isSavingProduct.value
                              ? 'saving'.tr
                              : 'save_product'.tr,
                          icon: Icons.save,
                          isLoading: controller.isSavingProduct.value,
                          onPressed: _saveProduct,
                          backgroundColor: AppColors.primary,
                        )),

                    const SizedBox(height: 16),

                    // Cancel Button
                    TextButton.icon(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back),
                      label: Text('cancel'.tr),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Helper method to build consistent text fields
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    String? prefixText,
    String? helperText,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator if needed
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              const Text(
                '*',
                style: TextStyle(
                  color: AppColors.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        // Text field with consistent styling
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AppColors.primary, size: 20),
            prefixText: prefixText,
            helperText: helperText,
            helperMaxLines: 2,
            helperStyle: const TextStyle(color: AppColors.textLight, fontSize: 12),
            filled: true,
            fillColor: AppColors.surface,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary, width: 1.5),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.error, width: 1.5),
            ),
          ),
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          maxLines: maxLines,
          validator: validator,
          textDirection: _languageController.isArabic ? TextDirection.rtl : TextDirection.ltr,
          textAlign: _languageController.isArabic ? TextAlign.right : TextAlign.left,
        ),
      ],
    );
  }
}
