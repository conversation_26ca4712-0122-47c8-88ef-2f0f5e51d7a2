// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../data/models/user_model.dart';
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/section_header.dart';
// import '../widgets/user_tile.dart';
//
// class UsersContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const UsersContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Users section header
//         SectionHeader(
//           title: 'users'.tr,
//           subtitle: 'users_subtitle'.tr,
//           icon: Icons.people_outline,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 16),
//
//         // Role filter
//         Obx(() => FilterBar<UserRole>(
//           title: 'filter_by_role'.tr,
//           options: controller.getUserRoleOptions(),
//           selectedValue: controller.userRoleFilter.value,
//           onChanged: controller.setUserRoleFilter,
//           isRTL: isRTL,
//         )),
//         const SizedBox(height: 16),
//
//         // Status filter
//         Obx(() => FilterBar<bool>(
//           title: 'filter_by_status'.tr,
//           options: controller.getUserStatusOptions(),
//           selectedValue: controller.userStatusFilter.value,
//           onChanged: controller.setUserStatusFilter,
//           isRTL: isRTL,
//         )),
//         const SizedBox(height: 16),
//
//         // Users list with loading state
//         Obx(() {
//           if (controller.isUsersLoading.value) {
//             return _buildLoadingList();
//           }
//
//           if (controller.filteredUsers.isEmpty) {
//             return _buildEmptyState();
//           }
//
//           return ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: controller.filteredUsers.length,
//             itemBuilder: (context, index) {
//               final user = controller.filteredUsers[index];
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: UserTile(
//                   user: user,
//                   isRTL: isRTL,
//                   onTap: () => Get.toNamed('/user/${user.id}'),
//                   onEdit: () => Get.toNamed('/user/${user.id}/edit'),
//                   onDelete: () => _showDeleteConfirmation(context, user),
//                   onActivate: user.status!=UserStatus.active ? () => _activateUser(user) : null,
//                   onDeactivate: user.status!=UserStatus.active ? () => _deactivateUser(user) : null,
//                 ),
//               );
//             },
//           );
//         }),
//       ],
//     );
//   }
//
//   // Show delete confirmation dialog
//   void _showDeleteConfirmation(BuildContext context, UserModel user) {
//     Get.defaultDialog(
//       title: 'confirm_delete'.tr,
//       titleStyle: GoogleFonts.cairo(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//       middleText: 'confirm_delete_user'.tr,
//       middleTextStyle: GoogleFonts.cairo(
//         fontSize: 14,
//         color: Colors.black54,
//       ),
//       textConfirm: 'delete'.tr,
//       textCancel: 'cancel'.tr,
//       confirmTextColor: Colors.white,
//       cancelTextColor: AppColors.primary,
//       buttonColor: AppColors.error,
//       onConfirm: () {
//         // Call delete user service
//         Get.back();
//         Get.snackbar(
//           'success'.tr,
//           'user_deleted'.tr,
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: AppColors.success.withOpacity(0.1),
//           colorText: AppColors.success,
//         );
//       },
//     );
//   }
//
//   // Activate user
//   void _activateUser(UserModel user) {
//     // Call user service to activate
//     Get.snackbar(
//       'success'.tr,
//       'user_activated'.tr,
//       snackPosition: SnackPosition.BOTTOM,
//       backgroundColor: AppColors.success.withOpacity(0.1),
//       colorText: AppColors.success,
//     );
//   }
//
//   // Deactivate user
//   void _deactivateUser(UserModel user) {
//     // Call user service to deactivate
//     Get.snackbar(
//       'success'.tr,
//       'user_deactivated'.tr,
//       snackPosition: SnackPosition.BOTTOM,
//       backgroundColor: AppColors.warning.withOpacity(0.1),
//       colorText: AppColors.warning,
//     );
//   }
//
//   // Build loading state
//   Widget _buildLoadingList() {
//     return ListView.builder(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 5,
//       itemBuilder: (context, index) {
//         return Padding(
//           padding: const EdgeInsets.only(bottom: 8),
//           child: Container(
//             height: 120,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.05),
//                   blurRadius: 10,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Shimmer.fromColors(
//               baseColor: Colors.grey[300]!,
//               highlightColor: Colors.grey[100]!,
//               child: Row(
//                 children: [
//                   Container(
//                     width: 48,
//                     height: 48,
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       shape: BoxShape.circle,
//                     ),
//                   ),
//                   const SizedBox(width: 16),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Container(
//                               width: 120,
//                               height: 20,
//                               color: Colors.white,
//                             ),
//                             Container(
//                               width: 60,
//                               height: 24,
//                               decoration: BoxDecoration(
//                                 color: Colors.white,
//                                 borderRadius: BorderRadius.circular(12),
//                               ),
//                             ),
//                           ],
//                         ),
//                         const SizedBox(height: 8),
//                         Container(
//                           width: 180,
//                           height: 16,
//                           color: Colors.white,
//                         ),
//                         const SizedBox(height: 8),
//                         Row(
//                           children: [
//                             Container(
//                               width: 8,
//                               height: 8,
//                               decoration: BoxDecoration(
//                                 color: Colors.white,
//                                 shape: BoxShape.circle,
//                               ),
//                             ),
//                             const SizedBox(width: 8),
//                             Container(
//                               width: 60,
//                               height: 14,
//                               color: Colors.white,
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                   const Icon(Icons.chevron_right, color: Colors.grey),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build empty state
//   Widget _buildEmptyState() {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.person_off_outlined,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'no_users_found'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'no_users_found_description'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 14,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
