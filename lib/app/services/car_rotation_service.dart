import 'package:cars_app/app/services/car_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../data/models/car_model.dart';
import '../data/models/car_rotate.dart';
import 'firestore_service.dart';

class CarRotationService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final CarService carService = Get.find();
  final FirestoreService _service = Get.find();

  Future<CarRotationService> init() async {
    return this;
  }

  Stream<List<CarRotate>> getUnActiveCarsStreamByAction(RotateAction action) {
    return _firestore
        .collection('car_rotations')
        .orderBy('createdAt', descending: true)
        .where('managerAgreeStatus', isEqualTo: 'pending')
        .where('action', isEqualTo: action.name)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return CarRotate.fromJson({...data, 'id': doc.id});
          }).toList();
        });
  }

  Stream<List<CarRotate>> getCarRotations(String sectorId,RotateAction? action) {
    if(action!=null){
      print('gooooooooooooooot${action.toString().split('.').last}');
      return _firestore
          .collection('car_rotations')
          .where('action', isEqualTo: action.toString().split('.').last)
          .where(action==RotateAction.support?'toSectorId':'toSectorId', isEqualTo: sectorId)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          return CarRotate.fromJson({...data, 'id': doc.id});
        }).toList();
      });
    }else if(sectorId.isEmpty){
      return _firestore
          .collection('car_rotations')
          .orderBy('createdAt', descending: true)
          .where('managerAgreeStatus', isEqualTo: 'accepted')
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          return CarRotate.fromJson({...data, 'id': doc.id});
        }).toList();
      });
    }else{
      return _firestore
          .collection('car_rotations')
          .where(Filter.or(
            Filter('toSectorId', isEqualTo: sectorId),
            Filter('fromSectorId', isEqualTo: sectorId),))
          .where('managerAgreeStatus', isEqualTo: 'accepted')
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          return CarRotate.fromJson({...data, 'id': doc.id});
        }).toList();
      });
    }
  }


  Future<void> createCarRotation({
    required Car? car,
    required String fromSectorId,
    required String toSectorId,
    required String fromSectorName,
    required String toSectorName,
    required String cause,
    required RotateType type,
    required RotateAction action,
    required bool isActive,
    RotateStatus? toSector,
    RotateStatus? fromSector
  }) async {
    try {
      _service.createRotationNoticiation('sector_$fromSectorId', [], 'newRequest');
      _service.createRotationNoticiation('sector_$toSectorId', [], 'newRequest');

      await _firestore.collection('car_rotations').add({
        'carId': car?.id??'',
        'fromSectorId': fromSectorId,
        'toSectorId': toSectorId,
        'cause': cause,
        'fromSectorName': fromSectorName,
        'action': action.toString().split('.').last,
        'toSectorName': toSectorName,
        'type': type.toString().split('.').last,
        'managerAgreeStatus': isActive? RotateStatus.accepted.toString().split('.').last: RotateStatus.pending.toString().split('.').last,
        'isFromSectorMangerAgree':fromSector!=null?fromSector.toString().split('.').last: RotateStatus.pending.toString().split('.').last,
        'isToSectorMangerAgree':toSector!=null?toSector.toString().split('.').last: RotateStatus.pending.toString().split('.').last,
        'createdAt': FieldValue.serverTimestamp(),
      });

    } catch (e) {
      print('Error creating car rotation: $e');
      rethrow;
    }
  }  Future<void> updateCarRotation({

    required CarRotate? carRotation,
    required Car? car,
    required String fromSectorId,
    required String toSectorId,
    required String fromSectorName,
    required String toSectorName,
    required String cause,
    required RotateAction action,
    required RotateType type,
    required bool isActive,
    RotateStatus? toSector,
    RotateStatus? fromSector
  }) async {
    try {

      /*
      *
       'fromSectorId': fromSectorId,
        'toSectorId': toSectorId,
        'fromSectorName': fromSectorName,
        'toSectorName': toSectorName,
      * */
      await _firestore.collection('car_rotations').doc(carRotation?.id??'').update({
        'carId': car?.id??'',
        'fromSectorId': fromSectorId,
        'toSectorId': toSectorId,
        'cause': cause,
        'fromSectorName': fromSectorName,
        'toSectorName': toSectorName,
        'managerAgreeStatus': isActive? RotateStatus.accepted.toString().split('.').last: RotateStatus.pending.toString().split('.').last,
        'type': type.toString().split('.').last,
        'isFromSectorMangerAgree':fromSector!=null?fromSector.toString().split('.').last: RotateStatus.pending.toString().split('.').last,
        'createdAt': FieldValue.serverTimestamp(),
      });

    } catch (e) {
      print('Error creating car rotation: $e');
      rethrow;
    }
  }

  Future<void> updateRotationStatus({
    required String rotationId,
    required bool isFromSector,
    required RotateStatus status,
  }) async {
    try {
      final field = isFromSector ? 'isFromSectorMangerAgree' : 'isToSectorMangerAgree';
      final dateField = isFromSector ? 'agreedByFromSectorManagerAt' : 'agreedByToSectorManagerAt';


      print('******************************************************************');

      print('update Rotaion');
      await _firestore.collection('car_rotations').doc(rotationId).update({
        field: status.toString().split('.').last,
        dateField: FieldValue.serverTimestamp(),
      });

      // If both managers have accepted, update the car's sector
      final rotation = await _firestore.collection('car_rotations').doc(rotationId).get();
      final rotationData = rotation.data();
      
      if (rotationData != null &&
          rotationData['isFromSectorMangerAgree'] == RotateStatus.accepted.toString().split('.').last &&
          rotationData['isToSectorMangerAgree'] == RotateStatus.accepted.toString().split('.').last) {
        
        final carDoc = await _firestore.collection('cars').doc(rotationData['carId']).get();
        if (!carDoc.exists) return;

        print(rotationData);
        if (rotationData['type'] == 'permanent') {
          // Update permanent sector
        //   await carDoc.reference.update({
        //     'sectorId': rotationData['toSectorId'],
        //     'tempSectorId': '',
        //   });
        //   if(rotationData['action'] == 'restore'){
        //     await carDoc.reference.update({
        //       'action':'none',
        //     });
        //   }
        // } else {
        //
        //   if(rotationData['action'] == 'restore'){
        //     await carDoc.reference.update({
        //       'action':'none',
        //       'tempSectorId':'',
        //       'toSectorId':rotationData['fromSectorId'],
        //     });
        //   }
        //   // Update temporary sector
        //   print('updating temp sector ${rotationData}');
        //   // await carDoc.reference.update({
          //   'tempSectorId': rotationData['toSectorId'],
          //   'tempSectorName': rotationData['toSectorName'],
          // });
          print('******************************************************************');

          print('paremnt rotation');
          CarRotate rotate = CarRotate.fromJson(rotationData);

          await carDoc.reference.update({
            'sectorId': rotationData['toSectorId'],
            'tempSectorId': '',
            'action':'none',
          });
          // await _firestore.collection('cars').doc(rotate.carId).update({
          //   'action':'none',
          //   'tempSectorId':'',
          //   'beshr'
          //   'toSectorId':rotate.fromSectorId,
          // });

          Logger().d(rotate.toJson());
          Logger().d('permanent rotation');
          Logger().d(rotate.action);



        }else{
          print('******************************************************************');

          CarRotate rotate = CarRotate.fromJson(rotationData);

          print(rotate.action);
          if(rotate.action==RotateAction.restore ){
            print('restore rotaion');

            // await carDoc.reference.update({
            //   'action':'none',
            //   'tempSectorId':'',
            //   'toSectorId':rotate.fromSectorId,
            // });
          }
          else{
            print('******************************************************************');
            print('support rotaion');
            await carDoc.reference.update({
              'tempSectorId': rotationData['toSectorId'],
              'tempSectorName': rotationData['toSectorName'],
              'action':'none',

            });
          }
        }
      }
    } catch (e) {
      print('Error updating rotation status: $e');
      rethrow;
    }
  }
  Future<void> updateRotationData({
    required String rotationId,
    required Map<String, dynamic> data,
  }) async {
    try {

      await _firestore.collection('car_rotations').doc(rotationId).update(data);

    } catch (e) {
      print('Error updating rotation status: $e');
      rethrow;
    }
  }

  Future<void> deleteCarRotation(String rotationId) async {
    try {
      await _firestore.collection('car_rotations').doc(rotationId).delete();
    } catch (e) {
      print('Error deleting car rotation: $e');
      rethrow;
    }
  }

  void activeRotateCar(CarRotate carRotate, bool agree) {
    _firestore.collection('car_rotations').doc(carRotate.id).update({
      'managerAgreeStatus':agree? RotateStatus.accepted.toString().split('.').last: RotateStatus.rejected.toString().split('.').last,
    });
  }
  void updateRotation(CarRotate carRotate, bool agree) {
    _firestore.collection('car_rotations').doc(carRotate.id).update({
      'managerAgreeStatus':agree? RotateStatus.accepted.toString().split('.').last: RotateStatus.rejected.toString().split('.').last,
    });
    if(carRotate.action!=RotateAction.support){
      _firestore.collection('cars').doc(carRotate.carId).update({
        'action':'none',
      });

    }

  }


}