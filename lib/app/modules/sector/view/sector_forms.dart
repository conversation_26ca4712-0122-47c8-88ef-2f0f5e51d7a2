import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/sector_model.dart';
import '../controller/sector_controller.dart';
import '../../user/controller/user_controller.dart';

class AddSectorForm extends GetView<SectorController> {
  final _formKey = GlobalKey<FormState>();
  final userController = Get.find<UserController>();

  AddSectorForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController();
    final selectedManagerId = ''.obs;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Add New Sector',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Sector Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) =>
                  value == null || value.isEmpty ? 'Please enter sector name' : null,
            ),
            const SizedBox(height: 16),
            Obx(() => DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Manager',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedManagerId.value.isEmpty ? null : selectedManagerId.value,
                  items: userController.users
                      .map((user) => DropdownMenuItem<String>(
                            value: user.id,
                            child: Text(user.username),
                          ))
                      .toList(),
                  onChanged: (value) => selectedManagerId.value = value ?? '',
                  validator: (value) =>
                      value == null || value.isEmpty ? 'Please select a manager' : null,
                )),
            const SizedBox(height: 24),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
              ),
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  controller.createSector(
                    nameController.text,
                    selectedManagerId.value,
                  );
                  Get.back();
                }
              },
              child: const Text('Add Sector'),
            ),
          ],
        ),
      ),
    );
  }
}

class EditSectorForm extends GetView<SectorController> {
  final Sector sector;
  final _formKey = GlobalKey<FormState>();
  final userController = Get.find<UserController>();

  EditSectorForm({Key? key, required this.sector}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController(text: sector.name);
    final selectedManagerId = sector.managerId.obs;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Edit Sector',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Sector Name',
                border: OutlineInputBorder(),
              ),
              validator: (value) =>
                  value == null || value.isEmpty ? 'Please enter sector name' : null,
            ),
            const SizedBox(height: 16),
            Obx(() => DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Manager',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedManagerId.value,
                  items: userController.users
                      .map((user) => DropdownMenuItem<String>(
                            value: user.id,
                            child: Text(user.username),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null && value != selectedManagerId.value) {
                      _showChangeManagerDialog(context, value);
                    }
                  },
                )),
            const SizedBox(height: 24),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
              ),
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  controller.updateSector(
                    sector.id,
                    name: nameController.text,
                  );
                  Get.back();
                }
              },
              child: const Text('Update Sector'),
            ),
          ],
        ),
      ),
    );
  }

  void _showChangeManagerDialog(BuildContext context, String newManagerId) {
    final reasonController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Change Manager'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: reasonController,
                decoration: const InputDecoration(
                  labelText: 'Reason for Change',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: const InputDecoration(
                  labelText: 'Additional Notes',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Confirm Change'),
              onPressed: () {
                controller.changeManager(
                  sector.id,
                  newManagerId,
                  reason: reasonController.text.isNotEmpty ? reasonController.text : null,
                  notes: notesController.text.isNotEmpty ? notesController.text : null,
                );
                Navigator.of(context).pop();
                Get.back();
              },
            ),
          ],
        );
      },
    );
  }
}
