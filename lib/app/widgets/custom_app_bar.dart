import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../themes/app_colors.dart';

enum AppBarVariant {
  normal,
  gradient,
  transparent,
  elevated,
}

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final AppBarVariant variant;
  final List<Color>? gradientColors;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;
  final double? titleSpacing;
  final double? leadingWidth;
  final double? toolbarHeight;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final bool pinned;
  final bool floating;
  final bool snap;

  const CustomAppBar({
    Key? key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.variant = AppBarVariant.normal,
    this.gradientColors,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.titleSpacing,
    this.leadingWidth,
    this.toolbarHeight,
    this.showBackButton = true,
    this.onBackPressed,
    this.systemOverlayStyle,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
  }) : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);

  Color _getBackgroundColor(bool isDark) {
    if (backgroundColor != null) return backgroundColor!;
    
    switch (variant) {
      case AppBarVariant.normal:
        return AppColors.getSurface(isDark);
      case AppBarVariant.gradient:
        return Colors.transparent;
      case AppBarVariant.transparent:
        return Colors.transparent;
      case AppBarVariant.elevated:
        return AppColors.getSurface(isDark);
    }
  }

  Color _getForegroundColor(bool isDark) {
    if (foregroundColor != null) return foregroundColor!;
    
    switch (variant) {
      case AppBarVariant.gradient:
        return Colors.white;
      case AppBarVariant.transparent:
        return AppColors.getTextPrimary(isDark);
      default:
        return AppColors.getTextPrimary(isDark);
    }
  }

  double _getElevation() {
    if (elevation != null) return elevation!;
    
    switch (variant) {
      case AppBarVariant.normal:
        return 0;
      case AppBarVariant.gradient:
        return 0;
      case AppBarVariant.transparent:
        return 0;
      case AppBarVariant.elevated:
        return 4;
    }
  }

  SystemUiOverlayStyle _getSystemOverlayStyle(bool isDark) {
    if (systemOverlayStyle != null) return systemOverlayStyle!;
    
    switch (variant) {
      case AppBarVariant.gradient:
        return SystemUiOverlayStyle.light;
      case AppBarVariant.transparent:
        return isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;
      default:
        return isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;
    }
  }

  Widget? _buildLeading(bool isDark) {
    if (leading != null) return leading;
    
    if (showBackButton && (Get.routing.previous.isNotEmpty || automaticallyImplyLeading)) {
      return IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: _getForegroundColor(isDark),
          size: 20.r,
        ),
        onPressed: onBackPressed ?? () => Get.back(),
      );
    }
    
    return null;
  }

  Widget? _buildTitle(bool isDark) {
    if (titleWidget != null) return titleWidget;
    
    if (title != null) {
      return Text(
        title!,
        style: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: _getForegroundColor(isDark),
          letterSpacing: 0.15,
        ),
      );
    }
    
    return null;
  }

  List<Widget>? _buildActions(bool isDark) {
    if (actions == null) return null;
    
    return actions!.map((action) {
      if (action is IconButton) {
        return IconButton(
          icon: action.icon,
          onPressed: action.onPressed,
          color: _getForegroundColor(isDark),
        );
      }
      return action;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Get.isDarkMode;
    
    Widget appBar = AppBar(
      title: _buildTitle(isDark),
      leading: _buildLeading(isDark),
      actions: _buildActions(isDark),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: _getBackgroundColor(isDark),
      foregroundColor: _getForegroundColor(isDark),
      elevation: _getElevation(),
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      leadingWidth: leadingWidth,
      toolbarHeight: toolbarHeight ?? kToolbarHeight,
      systemOverlayStyle: _getSystemOverlayStyle(isDark),
    );

    if (variant == AppBarVariant.gradient) {
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradientColors ?? AppColors.primaryGradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: appBar,
      );
    }

    return appBar;
  }
}

// Sliver version of CustomAppBar
class CustomSliverAppBar extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final AppBarVariant variant;
  final List<Color>? gradientColors;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;
  final double? titleSpacing;
  final double? leadingWidth;
  final double? toolbarHeight;
  final double? expandedHeight;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final bool pinned;
  final bool floating;
  final bool snap;
  final Widget? flexibleSpace;

  const CustomSliverAppBar({
    Key? key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.variant = AppBarVariant.normal,
    this.gradientColors,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.titleSpacing,
    this.leadingWidth,
    this.toolbarHeight,
    this.expandedHeight,
    this.showBackButton = true,
    this.onBackPressed,
    this.systemOverlayStyle,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
    this.flexibleSpace,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Get.isDarkMode;
    final customAppBar = CustomAppBar(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      variant: variant,
      gradientColors: gradientColors,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      leadingWidth: leadingWidth,
      toolbarHeight: toolbarHeight,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      systemOverlayStyle: systemOverlayStyle,
    );

    return SliverAppBar(
      title: customAppBar._buildTitle(isDark),
      leading: customAppBar._buildLeading(isDark),
      actions: customAppBar._buildActions(isDark),
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: customAppBar._getBackgroundColor(isDark),
      foregroundColor: customAppBar._getForegroundColor(isDark),
      elevation: customAppBar._getElevation(),
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      leadingWidth: leadingWidth,
      toolbarHeight: toolbarHeight ?? kToolbarHeight,
      expandedHeight: expandedHeight,
      systemOverlayStyle: customAppBar._getSystemOverlayStyle(isDark),
      pinned: pinned,
      floating: floating,
      snap: snap,
      flexibleSpace: flexibleSpace ?? (variant == AppBarVariant.gradient
          ? Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: gradientColors ?? AppColors.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            )
          : null),
    );
  }
}
