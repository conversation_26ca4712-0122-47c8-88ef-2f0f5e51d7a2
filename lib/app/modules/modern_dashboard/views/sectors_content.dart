// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/section_header.dart';
// import '../widgets/sector_card.dart';
//
// class SectorsContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const SectorsContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Sectors section header
//         SectionHeader(
//           title: 'sectors'.tr,
//           subtitle: 'sectors_subtitle'.tr,
//           icon: Icons.location_on_outlined,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 16),
//
//         // Search filter
//         Obx(() => FilterBar<String>(
//           title: 'search_sectors'.tr,
//           options: const [],
//           selectedValue: null,
//           onChanged: (_) {},
//           showClearButton: false,
//           isRTL: isRTL,
//           searchController: TextEditingController(text: controller.searchQuery.value),
//           onSearch: controller.setSearchQuery,
//         )),
//         const SizedBox(height: 16),
//
//         // Sectors list with loading state
//         Obx(() {
//           if (controller.isSectorsLoading.value) {
//             return _buildLoadingList();
//           }
//
//           if (controller.filteredSectors.isEmpty) {
//             return _buildEmptyState();
//           }
//
//           return ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: controller.filteredSectors.length,
//             itemBuilder: (context, index) {
//               final sector = controller.filteredSectors[index];
//               // Count cars in this sector
//               final carCount = controller.cars.where((car) =>
//                 car.sectorId == sector.id).length;
//
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: SectorCard(
//                   sector: sector,
//                   carCount: carCount,
//                   isRTL: isRTL,
//                   onTap: () => Get.toNamed('/sector/${sector.id}'),
//                   onEdit: () => Get.toNamed('/sector/${sector.id}/edit'),
//                   onDelete: () => _showDeleteConfirmation(context, sector),
//                   onViewCars: () {
//                     controller.setCarSectorFilter(sector.id);
//                     controller.changeView('cars');
//                   },
//                 ),
//               );
//             },
//           );
//         }),
//       ],
//     );
//   }
//
//   // Show delete confirmation dialog
//   void _showDeleteConfirmation(BuildContext context, dynamic sector) {
//     Get.defaultDialog(
//       title: 'confirm_delete'.tr,
//       titleStyle: GoogleFonts.cairo(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//       middleText: 'confirm_delete_sector'.tr,
//       middleTextStyle: GoogleFonts.cairo(
//         fontSize: 14,
//         color: Colors.black54,
//       ),
//       textConfirm: 'delete'.tr,
//       textCancel: 'cancel'.tr,
//       confirmTextColor: Colors.white,
//       cancelTextColor: AppColors.primary,
//       buttonColor: AppColors.error,
//       onConfirm: () {
//         // Call delete sector service
//         Get.back();
//         Get.snackbar(
//           'success'.tr,
//           'sector_deleted'.tr,
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: AppColors.success.withOpacity(0.1),
//           colorText: AppColors.success,
//         );
//       },
//     );
//   }
//
//   // Build loading state
//   Widget _buildLoadingList() {
//     return ListView.builder(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 3,
//       itemBuilder: (context, index) {
//         return Padding(
//           padding: const EdgeInsets.only(bottom: 8),
//           child: Container(
//             height: 120,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.05),
//                   blurRadius: 10,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Shimmer.fromColors(
//               baseColor: Colors.grey[300]!,
//               highlightColor: Colors.grey[100]!,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     children: [
//                       Container(
//                         width: 40,
//                         height: 40,
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                       ),
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Container(
//                               width: 120,
//                               height: 20,
//                               color: Colors.white,
//                             ),
//                             const SizedBox(height: 8),
//                             Container(
//                               width: 200,
//                               height: 16,
//                               color: Colors.white,
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build empty state
//   Widget _buildEmptyState() {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.location_off_outlined,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'no_sectors_found'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'no_sectors_found_description'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 14,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
