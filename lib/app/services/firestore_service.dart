import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import '../data/models/user_model.dart';
import 'notification_service_selector.dart';

class FirestoreService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final NotificationService _notificationService =
      Get.find<NotificationService>();

  // Generic CRUD operations
  Future<DocumentReference> create(
      String collection, Map<String, dynamic> data) async {
    try {
      return await _firestore.collection(collection).add(data);
    } catch (e) {
      throw 'Failed to create document: $e';
    }
  }
  Future<void> createNoticiation(String role, List<String> userIds,Car car,String status) async {
    try {
      await _firestore.collection('notifications').add({
        'title': 'New Notification',
        'content': 'You have a new notification',
        'type': 'Notification',
        'createdAt': FieldValue.serverTimestamp(),
        'userIds': userIds,
        'role': role,
        'readBy': {},
      });
    } catch (e) {
      print('Error creating notification: $e');
      rethrow;
    }
  }
  Future<void> createRotationNoticiation(String role, List<String> userIds,String status) async {
    try {
      await _firestore.collection('notifications').add({
        'title': 'New Notification',
        'content': 'You have a new notification',
        'type': 'Notification',
        'createdAt': FieldValue.serverTimestamp(),
        'userIds': userIds,
        'role': role,
        'readBy': {},
      });
    } catch (e) {
      print('Error creating notification: $e');
      rethrow;
    }
  }

  Future<void> createWithId(
      String collection, String id, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(id).set(data);
    } catch (e) {
      throw 'Failed to create document with ID: $e';
    }
  }

  Future<DocumentSnapshot> read(String collection, String id) async {
    try {
      return await _firestore.collection(collection).doc(id).get();
    } catch (e) {
      throw 'Failed to read document: $e';
    }
  }

  Future<List<QueryDocumentSnapshot>> readAll(String collection) async {
    try {
      QuerySnapshot snapshot = await _firestore.collection(collection).get();
      return snapshot.docs;
    } catch (e) {
      throw 'Failed to read documents: $e';
    }
  }

  Future<List<QueryDocumentSnapshot>> query(
    String collection, {
    required String field,
    required dynamic isEqualTo,
  }) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection(collection)
          .where(field, isEqualTo: isEqualTo)
          .get();
      return snapshot.docs;
    } catch (e) {
      throw 'Failed to query documents: $e';
    }
  }

  Future<void> update(
    String collection,
    String id,
    Map<String, dynamic> data,
  ) async {
    try {
      await _firestore.collection(collection).doc(id).update(data);
    } catch (e) {
      throw 'Failed to update document: $e';
    }
  }

  Future<void> delete(String collection, String id) async {
    try {
      await _firestore.collection(collection).doc(id).delete();
    } catch (e) {
      throw 'Failed to delete document: $e';
    }
  }

  // Batch operations
  Future<void> batchCreate(
    String collection,
    List<Map<String, dynamic>> dataList,
  ) async {
    try {
      WriteBatch batch = _firestore.batch();
      for (var data in dataList) {
        DocumentReference doc = _firestore.collection(collection).doc();
        batch.set(doc, data);
      }
      await batch.commit();
    } catch (e) {
      throw 'Failed to batch create documents: $e';
    }
  }

  // Real-time listeners
  Stream<QuerySnapshot> streamCollection(String collection) {
    return _firestore.collection(collection).snapshots();
  }

  Stream<DocumentSnapshot> streamDocument(String collection, String id) {
    return _firestore.collection(collection).doc(id).snapshots();
  }

  // Advanced queries
  Future<List<QueryDocumentSnapshot>> advancedQuery(
    String collection, {
    List<String>? orderBy,
    bool descending = false,
    int? limit,
    Map<String, dynamic>? whereConditions,
  }) async {
    try {
      Query query = _firestore.collection(collection);

      if (whereConditions != null) {
        whereConditions.forEach((field, value) {
          if (value is Map) {
            if (value.containsKey('arrayContains')) {
              query = query.where(field, arrayContains: value['arrayContains']);
            } else if (value.containsKey('arrayContainsAny')) {
              query = query.where(field,
                  arrayContainsAny: value['arrayContainsAny']);
            } else if (value.containsKey('isEqualTo')) {
              query = query.where(field, isEqualTo: value['isEqualTo']);
            } else if (value.containsKey('isGreaterThan')) {
              query = query.where(field, isGreaterThan: value['isGreaterThan']);
            } else if (value.containsKey('isLessThan')) {
              query = query.where(field, isLessThan: value['isLessThan']);
            }
          } else {
            query = query.where(field, isEqualTo: value);
          }
        });
      }

      if (orderBy != null) {
        for (var field in orderBy) {
          query = query.orderBy(field, descending: descending);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      QuerySnapshot snapshot = await query.get();
      return snapshot.docs;
    } catch (e) {
      throw 'Failed to execute advanced query: $e';
    }
  }

  Future<void> sendNotificationToUser(
      String userId, String title, String body) async {
    try {
      // Get FCM token for the user
      final userSnapshot =
          await _firestore.collection('users').doc(userId).get();
      final fcmToken = userSnapshot.data()!['fcmToken'] as String?;

      if (fcmToken != null) {
        // Send notification using the notification service
        await _notificationService.sendNotificationToTokens(
          tokens: [fcmToken],
          title: title,
          body: body,
        );
      } else {
        print('No FCM token found for the user');
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> sendNotificationToUsersByRole({
    required List<UserRole> roles,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Get all users with the specified roles
      final usersSnapshot = await _firestore
          .collection('users')
          .where('role',
              whereIn:
                  roles.map((role) => role.toString().split('.').last).toList())
          .get();

      // Get FCM tokens for all users and filter out null values
      final tokens = usersSnapshot.docs
          .map((doc) => doc.data()['fcmToken'] as String?)
          .where((token) => token != null)
          .map((token) => token!) // Convert String? to String
          .toList();

      if (tokens.isEmpty) {
        print('No FCM tokens found for the specified roles');
        return;
      }

      print('*************');
      print(tokens.length);
      print('*************');
      // Send notification using the notification service
      await _notificationService.sendNotificationToTokens(
        tokens: tokens,
        title: title,
        body: body,
        data: data,
      );
    } catch (e) {
      print('Error sending notification: $e');
      rethrow;
    }
  }

  Future<void> updateUserFcmToken(String userId, String token) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });
      print('FCM token updated successfully for user: $userId');
    } catch (e) {
      print('Error updating FCM token: $e');
      rethrow;
    }
  }

  Future<UserModel?> login(String username, String password) async {
    try {
      // Query Firestore for user with matching username/email and password
      final userQuery = await _firestore
          .collection('users')
          .where(Filter.or(
            Filter('username', isEqualTo: username),
            Filter('email', isEqualTo: username),
          ))
          .where('password', isEqualTo: password)
          .where('status', isEqualTo: 'active')
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        return null;
      }

      final userData = userQuery.docs.first;
      return UserModel.fromJson({...userData.data(), 'id': userData.id});
    } catch (e) {
      print('Error during login: $e');
      return null;
    }
  }

  Future<UserModel?> getUser(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        return UserModel.fromJson({...userDoc.data()!, 'id': userDoc.id});
      }
      return null;
    } catch (e) {
      print('Error getting user: $e');
      return null;
    }
  }
}
