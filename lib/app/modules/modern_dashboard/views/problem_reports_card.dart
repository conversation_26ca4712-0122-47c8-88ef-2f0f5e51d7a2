import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../routes/app_pages.dart';

class ProblemReportsCard extends StatelessWidget {
  final Color backgroundColor;
  final Color primaryText;
  final Color secondaryText;

  const ProblemReportsCard({
    Key? key,
    required this.backgroundColor,
    required this.primaryText,
    required this.secondaryText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تقارير المشاكل',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: primaryText,
                ),
              ),
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.report_problem,
                  color: Colors.purple,
                  size: 24.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'تحليل المشاكل الشائعة للمركبات',
            style: TextStyle(
              fontSize: 14.sp,
              color: secondaryText,
            ),
          ),
          SizedBox(height: 24.h),
          
          // Problem categories
          _buildProblemCategory('مشاكل المحرك', 35, Colors.red),
          SizedBox(height: 12.h),
          _buildProblemCategory('مشاكل الكهرباء', 28, Colors.orange),
          SizedBox(height: 12.h),
          _buildProblemCategory('مشاكل التكييف', 22, Colors.blue),
          SizedBox(height: 12.h),
          _buildProblemCategory('مشاكل الفرامل', 15, Colors.green),
          
          SizedBox(height: 24.h),
          
          // View all button
          Center(
            child: ElevatedButton.icon(
              onPressed: () => Get.toNamed(Routes.PROBLEM_REPORTS),
              icon: const Icon(Icons.analytics),
              label: const Text('عرض تقارير المشاكل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProblemCategory(String name, int count, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              name,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: primaryText,
              ),
            ),
            Text(
              '$count مركبة',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        ClipRRect(
          borderRadius: BorderRadius.circular(4.r),
          child: LinearProgressIndicator(
            value: count / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8.h,
          ),
        ),
      ],
    );
  }
}
