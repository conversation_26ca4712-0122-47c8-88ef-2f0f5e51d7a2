import 'package:cloud_firestore/cloud_firestore.dart';

import '../services/firestore_service.dart';

class SectorProvider {
  final FirestoreService _firestoreService;

  SectorProvider({FirestoreService? firestoreService})
      : _firestoreService = firestoreService ?? FirestoreService();

  /// Create a new sector document in the 'sectors' collection
  Future<void> createSector(Map<String, dynamic> data) async {
    await _firestoreService.create('sectors', data);
  }

  /// Read all sector documents from the 'sectors' collection
  Future<List<Map<String, dynamic>>> readSectors() async {
    QuerySnapshot querySnapshot = (await _firestoreService.readAll('sectors')) as QuerySnapshot<Object?>;
    return querySnapshot.docs.map((doc) => doc.data() as Map<String, dynamic>).toList();
  }

  /// Update a sector document identified by [documentId] in the 'sectors' collection
  Future<void> updateSector(String documentId, Map<String, dynamic> data) async {
    await _firestoreService.update('sectors', documentId, data);
  }

  /// Delete a sector document identified by [documentId] from the 'sectors' collection
  Future<void> deleteSector(String documentId) async {
    await _firestoreService.delete('sectors', documentId);
  }
}
