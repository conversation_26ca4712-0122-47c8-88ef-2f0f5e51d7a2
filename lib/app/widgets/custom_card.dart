import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../themes/app_colors.dart';

enum CardVariant {
  elevated,
  outlined,
  filled,
  gradient,
}

class CustomCard extends StatefulWidget {
  final Widget child;
  final CardVariant variant;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final Color? borderColor;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final bool isClickable;
  final bool showShadow;
  final double? width;
  final double? height;
  final Alignment? gradientBegin;
  final Alignment? gradientEnd;

  const CustomCard({
    Key? key,
    required this.child,
    this.variant = CardVariant.elevated,
    this.backgroundColor,
    this.gradientColors,
    this.borderColor,
    this.elevation,
    this.padding,
    this.margin,
    this.borderRadius,
    this.onTap,
    this.isClickable = false,
    this.showShadow = true,
    this.width,
    this.height,
    this.gradientBegin,
    this.gradientEnd,
  }) : super(key: key);

  @override
  State<CustomCard> createState() => _CustomCardState();
}

class _CustomCardState extends State<CustomCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: widget.elevation ?? _getDefaultElevation(),
      end: (widget.elevation ?? _getDefaultElevation()) + 2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getDefaultElevation() {
    switch (widget.variant) {
      case CardVariant.elevated:
        return 4;
      case CardVariant.outlined:
        return 0;
      case CardVariant.filled:
        return 1;
      case CardVariant.gradient:
        return 6;
    }
  }

  Color _getBackgroundColor() {
    final isDark = Get.isDarkMode;
    
    if (widget.backgroundColor != null) {
      return widget.backgroundColor!;
    }

    switch (widget.variant) {
      case CardVariant.elevated:
      case CardVariant.filled:
        return AppColors.getCard(isDark);
      case CardVariant.outlined:
        return Colors.transparent;
      case CardVariant.gradient:
        return Colors.transparent;
    }
  }

  Border? _getBorder() {
    if (widget.variant == CardVariant.outlined) {
      final isDark = Get.isDarkMode;
      return Border.all(
        color: widget.borderColor ?? AppColors.getBorder(isDark),
        width: 1.5,
      );
    }
    return null;
  }

  List<BoxShadow>? _getBoxShadow() {
    if (!widget.showShadow || widget.variant == CardVariant.outlined) {
      return null;
    }

    final isDark = Get.isDarkMode;
    final shadowColor = AppColors.getShadow(isDark);
    final elevation = widget.elevation ?? _getDefaultElevation();

    return [
      BoxShadow(
        color: shadowColor,
        blurRadius: elevation * 2,
        offset: Offset(0, elevation / 2),
        spreadRadius: 0,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(16.r);
    final isClickable = widget.isClickable || widget.onTap != null;

    Widget card = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: isClickable ? _scaleAnimation.value : 1.0,
          child: child,
        );
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        margin: widget.margin ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: widget.variant != CardVariant.gradient ? _getBackgroundColor() : null,
          gradient: widget.variant == CardVariant.gradient ? _buildGradient() : null,
          borderRadius: borderRadius,
          border: _getBorder(),
          boxShadow: _getBoxShadow(),
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: borderRadius,
          child: InkWell(
            onTap: widget.onTap,
            onTapDown: isClickable ? (_) => _animationController.forward() : null,
            onTapUp: isClickable ? (_) => _animationController.reverse() : null,
            onTapCancel: isClickable ? () => _animationController.reverse() : null,
            borderRadius: borderRadius,
            child: Container(
              padding: widget.padding ?? EdgeInsets.all(16.w),
              child: widget.child,
            ),
          ),
        ),
      ),
    );

    return card;
  }

  LinearGradient? _buildGradient() {
    if (widget.variant != CardVariant.gradient) return null;

    final colors = widget.gradientColors ?? AppColors.cardGradient1;
    
    return LinearGradient(
      colors: colors,
      begin: widget.gradientBegin ?? Alignment.topLeft,
      end: widget.gradientEnd ?? Alignment.bottomRight,
    );
  }
}

// Predefined card variants for common use cases
class StatusCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final List<Color> gradientColors;
  final VoidCallback? onTap;

  const StatusCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.gradientColors,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      variant: CardVariant.gradient,
      gradientColors: gradientColors,
      onTap: onTap,
      isClickable: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 24.r,
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white70,
                  size: 16.r,
                ),
            ],
          ),
          SizedBox(height: 16.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}

class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;

  const InfoCard({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      variant: CardVariant.elevated,
      onTap: onTap,
      isClickable: onTap != null,
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            SizedBox(width: 16.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    subtitle!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.getTextSecondary(Get.isDarkMode),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            SizedBox(width: 16.w),
            trailing!,
          ],
        ],
      ),
    );
  }
}
