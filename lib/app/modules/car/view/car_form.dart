import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/car_model.dart';
import '../controller/car_controller.dart';
import '../../sector/controller/sector_controller.dart';

class CarForm extends GetView<CarController> {
  final Car? car;
  final _formKey = GlobalKey<FormState>();
  final sectorController = Get.find<SectorController>();

  CarForm({Key? key, this.car}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final sectorId = car?.sectorId.obs ?? ''.obs;
    final plateNumber = TextEditingController(text: car?.plateNumber);
    final plateCharacters = TextEditingController(text: car?.plateCharacters);
    final encoding = TextEditingController(text: car?.encoding);
    final carModel = TextEditingController(text: car?.carModel);
    final carType = TextEditingController(text: car?.carType);
    final isInternal = (car?.isInternal ?? false).obs;
    final status = (car?.status ?? CarStatus.active).obs;

    return Scaffold(
      appBar: AppBar(
        title: Text(car == null ? 'Add Car' : 'Edit Car'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Obx(() => DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Sector',
                      border: OutlineInputBorder(),
                    ),
                    value: sectorId.value.isEmpty ? null : sectorId.value,
                    items: sectorController.sectors
                        .map((sector) => DropdownMenuItem<String>(
                              value: sector.id,
                              child: Text(sector.name),
                            ))
                        .toList(),
                    onChanged: (value) => sectorId.value = value ?? '',
                    validator: (value) =>
                        value == null || value.isEmpty ? 'Please select a sector' : null,
                  )),
              const SizedBox(height: 16),
              TextFormField(
                controller: plateNumber,
                decoration: const InputDecoration(
                  labelText: 'Plate Number',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value == null || value.isEmpty ? 'Please enter plate number' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: plateCharacters,
                decoration: const InputDecoration(
                  labelText: 'Plate Characters',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value == null || value.isEmpty ? 'Please enter plate characters' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: encoding,
                decoration: const InputDecoration(
                  labelText: 'Encoding',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value == null || value.isEmpty ? 'Please enter encoding' : null,
              ),
              const SizedBox(height: 16),
              // TextFormField(
              //   controller: carModel,
              //   decoration: const InputDecoration(
              //     labelText: 'Car Model',
              //     border: OutlineInputBorder(),
              //   ),
              //   validator: (value) =>
              //       value == null || value.isEmpty ? 'Please enter car model' : null,
              // ),
              // const SizedBox(height: 16),
              TextFormField(
                controller: carType,
                decoration: const InputDecoration(
                  labelText: 'Car Type',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                    value == null || value.isEmpty ? 'Please enter car type' : null,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Obx(() => CheckboxListTile(
                          title: const Text('Internal'),
                          value: isInternal.value,
                          onChanged: (value) => isInternal.value = value ?? false,
                          contentPadding: EdgeInsets.zero,
                        )),
                  ),
                ],
              ),
              if (car != null) ...[
                const SizedBox(height: 16),
                Obx(() => DropdownButtonFormField<CarStatus>(
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        border: OutlineInputBorder(),
                      ),
                      value: status.value,
                      items: CarStatus.values
                          .map((s) => DropdownMenuItem<CarStatus>(
                                value: s,
                                child: Text(s.toString().split('.').last),
                              ))
                          .toList(),
                      onChanged: (value) => status.value = value ?? CarStatus.active,
                    )),
              ],
              const SizedBox(height: 24),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(16),
                ),
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    if (car == null) {
                      // controller.createCar(
                      //   sectorId.value,
                      //   plateNumber.text,
                      //   plateCharacters.text,
                      //   encoding.text,
                      //   carModel.text,
                      //   carType.text,
                      //   isInternal.value,
                      // );
                    } else {
                      controller.updateCar(
                        car!.copyWith(
                          sectorId: sectorId.value,
                          sectorName: controller.sectors.firstWhere((element) => element.id == sectorId.value).name,
                          plateNumber: plateNumber.text,
                          plateCharacters: plateCharacters.text,
                          encoding: encoding.text,
                          carModel: carModel.text,
                          carType: carType.text,
                          isInternal: isInternal.value,
                        )
                      );
                    }
                    Get.back();
                  }
                },
                child: Text(car == null ? 'Add Car' : 'Update Car'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
