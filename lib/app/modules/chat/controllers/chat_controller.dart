import 'dart:async';
import 'package:get/get.dart';
import '../models/chat_message.dart';
import '../models/chat_room.dart';
import '../services/chat_service.dart';

class ChatController extends GetxController {
  final ChatService _chatService = Get.find<ChatService>();
  
  final messages = <ChatMessage>[].obs;
  final chatRoom = Rxn<ChatRoom>();
  final isLoading = true.obs;
  final isTyping = false.obs;
  
  late String currentUserId;
  late String currentRoomId;
  late String otherUserId;
  late StreamSubscription _messagesSubscription;
  late StreamSubscription _chatRoomSubscription;
  Timer? _typingTimer;

  @override
  void onInit() {
    super.onInit();
    currentUserId = Get.arguments['currentUserId'];
    otherUserId = Get.arguments['otherUserId'];
    currentRoomId = Get.arguments['currentRoomId'];

    // Initialize push notifications for the current user
    _chatService.initPushNotifications(currentUserId);
    
    _initializeChat();
  }

  @override
  void onClose() {
    _messagesSubscription.cancel();
    _chatRoomSubscription.cancel();
    _typingTimer?.cancel();
    _updateOnlineStatus(false);
    super.onClose();
  }

  void _initializeChat() async {
    isLoading.value = true;
    
    // Get or create chat room

    // Listen to chat room updates
    _chatRoomSubscription = _chatService
        .getChatRooms(currentUserId)
        .listen((rooms) {
      final room = rooms.firstWhereOrNull((room) => room.id == currentRoomId);
      chatRoom.value = room;
    });

    print(currentRoomId);
    // Listen to messages
    _messagesSubscription = _chatService
        .getMessages(currentRoomId)
        .listen((msgs) {
      messages.value = msgs;
      _markMessagesAsRead();
    });

    // Update online status
    _updateOnlineStatus(true);
    
    isLoading.value = false;
  }

  //chat between sector manger and superrvistor
  Stream<List<ChatMessage>> getMessages(){
    return _chatService.getMessages(_getChatRoomId(currentUserId, otherUserId));
  }
  String _getChatRoomId(String uid1, String uid2) {
    final sortedIds = [uid1, uid2]..sort();
    return currentRoomId;
  }

  void sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final message = ChatMessage(
      id: '',  // Will be set by Firebase
      senderId: currentUserId,
      receiverId: otherUserId,
      content: content,
      timestamp: DateTime.now(),
    );

    try {
      await _chatService.sendMessage(
        message,
        _getChatRoomId(currentUserId, otherUserId),
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to send message',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void _markMessagesAsRead() async {
    if (messages.isEmpty) return;
    
    await _chatService.markMessagesAsRead(
      _getChatRoomId(currentUserId, otherUserId),
      currentUserId,
    );
  }

  void updateTypingStatus(bool isTyping) {
    _typingTimer?.cancel();
    
    if (isTyping) {
      _chatService.updateTypingStatus(
        _getChatRoomId(currentUserId, otherUserId),
        currentUserId,
        true,
      );

      _typingTimer = Timer(const Duration(seconds: 3), () {
        _chatService.updateTypingStatus(
          _getChatRoomId(currentUserId, otherUserId),
          currentUserId,
          false,
        );
      });
    } else {
      _chatService.updateTypingStatus(
        _getChatRoomId(currentUserId, otherUserId),
        currentUserId,
        false,
      );
    }
  }

  void _updateOnlineStatus(bool isOnline) {
    _chatService.updateOnlineStatus(
      _getChatRoomId(currentUserId, otherUserId),
      currentUserId,
      isOnline,
    );
  }
}
