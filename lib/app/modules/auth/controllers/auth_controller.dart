import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/gestures.dart';
import '../../../routes/app_pages.dart';
import '../../../themes/app_colors.dart';

class AuthController extends GetxController {
  // Form keys
  final signupFormKey = GlobalKey<FormState>();
  final loginFormKey = GlobalKey<FormState>();
  
  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Text controllers for signup
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final dateOfBirthController = TextEditingController();
  final licenseNumberController = TextEditingController();
  final licenseExpiryController = TextEditingController();
  final vehicleModelController = TextEditingController();
  final vehicleYearController = TextEditingController();
  final licensePlateController = TextEditingController();
  final vehicleColorController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  
  // Text controllers for login
  final loginEmailController = TextEditingController();
  final loginPasswordController = TextEditingController();
  
  // Observable variables
  final isLoading = false.obs;
  final isPasswordVisible = false.obs;
  final isConfirmPasswordVisible = false.obs;
  final selectedGender = 'male'.obs;
  final termsAccepted = false.obs;
  final selectedColor = Color(0xFF2196F3).obs; // Default blue color
  
  // Gesture recognizers for terms and privacy policy
  late TapGestureRecognizer termsRecognizer;
  late TapGestureRecognizer privacyRecognizer;
  
  @override
  void onInit() {
    super.onInit();
    
    // Initialize gesture recognizers
    termsRecognizer = TapGestureRecognizer()
      ..onTap = () {
        // Show terms and conditions
        _showTermsAndConditions();
      };
    
    privacyRecognizer = TapGestureRecognizer()
      ..onTap = () {
        // Show privacy policy
        _showPrivacyPolicy();
      };
  }
  
  @override
  void onClose() {
    // Dispose controllers
    firstNameController.dispose();
    lastNameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    dateOfBirthController.dispose();
    licenseNumberController.dispose();
    licenseExpiryController.dispose();
    vehicleModelController.dispose();
    vehicleYearController.dispose();
    licensePlateController.dispose();
    vehicleColorController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    loginEmailController.dispose();
    loginPasswordController.dispose();
    
    // Dispose recognizers
    termsRecognizer.dispose();
    privacyRecognizer.dispose();
    
    super.onClose();
  }
  
  // Sign up method
  Future<void> signUp() async {
    if (signupFormKey.currentState!.validate()) {
      if (!termsAccepted.value) {
        Get.snackbar(
          'error'.tr,
          'accept_terms_required'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
        );
        return;
      }
      
      try {
        isLoading.value = true;
        
        // Create user with email and password
        UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
          email: emailController.text.trim(),
          password: passwordController.text.trim(),
        );
        
        // Prepare user data
        Map<String, dynamic> userData = {
          'firstName': firstNameController.text.trim(),
          'lastName': lastNameController.text.trim(),
          'email': emailController.text.trim(),
          'phone': phoneController.text.trim(),
          'dateOfBirth': dateOfBirthController.text.trim(),
          'gender': selectedGender.value,
          'role': 'driver',
          'createdAt': FieldValue.serverTimestamp(),
          'isActive': true,
        };
        
        // Prepare driver data
        Map<String, dynamic> driverData = {
          'userId': userCredential.user!.uid,
          'licenseNumber': licenseNumberController.text.trim(),
          'licenseExpiry': licenseExpiryController.text.trim(),
          'vehicleModel': vehicleModelController.text.trim(),
          'vehicleYear': vehicleYearController.text.trim(),
          'licensePlate': licensePlateController.text.trim(),
          'vehicleColor': vehicleColorController.text.trim(),
          'vehicleColorHex': '#${selectedColor.value.toRadixString(16).substring(2).toUpperCase()}',
          'isAvailable': true,
          'rating': 0.0,
          'totalTrips': 0,
          'createdAt': FieldValue.serverTimestamp(),
        };
        
        // Save user data to Firestore
        await _firestore.collection('users').doc(userCredential.user!.uid).set(userData);
        
        // Save driver data to Firestore
        await _firestore.collection('drivers').doc(userCredential.user!.uid).set(driverData);
        
        // Clear form fields
        _clearSignupForm();
        
        // Show success message
        Get.snackbar(
          'success'.tr,
          'signup_success'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withOpacity(0.7),
          colorText: Colors.white,
        );
        
        // Navigate to login screen
        Get.offNamed(Routes.LOGIN);
      } on FirebaseAuthException catch (e) {
        String errorMessage = 'signup_failed'.tr;
        
        if (e.code == 'weak-password') {
          errorMessage = 'weak_password'.tr;
        } else if (e.code == 'email-already-in-use') {
          errorMessage = 'email_already_in_use'.tr;
        } else if (e.code == 'invalid-email') {
          errorMessage = 'invalid_email'.tr;
        }
        
        Get.snackbar(
          'error'.tr,
          errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'error'.tr,
          'signup_failed'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }
  
  // Login method
  Future<void> login() async {
    if (loginFormKey.currentState!.validate()) {
      try {
        isLoading.value = true;
        
        // Sign in with email and password
        await _auth.signInWithEmailAndPassword(
          email: loginEmailController.text.trim(),
          password: loginPasswordController.text.trim(),
        );
        
        // Get user data
        DocumentSnapshot userDoc = await _firestore
            .collection('users')
            .doc(_auth.currentUser!.uid)
            .get();
        
        if (!userDoc.exists) {
          throw Exception('User data not found');
        }
        
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;
        
        // Check if user is a driver
        if (userData['role'] == 'driver') {
          // Get driver data
          DocumentSnapshot driverDoc = await _firestore
              .collection('drivers')
              .doc(_auth.currentUser!.uid)
              .get();
          
          if (!driverDoc.exists) {
            throw Exception('Driver data not found');
          }
          
          // Navigate to driver home
          Get.offAllNamed(Routes.DRIVER_HOME);
        } else {
          // Navigate to customer home
          Get.offAllNamed(Routes.HOME);
        }
        
        // Clear login form
        loginEmailController.clear();
        loginPasswordController.clear();
      } on FirebaseAuthException catch (e) {
        String errorMessage = 'login_failed'.tr;
        
        if (e.code == 'user-not-found') {
          errorMessage = 'user_not_found'.tr;
        } else if (e.code == 'wrong-password') {
          errorMessage = 'wrong_password'.tr;
        } else if (e.code == 'invalid-email') {
          errorMessage = 'invalid_email'.tr;
        } else if (e.code == 'user-disabled') {
          errorMessage = 'user_disabled'.tr;
        }
        
        Get.snackbar(
          'error'.tr,
          errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'error'.tr,
          'login_failed'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
        );
      } finally {
        isLoading.value = false;
      }
    }
  }
  
  // Forgot password method
  Future<void> forgotPassword(String email) async {
    if (email.isEmpty) {
      Get.snackbar(
        'error'.tr,
        'email_required'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.7),
        colorText: Colors.white,
      );
      return;
    }
    
    if (!GetUtils.isEmail(email)) {
      Get.snackbar(
        'error'.tr,
        'invalid_email'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.7),
        colorText: Colors.white,
      );
      return;
    }
    
    try {
      isLoading.value = true;
      
      await _auth.sendPasswordResetEmail(email: email);
      
      Get.snackbar(
        'success'.tr,
        'password_reset_email_sent'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success.withOpacity(0.7),
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'password_reset_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  // Sign out method
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'signout_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }
  
  // Clear signup form
  void _clearSignupForm() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    phoneController.clear();
    dateOfBirthController.clear();
    licenseNumberController.clear();
    licenseExpiryController.clear();
    vehicleModelController.clear();
    vehicleYearController.clear();
    licensePlateController.clear();
    vehicleColorController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    selectedGender.value = 'male';
    termsAccepted.value = false;
    selectedColor.value = Color(0xFF2196F3);
  }
  
  // Show terms and conditions
  void _showTermsAndConditions() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'terms_and_conditions'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    'terms_and_conditions_content'.tr,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('close'.tr),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Show privacy policy
  void _showPrivacyPolicy() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'privacy_policy'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    'privacy_policy_content'.tr,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('close'.tr),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
