import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../routes/app_pages.dart';

class HomeController extends GetxController {
  final RxString currentPage = 'dashboard'.obs;
  final isDrawerOpen = false.obs;
  final _auth = Get.find<AuthController>();

  void setPage(String page) {
    // Validate access based on user role


    isDrawerOpen.value = false;
    currentPage.value = page;
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }

  void toggleDrawer() {
    isDrawerOpen.value = !isDrawerOpen.value;
  }

  @override
  void onInit() {
    super.onInit();
    _checkAuth();
    // Check if there's a page parameter in the URL
    final pageParam = Get.parameters['page'];
    if (pageParam != null) {
      setPage(pageParam);
    }
  }

  void _checkAuth() {
    // Use postFrameCallback to ensure navigation happens after the build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // if (!_authController.isLoggedIn) {
      //   Get.offAllNamed(Routes.LOGIN);
      // }
    });
  }

  void logout() {
    // _authController.logout();
  }

  // Use AuthController properties as per memory requirements
  String get userName => 'Guest';
  String get userRole =>  'User';
  String get uid => '';
}
