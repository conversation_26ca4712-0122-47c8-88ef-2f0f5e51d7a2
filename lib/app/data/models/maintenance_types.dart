import 'package:flutter/material.dart';

/// Model for maintenance type
class MaintenanceType {
  final int id;
  final String name;
  final String? description;
  final IconData? icon;

  const MaintenanceType({
    required this.id,
    required this.name,
    this.description,
    this.icon,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
    };
  }

  factory MaintenanceType.fromJson(Map<String, dynamic> json) {
    return MaintenanceType(
      id: json['id'],
      name: json['name'],
      description: json['description'],
    );
  }
}

/// List of standard maintenance types based on the checklist
final List<MaintenanceType> standardMaintenanceTypes = [

  const MaintenanceType(
    id: 1,
    name: 'زيت الماكينة',
    icon: Icons.oil_barrel,
  ),
  const MaintenanceType(
    id: 2,
    name: 'فلتر الزيت',
    icon: Icons.filter_alt,
  ),
  const MaintenanceType(
    id: 3,
    name: 'زيت ناقل الحركة',
    icon: Icons.oil_barrel,
  ),
  const MaintenanceType(
    id: 4,
    name: 'زيت الفرامل',
    icon: Icons.oil_barrel,
  ),
  const MaintenanceType(
    id: 5,
    name: 'زيت الرؤوس (الديفرنس)',
    icon: Icons.oil_barrel,
  ),
  const MaintenanceType(
    id: 6,
    name: 'ماء الرديتر',
    icon: Icons.water_drop,
  ),
  const MaintenanceType(
    id: 7,
    name: 'قربة ماء الرديتر',
    icon: Icons.water,
  ),
  const MaintenanceType(
    id: 8,
    name: 'خزان الرديتر',
    icon: Icons.water_damage,
  ),
  const MaintenanceType(
    id: 9,
    name: 'ملف حرارة المحرك',
    icon: Icons.thermostat,
  ),
  const MaintenanceType(
    id: 10,
    name: 'حالة البطارية',
    icon: Icons.battery_full,
  ),
  const MaintenanceType(
    id: 11,
    name: 'جهد مولد الشحن (الدينمو)',
    icon: Icons.electrical_services,
  ),
  const MaintenanceType(
    id: 12,
    name: 'فحص الدوار الامامي والخلفي',
    icon: Icons.rotate_right,
  ),
  const MaintenanceType(
    id: 13,
    name: 'فحص الإطارات وضبط الهواء',
    icon: Icons.tire_repair,
  ),
  const MaintenanceType(
    id: 14,
    name: 'الإطار الاحتياطي',
    icon: Icons.tire_repair,
  ),
  const MaintenanceType(
    id: 15,
    name: 'السيور وبكرات المحرك',
    icon: Icons.settings,
  ),
  const MaintenanceType(
    id: 16,
    name: 'الإضاءات التحذيرية والكشافات الجانبية',
    icon: Icons.lightbulb,
  ),
  const MaintenanceType(
    id: 17,
    name: 'الكسحات التحذيرية ومخاطبة الجمهور',
    icon: Icons.campaign,
  ),
  const MaintenanceType(
    id: 18,
    name: 'الجوازات',
    icon: Icons.document_scanner,
  ),
  const MaintenanceType(
    id: 19,
    name: 'بواجي المحرك',
    icon: Icons.electric_bolt,
  ),
  const MaintenanceType(
    id: 20,
    name: 'مساحات الزجاج',
    icon: Icons.cleaning_services,
  ),
  const MaintenanceType(
    id: 21,
    name: 'قربة سائل المساحات',
    icon: Icons.water,
  ),
  const MaintenanceType(
    id: 22,
    name: 'جلد المقصات',
    icon: Icons.chair,
  ),
  const MaintenanceType(
    id: 23,
    name: 'المقصات',
    icon: Icons.settings,
  ),
  const MaintenanceType(
    id: 24,
    name: 'الاذرعة',
    icon: Icons.settings,
  ),
  const MaintenanceType(
    id: 25,
    name: 'الفرامل',
    icon: Icons.accessibility_new,
  ),
  const MaintenanceType(
    id: 26,
    name: 'فحصات (الأقمشة)',
    icon: Icons.check_box_outlined,
  ),
  const MaintenanceType(
    id: 27,
    name: 'الهوبات',
    icon: Icons.settings,
  ),
  const MaintenanceType(
    id: 28,
    name: 'فحصات البريك (الجلنط)',
    icon: Icons.accessibility_new,
  ),
  const MaintenanceType(
    id: 29,
    name: 'تشغيل مزراج التبريد مع البريك',
    icon: Icons.ac_unit,
  ),
  const MaintenanceType(
    id: 30,
    name: 'المرايات الجانبية',
    icon: Icons.flip_camera_android,
  ),
  const MaintenanceType(
    id: 31,
    name: 'الزجاج الامامي',
    icon: Icons.window,
  ),
  const MaintenanceType(
    id: 32,
    name: 'جسم المركبة (البودي)',
    icon: Icons.directions_car,
  ),
  const MaintenanceType(
    id: 33,
    name: 'شريحة الوقود وملحقات المركبة',
    icon: Icons.local_gas_station,
  ),
];

/// Get maintenance type by ID
MaintenanceType? getMaintenanceTypeById(int id) {
  try {
    return standardMaintenanceTypes.firstWhere((type) => type.id == id);
  } catch (e) {
    return null;
  }
}

/// Get maintenance type by name
MaintenanceType? getMaintenanceTypeByName(String name) {
  try {
    return standardMaintenanceTypes.firstWhere((type) => type.name == name);
  } catch (e) {
    return null;
  }
}
