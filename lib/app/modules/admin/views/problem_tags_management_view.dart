import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/problem_tag_model.dart';
import '../../../services/problem_tag_service.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/shared_app_bar.dart';

class ProblemTagsManagementView extends StatefulWidget {
  const ProblemTagsManagementView({Key? key}) : super(key: key);

  @override
  State<ProblemTagsManagementView> createState() =>
      _ProblemTagsManagementViewState();
}

class _ProblemTagsManagementViewState extends State<ProblemTagsManagementView> {
  final ProblemTagService _tagService = Get.find<ProblemTagService>();
  final TextEditingController _searchController = TextEditingController();
  final RxString _searchQuery = ''.obs;
  final RxString _selectedCategory = 'all'.obs;

  @override
  void initState() {
    super.initState();
    _tagService.loadTags();

    _searchController.addListener(() {
      _searchQuery.value = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SharedAppBar(
        title: 'إدارة المشاكل الشائعة',
        showBackButton: true,
      ),
      body: Column(
        children: [
          _buildSearchAndFilterBar(),
          Expanded(
            child: Obx(() {
              if (_tagService.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final filteredTags = _getFilteredTags();

              if (filteredTags.isEmpty) {
                return Center(
                  child: Text(
                    _searchQuery.value.isEmpty
                        ? 'لا توجد مشاكل متاحة'
                        : 'لا توجد نتائج لـ "${_searchQuery.value}"',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: filteredTags.length,
                itemBuilder: (context, index) {
                  final tag = filteredTags[index];
                  return _buildTagCard(tag);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTagDialog,
        child: const Icon(Icons.add),
        tooltip: 'إضافة مشكلة جديدة',
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'بحث عن مشكلة',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            ),
          ),
          const SizedBox(height: 16),
          Obx(() {
            final categories = ['all', ..._tagService.getAllCategories()];
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: categories.map((category) {
                  final isSelected = _selectedCategory.value == category;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category == 'all' ? 'الكل' : category),
                      selected: isSelected,
                      onSelected: (selected) {
                        _selectedCategory.value = selected ? category : 'all';
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTagCard(ProblemTag tag) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(tag.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الفئة: ${tag.category}'),
            Text('عدد الاستخدامات: ${tag.usageCount}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: AppColors.primary),
              onPressed: () => _showEditTagDialog(tag),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(tag),
              tooltip: 'حذف',
            ),
          ],
        ),
      ),
    );
  }

  void _showAddTagDialog() {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController categoryController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مشكلة جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المشكلة',
                hintText: 'أدخل اسم المشكلة',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            Obx(() {
              final categories = _tagService.getAllCategories();
              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  hintText: 'اختر الفئة',
                ),
                value: categories.contains(categoryController.text)
                    ? categoryController.text
                    : (categories.isNotEmpty ? categories.first : null),
                items: [
                  ...categories.map((category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      )),
                  const DropdownMenuItem(
                    value: 'custom',
                    child: Text('فئة جديدة'),
                  ),
                ],
                onChanged: (value) {
                  if (value == 'custom') {
                    _showAddCategoryDialog(categoryController);
                  } else if (value != null) {
                    categoryController.text = value;
                  }
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                final category = categoryController.text.isEmpty
                    ? 'general'
                    : categoryController.text;

                final newTag = ProblemTag(
                  id: '', // Will be set by Firestore
                  name: nameController.text.trim(),
                  category: category,
                  usageCount: 0,
                  createdAt: DateTime.now(),
                );

                // Store context before async gap
                final navigatorContext = context;

                await _tagService.addTag(newTag);

                // Check if widget is still mounted before using context
                if (navigatorContext.mounted) {
                  Navigator.of(navigatorContext).pop();
                }
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    ).then((_) {
      nameController.dispose();
      categoryController.dispose();
    });
  }

  void _showEditTagDialog(ProblemTag tag) {
    final TextEditingController nameController =
        TextEditingController(text: tag.name);
    final TextEditingController categoryController =
        TextEditingController(text: tag.category);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل المشكلة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المشكلة',
                hintText: 'أدخل اسم المشكلة',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            Obx(() {
              final categories = _tagService.getAllCategories();
              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  hintText: 'اختر الفئة',
                ),
                value: categories.contains(categoryController.text)
                    ? categoryController.text
                    : (categories.isNotEmpty ? categories.first : null),
                items: [
                  ...categories.map((category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      )),
                  const DropdownMenuItem(
                    value: 'custom',
                    child: Text('فئة جديدة'),
                  ),
                ],
                onChanged: (value) {
                  if (value == 'custom') {
                    _showAddCategoryDialog(categoryController);
                  } else if (value != null) {
                    categoryController.text = value;
                  }
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                final updatedTag = tag.copyWith(
                  name: nameController.text.trim(),
                  category: categoryController.text.isEmpty
                      ? 'general'
                      : categoryController.text,
                  updatedAt: DateTime.now(),
                );

                // Store context before async gap
                final navigatorContext = context;

                await _tagService.updateTag(updatedTag);

                // Check if widget is still mounted before using context
                if (navigatorContext.mounted) {
                  Navigator.of(navigatorContext).pop();
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    ).then((_) {
      nameController.dispose();
      categoryController.dispose();
    });
  }

  void _showDeleteConfirmation(ProblemTag tag) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشكلة "${tag.name}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context before async gap
              final navigatorContext = context;

              await _tagService.deleteTag(tag.id);

              // Check if widget is still mounted before using context
              if (navigatorContext.mounted) {
                Navigator.of(navigatorContext).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showAddCategoryDialog(TextEditingController categoryController) {
    final TextEditingController newCategoryController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة فئة جديدة'),
        content: TextField(
          controller: newCategoryController,
          decoration: const InputDecoration(
            labelText: 'اسم الفئة',
            hintText: 'أدخل اسم الفئة الجديدة',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newCategoryController.text.trim().isNotEmpty) {
                categoryController.text = newCategoryController.text.trim();
                Navigator.of(context).pop();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    ).then((_) {
      newCategoryController.dispose();
    });
  }

  List<ProblemTag> _getFilteredTags() {
    // Create a new list instead of using the reactive list directly
    final List<ProblemTag> allTags = List<ProblemTag>.from(_tagService.tags);

    // Apply category filter
    final List<ProblemTag> categoryFiltered = _selectedCategory.value != 'all'
        ? allTags
            .where((tag) => tag.category == _selectedCategory.value)
            .toList()
        : allTags;

    // Apply search filter
    final List<ProblemTag> searchFiltered = _searchQuery.value.isNotEmpty
        ? categoryFiltered
            .where((tag) =>
                tag.name
                    .toLowerCase()
                    .contains(_searchQuery.value.toLowerCase()) ||
                tag.category
                    .toLowerCase()
                    .contains(_searchQuery.value.toLowerCase()))
            .toList()
        : categoryFiltered;

    // Create a sorted copy to avoid modifying the original list
    final List<ProblemTag> result = List<ProblemTag>.from(searchFiltered)
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));

    return result;
  }
}
