// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/section_header.dart';
// import '../widgets/product_card.dart';
//
// class ProductsContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const ProductsContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Products section header
//         SectionHeader(
//           title: 'products'.tr,
//           subtitle: 'products_subtitle'.tr,
//           icon: Icons.inventory_2_outlined,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 16),
//
//         // Category filter
//         Obx(() {
//           if (controller.getProductCategoryOptions().isEmpty) {
//             return const SizedBox.shrink();
//           }
//
//           return Column(
//             children: [
//               FilterBar<String>(
//                 title: 'filter_by_category'.tr,
//                 options: controller.getProductCategoryOptions(),
//                 selectedValue: controller.productCategoryFilter.value,
//                 onChanged: controller.setProductCategoryFilter,
//                 isRTL: isRTL,
//               ),
//               const SizedBox(height: 16),
//             ],
//           );
//         }),
//
//         // Stock filter
//         Obx(() => FilterBar<bool>(
//           title: 'filter_by_stock'.tr,
//           options: [
//             ModernDashboardController.FilterOption(
//               label: 'low_stock'.tr,
//               value: true,
//               icon: Icons.warning_amber_outlined,
//             ),
//             ModernDashboardController.FilterOption(
//               label: 'all_stock'.tr,
//               value: false,
//               icon: Icons.inventory_outlined,
//             ),
//           ],
//           selectedValue: controller.productStockFilter.value,
//           onChanged: controller.setProductStockFilter,
//           isRTL: isRTL,
//         )),
//         const SizedBox(height: 16),
//
//         // Products list with loading state
//         Obx(() {
//           if (controller.isProductsLoading.value) {
//             return _buildLoadingList();
//           }
//
//           if (controller.filteredProducts.isEmpty) {
//             return _buildEmptyState();
//           }
//
//           return ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: controller.filteredProducts.length,
//             itemBuilder: (context, index) {
//               final product = controller.filteredProducts[index];
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: ProductCard(
//                   product: product,
//                   isRTL: isRTL,
//                   onTap: () => Get.toNamed('/product/${product.id}'),
//                   onEdit: () => Get.toNamed('/product/${product.id}/edit'),
//                   onDelete: () => _showDeleteConfirmation(context, product),
//                   onRestock: () => _showRestockDialog(context, product),
//                 ),
//               );
//             },
//           );
//         }),
//       ],
//     );
//   }
//
//   // Show delete confirmation dialog
//   void _showDeleteConfirmation(BuildContext context, dynamic product) {
//     Get.defaultDialog(
//       title: 'confirm_delete'.tr,
//       titleStyle: GoogleFonts.cairo(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//       middleText: 'confirm_delete_product'.tr,
//       middleTextStyle: GoogleFonts.cairo(
//         fontSize: 14,
//         color: Colors.black54,
//       ),
//       textConfirm: 'delete'.tr,
//       textCancel: 'cancel'.tr,
//       confirmTextColor: Colors.white,
//       cancelTextColor: AppColors.primary,
//       buttonColor: AppColors.error,
//       onConfirm: () {
//         // Call delete product service
//         Get.back();
//         Get.snackbar(
//           'success'.tr,
//           'product_deleted'.tr,
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: AppColors.success.withOpacity(0.1),
//           colorText: AppColors.success,
//         );
//       },
//     );
//   }
//
//   // Show restock dialog
//   void _showRestockDialog(BuildContext context, dynamic product) {
//     final TextEditingController quantityController = TextEditingController();
//
//     Get.defaultDialog(
//       title: 'restock_product'.tr,
//       titleStyle: GoogleFonts.cairo(
//         fontSize: 18,
//         fontWeight: FontWeight.bold,
//         color: Colors.black87,
//       ),
//       content: Column(
//         children: [
//           Text(
//             'current_quantity'.tr + ': ${product.quantity}',
//             style: GoogleFonts.cairo(
//               fontSize: 14,
//               color: Colors.black54,
//             ),
//           ),
//           const SizedBox(height: 16),
//           TextField(
//             controller: quantityController,
//             keyboardType: TextInputType.number,
//             decoration: InputDecoration(
//               labelText: 'add_quantity'.tr,
//               border: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(8),
//               ),
//             ),
//           ),
//         ],
//       ),
//       textConfirm: 'restock'.tr,
//       textCancel: 'cancel'.tr,
//       confirmTextColor: Colors.white,
//       cancelTextColor: AppColors.primary,
//       buttonColor: AppColors.success,
//       onConfirm: () {
//         // Call restock product service
//         final quantity = int.tryParse(quantityController.text) ?? 0;
//         if (quantity <= 0) {
//           Get.snackbar(
//             'error'.tr,
//             'invalid_quantity'.tr,
//             snackPosition: SnackPosition.BOTTOM,
//             backgroundColor: AppColors.error.withOpacity(0.1),
//             colorText: AppColors.error,
//           );
//           return;
//         }
//
//         Get.back();
//         Get.snackbar(
//           'success'.tr,
//           'product_restocked'.tr,
//           snackPosition: SnackPosition.BOTTOM,
//           backgroundColor: AppColors.success.withOpacity(0.1),
//           colorText: AppColors.success,
//         );
//       },
//     );
//   }
//
//   // Build loading state
//   Widget _buildLoadingList() {
//     return ListView.builder(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 3,
//       itemBuilder: (context, index) {
//         return Padding(
//           padding: const EdgeInsets.only(bottom: 8),
//           child: Container(
//             height: 180,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.05),
//                   blurRadius: 10,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Shimmer.fromColors(
//               baseColor: Colors.grey[300]!,
//               highlightColor: Colors.grey[100]!,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     children: [
//                       Container(
//                         width: 50,
//                         height: 50,
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                       ),
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Container(
//                               width: 120,
//                               height: 20,
//                               color: Colors.white,
//                             ),
//                             const SizedBox(height: 8),
//                             Container(
//                               width: 200,
//                               height: 16,
//                               color: Colors.white,
//                             ),
//                           ],
//                         ),
//                       ),
//                       Container(
//                         width: 80,
//                         height: 24,
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   Container(
//                     width: double.infinity,
//                     height: 1,
//                     color: Colors.white,
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build empty state
//   Widget _buildEmptyState() {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.inventory_2_outlined,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'no_products_found'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'no_products_found_description'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 14,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
