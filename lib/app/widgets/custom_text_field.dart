import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../themes/app_colors.dart';

enum TextFieldVariant {
  outlined,
  filled,
  underlined,
}

class CustomTextField extends StatefulWidget {
  final String name;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final TextInputAction? textInputAction;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final void Function(String?)? onChanged;
  final void Function()? onTap;
  final void Function(String?)? onSubmitted;
  final bool autofocus;
  final bool readOnly;
  final bool enabled;
  final EdgeInsetsGeometry? contentPadding;
  final TextFieldVariant variant;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final double? borderRadius;
  final List<TextInputFormatter>? inputFormatters;
  final bool showCharacterCount;
  final bool isRequired;

  const CustomTextField({
    Key? key,
    required this.name,
    this.labelText,
    this.hintText,
    this.helperText,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.textInputAction,
    this.prefixIcon,
    this.suffixIcon,
    this.focusNode,
    this.controller,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.autofocus = false,
    this.readOnly = false,
    this.enabled = true,
    this.contentPadding,
    this.variant = TextFieldVariant.outlined,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.borderRadius,
    this.inputFormatters,
    this.showCharacterCount = false,
    this.isRequired = false,
  }) : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _borderColorAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _updateBorderAnimation();

    _focusNode.addListener(_onFocusChange);
  }

  void _updateBorderAnimation() {
    final isDark = Get.isDarkMode;
    final normalColor = widget.borderColor ?? AppColors.getBorder(isDark);
    final focusedColor = widget.focusedBorderColor ?? AppColors.primary;

    _borderColorAnimation = ColorTween(
      begin: normalColor,
      end: focusedColor,
    ).animate(_animationController);
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  InputDecoration _buildDecoration() {
    final isDark = Get.isDarkMode;
    final borderRadius = BorderRadius.circular(widget.borderRadius ?? 12.r);

    // Get colors
    final fillColor = widget.fillColor ??
        (widget.variant == TextFieldVariant.filled
            ? AppColors.getSurface(isDark)
            : Colors.transparent);

    final borderColor = widget.borderColor ?? AppColors.getBorder(isDark);
    final focusedBorderColor = widget.focusedBorderColor ?? AppColors.primary;
    final errorColor = AppColors.error;

    // Build borders based on variant
    InputBorder? border;
    InputBorder? enabledBorder;
    InputBorder? focusedBorder;
    InputBorder? errorBorder;
    InputBorder? focusedErrorBorder;

    switch (widget.variant) {
      case TextFieldVariant.outlined:
        border = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: borderColor, width: 1),
        );
        enabledBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: borderColor, width: 1),
        );
        focusedBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: focusedBorderColor, width: 2),
        );
        errorBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: errorColor, width: 1.5),
        );
        focusedErrorBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: errorColor, width: 2),
        );
        break;

      case TextFieldVariant.filled:
        border = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide.none,
        );
        enabledBorder = border;
        focusedBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: focusedBorderColor, width: 2),
        );
        errorBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: errorColor, width: 1.5),
        );
        focusedErrorBorder = OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: errorColor, width: 2),
        );
        break;

      case TextFieldVariant.underlined:
        border = UnderlineInputBorder(
          borderSide: BorderSide(color: borderColor, width: 1),
        );
        enabledBorder = border;
        focusedBorder = UnderlineInputBorder(
          borderSide: BorderSide(color: focusedBorderColor, width: 2),
        );
        errorBorder = UnderlineInputBorder(
          borderSide: BorderSide(color: errorColor, width: 1.5),
        );
        focusedErrorBorder = UnderlineInputBorder(
          borderSide: BorderSide(color: errorColor, width: 2),
        );
        break;
    }

    return InputDecoration(
      filled: widget.variant == TextFieldVariant.filled,
      fillColor: fillColor,
      hintText: widget.hintText,
      helperText: widget.helperText,
      hintStyle: TextStyle(
        color: AppColors.getTextTertiary(isDark),
        fontSize: 16.sp,
      ),
      helperStyle: TextStyle(
        color: AppColors.getTextSecondary(isDark),
        fontSize: 12.sp,
      ),
      contentPadding: widget.contentPadding ??
          EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      prefixIcon: widget.prefixIcon,
      suffixIcon: _buildSuffixIcon(),
      border: border,
      enabledBorder: enabledBorder,
      focusedBorder: focusedBorder,
      errorBorder: errorBorder,
      focusedErrorBorder: focusedErrorBorder,
      counterText: widget.showCharacterCount ? null : '',
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.suffixIcon != null) {
      return widget.suffixIcon;
    }

    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _isFocused ? Icons.visibility_off : Icons.visibility,
          color: AppColors.getTextSecondary(Get.isDarkMode),
        ),
        onPressed: () {
          // Toggle password visibility
          // This would need to be implemented with a state variable
        },
      );
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Get.isDarkMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null)
          Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: RichText(
              text: TextSpan(
                text: widget.labelText!,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.getTextSecondary(isDark),
                ),
                children: [
                  if (widget.isRequired)
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: AppColors.error,
                        fontSize: 14.sp,
                      ),
                    ),
                ],
              ),
            ),
          ),
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FormBuilderTextField(
              name: widget.name,
              obscureText: widget.obscureText,
              keyboardType: widget.keyboardType,
              maxLines: widget.maxLines,
              minLines: widget.minLines,
              maxLength: widget.maxLength,
              textInputAction: widget.textInputAction,
              focusNode: _focusNode,
              controller: widget.controller,
              onChanged: widget.onChanged,
              onTap: widget.onTap,
              onSubmitted: widget.onSubmitted,
              autofocus: widget.autofocus,
              readOnly: widget.readOnly,
              enabled: widget.enabled,
              inputFormatters: widget.inputFormatters,
              decoration: _buildDecoration(),
              validator: widget.validator,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.getTextPrimary(isDark),
              ),
            );
          },
        ),
      ],
    );
  }
}
