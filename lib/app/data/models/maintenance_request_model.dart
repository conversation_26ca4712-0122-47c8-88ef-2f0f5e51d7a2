import 'package:cloud_firestore/cloud_firestore.dart';
import 'car_model.dart';
import 'technician_model.dart';

class MaintenanceRequest {
  final String id;
  final Car car;
  final String maintenanceType;
  final DateTime date;
  final String status;
  final String notes;
  final Technician? assignedTechnician;
  final String? assignedBy;
  final String? assignedByName;
  final DateTime? assignmentDate;
  final Map<String, dynamic>? metadata;

  MaintenanceRequest({
    required this.id,
    required this.car,
    required this.maintenanceType,
    required this.date,
    required this.status,
    required this.notes,
    this.assignedTechnician,
    this.assignedBy,
    this.assignedByName,
    this.assignmentDate,
    this.metadata,
  });

  factory MaintenanceRequest.fromJson(Map<String, dynamic> json) {
    return MaintenanceRequest(
      id: json['id'] as String,
      car: Car.fromJson(json['car'] as Map<String, dynamic>),
      maintenanceType: json['maintenanceType'] as String,
      date: (json['date'] as Timestamp).toDate(),
      status: json['status'] as String,
      notes: json['notes'] as String? ?? '',
      assignedTechnician: json['assignedTechnician'] != null
          ? Technician.fromJson(json['assignedTechnician'] as Map<String, dynamic>)
          : null,
      assignedBy: json['assignedBy'] as String?,
      assignedByName: json['assignedByName'] as String?,
      assignmentDate: json['assignmentDate'] != null
          ? (json['assignmentDate'] as Timestamp).toDate()
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'car': car.toJson(),
      'maintenanceType': maintenanceType,
      'date': Timestamp.fromDate(date),
      'status': status,
      'notes': notes,
      if (assignedTechnician != null)
        'assignedTechnician': assignedTechnician!.toJson(),
      if (assignedBy != null) 'assignedBy': assignedBy,
      if (assignedByName != null) 'assignedByName': assignedByName,
      if (assignmentDate != null)
        'assignmentDate': Timestamp.fromDate(assignmentDate!),
      if (metadata != null) 'metadata': metadata,
    };
  }
}
