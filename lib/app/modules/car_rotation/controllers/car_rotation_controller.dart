import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/car_rotate.dart';
import '../../../data/models/sector_model.dart';
import '../../../services/car_rotation_service.dart';
import '../../../services/car_service.dart';
import '../../../services/sector_service.dart';
import '../../../services/user_storage_service.dart';
import 'package:flutter/material.dart';

class CarRotationController extends GetxController {
  final CarService carService = Get.find();
  final SectorService sectorService = Get.find();
  final CarRotationService carRotationService = Get.find();
  final _firestore = FirebaseFirestore.instance;

  CarRotate? carRotation;
  final currentStep = 1.obs;
  final isLoading = false.obs;
  final isLoadingCurrentSector = false.obs;
  final isSubmitting = false.obs;
  final sectors = <Sector>[].obs;
  final newSectors = <Sector>[].obs;
  final cars = <Car>[].obs;
  
  final selectedFromSector = Rxn<Sector>();
  final selectedCar = Rxn<Car>();
  final selectedNewSector = Rxn<Sector>();
  final selectedRotationType = Rx<RotateType>(RotateType.temporary);
  final currentSector = Rxn<Sector>();
  final reasonController = TextEditingController();

  final LocalStorageService localStorageService = Get.find();
  final RxString uid = ''.obs;
  final RxString role = ''.obs;
  final RxString userName = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    uid.value = (await localStorageService.getUserId()!)!;
    role.value = (await localStorageService.getUserRole()!)!;
    userName.value = (await localStorageService.getUsername()!)!;
    if(Get.arguments != null) {
      carRotation = Get.arguments;
      reasonController.text = 'دعم';
    }
    loadSectors();

    ever(selectedFromSector, (Sector? sector) {
      if (sector != null) {
        loadCarsForSector(sector.id);
      } else {
        cars.clear();
      }
    });
  }

  @override
  void onClose() {
    reasonController.dispose();
    super.onClose();
  }

  bool get hasPermission => role.value == 'supervisor' || role.value == 'admin';

  Future<void> loadSectors() async {
    try {
      isLoading.value = true;
      final sectorsList = await sectorService.getSectors();
      newSectors.value = sectorsList;
      if(carRotation != null) {
        selectedNewSector.value = newSectors.firstWhere((s) => s.id == carRotation!.toSectorId);
        sectors.assignAll(sectorsList.where((s) => s.id != carRotation!.toSectorId).toList());
      }else {
        sectors.assignAll(sectorsList);

      }
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to load sectors'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadCarsForSector(String sectorId) async {
    try {
      isLoading.value = true;
      final carsList = await carService.getCarsBySector(sectorId);
      cars.assignAll(carsList);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to load cars'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _loadCurrentSector(String sectorId) async {
    try {
      isLoadingCurrentSector.value = true;
      currentSector.value = await sectorService.getSectorById(sectorId);
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to load current sector'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoadingCurrentSector.value = false;
    }
  }

  Future<void> submitRotation() async {
    if (!hasPermission) {
      Get.snackbar(
        'Error'.tr,
        'Unauthorized access'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    if (selectedCar.value == null ||
        selectedFromSector.value == null ||
        selectedNewSector.value == null ||
        reasonController.text.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'في حقول فارغة'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      isSubmitting.value = true;
      if(carRotation != null) {
        await carRotationService.updateCarRotation(
          carRotation: carRotation!,
          car: selectedCar.value!,
          action: RotateAction.none,
          cause: reasonController.text,
          isActive: true,
          fromSectorId: selectedFromSector.value!.id,
          toSectorId: selectedNewSector.value!.id,
          fromSectorName: selectedFromSector.value!.name,
          toSectorName: selectedNewSector.value!.name,
          type: selectedRotationType.value,
        );
        Get.back();
        Get.snackbar(
          'Success'.tr,
          'Rotation updated successfully'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }
      await carRotationService.createCarRotation(
        car: selectedCar.value!,
        cause: reasonController.text,
        isActive: true,
        action: RotateAction.none,
        fromSectorId: selectedFromSector.value!.id,
        toSectorId: selectedNewSector.value!.id,
        fromSectorName: selectedFromSector.value!.name,
        toSectorName: selectedNewSector.value!.name,
        type: selectedRotationType.value,
      );

      Get.back();
      Get.snackbar(
        'Success'.tr,
        'Rotation request sent successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to create rotation request'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isSubmitting.value = false;
    }
  }

  void goToStep(int step) {
    if (step < currentStep.value) {
      if (step <= 1) {
        selectedFromSector.value = null;
        selectedCar.value = null;
        selectedNewSector.value = null;
        selectedRotationType.value = RotateType.temporary;
        reasonController.clear();
      } else if (step <= 2) {
        selectedCar.value = null;
        selectedNewSector.value = null;
        selectedRotationType.value = RotateType.temporary;
        reasonController.clear();
      }
    }
    currentStep.value = step;
  }
}
