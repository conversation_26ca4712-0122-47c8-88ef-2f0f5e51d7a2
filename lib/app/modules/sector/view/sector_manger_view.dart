import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/data/models/user_model.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:cars_app/app/services/car_rotation_service.dart';
import 'package:cars_app/app/themes/app_theme.dart';
import 'package:cars_app/app/utils/connection_utils.dart';
import 'package:cars_app/app/widgets/awesome_snackbar_content.dart';
import 'package:cars_app/app/widgets/car_rotation_dashboard.dart';
import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:cars_app/app/widgets/custom_text_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/notification_icon_widget.dart';
import '../../../widgets/sector_car_card.dart';
import '../controller/sector_manager_controller.dart';
import '../../../data/models/car_model.dart';

class SectorMangerView extends GetView<SectorManagerController> {
  const SectorMangerView({super.key});

  @override
  Widget build(BuildContext context) {

    return DefaultTabController(
        length: 4,
        child: Scaffold(
            backgroundColor: AppColors.background,
            body: Column(children: [
              // No internet connection banner
              ConnectionUtils.buildNoConnectionBanner(),
              // Main content
              Expanded(
                child: Obx(() {
                  if (controller.isLoading.value) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (controller.cars.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.directions_car_outlined,
                            size: 64,
                            color: AppColors.primary.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'لا توجد سيارات في هذا القطاع',
                            style: TextStyle(
                              fontSize: 18,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return StreamBuilder<List<Car>>(
                    key: UniqueKey(),

                    stream: controller.getCarsStream(),
                    builder: (context, snapshot) {
                      final cars = snapshot.data ?? [];
                      return NestedScrollView(
                        headerSliverBuilder: (context, innerBoxIsScrolled) {
                          return [

                            _buildSliverAppBar(
                                context, cars, innerBoxIsScrolled),
                          ];
                        },
                        body: Column(
                          children: [
                            Obx(() {
                              // Get filtered count for badge
                              final filteredCount =
                                  controller.selectedStatus.value != null
                                      ? cars.length
                                      : 0;

                              return TabBar(
                                indicatorColor: AppColors.primary,
                                labelColor: AppColors.primary,
                                unselectedLabelColor: Colors.grey,
                                tabs: [
                                  Tab(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Text('السيارات'),
                                        if (filteredCount > 0) ...[
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: AppColors.primary,
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            child: Text(
                                              '$filteredCount',
                                              style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10),
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  const Tab(text: 'الدعم'),
                                  const Tab(text: 'اعاده'),
                                  const Tab(text: 'التدوير'),
                                ],
                              );
                            }),
                            Expanded(
                              child: TabBarView(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    child: ListView.builder(
                                      itemCount: cars.length,
                                      padding: const EdgeInsets.only(
                                          top: 8, bottom: 16),
                                      itemBuilder: (context, index) {
                                        final car = cars[index];
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 8),
                                          child: SectorCarCard(
                                            sectorId:
                                                controller.sector.value!.id,
                                            uid: controller.uid.value,
                                            car: car,
                                            confirmCallCarToWorkshop: () {
                                              controller.carService
                                                  .updateCarStatusWithEntry(
                                                      car,
                                                      car.id,
                                                      CarStatus
                                                          .agreeDeliveryToWorkShop,
                                                      '');
                                            },
                                            rejectCallCarToWorkshop: () {
                                              controller.carService
                                                  .updateCarStatusWithEntry(
                                                      car,
                                                      car.id,
                                                      CarStatus.rejected,
                                                      '');
                                            },
                                            cancelAction: () {
                                              controller.carService
                                                  .updateCarStatusWithEntry(
                                                      car,
                                                      car.id,
                                                      CarStatus.rejected,
                                                      '');
                                            },
                                            confirmAction: () {
                                              controller.carService
                                                  .updateCarStatusWithEntry(
                                                      car,
                                                      car.id,
                                                      CarStatus.active,
                                                      '');
                                            },
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  _buildSupportWidget(),
                                  _buildRestoreWidget(),
                                  CarRotationDashboard(
                                    sectorId: controller.sector.value!.id,
                                    action: null,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                }),
              )
            ])));
  }

  Widget _buildSliverAppBar(
      BuildContext context, List<Car> cars, bool innerBoxIsScrolled) {
    return SliverAppBar(
      expandedHeight: 220.0,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      elevation: 0,
      forceElevated: innerBoxIsScrolled,
      title: Obx(() {
        String title = 'مشرف قطاع ${controller.sector.value?.name ?? ''}';

        // Add filter indicator to title if a filter is active
        if (controller.selectedStatus.value != null) {
          String filterName = '';
          switch (controller.selectedStatus.value) {
            case CarStatus.active:
              filterName = 'نشطة';
              break;
            case CarStatus.callToWorkshop:
              filterName = 'استدعاء للورشة';
              break;
            case CarStatus.pending:
              filterName = 'عند الفني';
              break;
            case CarStatus.sendGroup:
              filterName = 'أرسال مجموعة';
              break;
            case CarStatus.receipt:
              filterName = 'تم الاستلام';
              break;
            case CarStatus.sentRequest:
              filterName = 'ارسال طلب';
              break;
            case CarStatus.done:
              filterName = 'السيارات الجاهزة';
              break;
            default:
              filterName = '';
          }

          if (filterName.isNotEmpty) {
            title += ' (تصفية: $filterName)';
          }
        }

        return Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          overflow: TextOverflow.ellipsis,
        );
      }),
      actions: [
        // Filter indicator and clear button
        Obx(() => controller.selectedStatus.value != null
            ? IconButton(
                icon: const Icon(Icons.filter_list_off, color: Colors.white),
                tooltip: 'إلغاء التصفية',
                onPressed: () => controller.setSelectedStatus(null),
              )
            : const SizedBox.shrink()),
        // Search icon
        Obx(() => controller.isSearching.value
            ? IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: controller.toggleSearch,
              )
            : IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: controller.toggleSearch,
              )),
        const NotificationIconWidget(),
        IconButton(
          onPressed: () => Get.toNamed(Routes.PROFILE),
          icon: const Icon(Icons.person, color: Colors.white),
        ),
        IconButton(
          onPressed: () => Get.toNamed(Routes.CarRotationDashboard,
              arguments: controller.sector.value?.id),
          icon: const Icon(Icons.rotate_90_degrees_ccw, color: Colors.white),
        ),
      ],
      // Search bar when isSearching is true
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: Obx(() => controller.isSearching.value
            ? Container(
                height: 60,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                color: AppColors.primary,
                child: TextField(
                  controller: controller.searchController,
                  autofocus: true,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'بحث عن سيارة...',
                    hintStyle: const TextStyle(color: Colors.white70),
                    prefixIcon: const Icon(Icons.search, color: Colors.white),
                    filled: true,
                    fillColor: Colors.white.withOpacity(0.2),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onChanged: controller.updateSearchQuery,
                ),
              )
            : const SizedBox()),
      ),

      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          color: AppColors.primary,
          child: Padding(
            padding: const EdgeInsets.only(
                top: 100.0, left: 16.0, right: 16.0, bottom: 16.0),
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildInfoColumn(
                            'عدد السيارات',
                            '${cars.where((car) => car.tempSectorId.isEmpty || car.tempSectorId == controller.sector.value!.id).length}',
                            Icons.directions_car,
                          ),
                          _buildInfoColumn(
                            'نشطة',
                            '${cars.where((car) => car.status == CarStatus.active && car.tempSectorId.isEmpty).length}',
                            Icons.check_circle,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'استدعاء للورشة',
                            '${cars.where((car) => car.status == CarStatus.callToWorkshop).length}',
                            Icons.call_made,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'عند الفني',
                            '${cars.where((car) => car.status == CarStatus.pending).length}',
                            Icons.accessibility_new,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'أرسال مجموعة',
                            '${cars.where((car) => car.status == CarStatus.sendGroup).length}',
                            Icons.group,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'تم الاستلام',
                            '${cars.where((car) => car.status == CarStatus.receipt).length}',
                            Icons.receipt,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'ارسال طلب',
                            '${cars.where((car) => car.status == CarStatus.sentRequest).length}',
                            Icons.request_page,
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: Colors.white.withOpacity(0.2),
                          ),
                          _buildInfoColumn(
                            'السيارات الجاهزة',
                            '${cars.where((car) => car.status == CarStatus.done).length}',
                            Icons.done,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSupportWidget() {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
        onPressed: () {
          Get.bottomSheet(
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  _buildNotesCard(),
                  CustomButton(
                    isSwipe: true,
                    text: 'تأكيد',
                    onPressed: () {
                      controller.reasonController.text = '';
                      CarRotationService rotationService = Get.find();
                      rotationService.createCarRotation(
                          car: null,
                          action: RotateAction.support,
                          cause: controller.reasonController.text,
                          fromSectorId: '',
                          toSector: RotateStatus.accepted,
                          toSectorId: controller.sector.value!.id,
                          fromSectorName: '',
                          toSectorName: controller.sector.value!.name,
                          type: RotateType.temporary,
                          isActive: false);
                      Get.back();
                      showCustomSnackBar(
                          title: 'طلب دعم',
                          message: 'تم ارسال طلب الدعم',
                          contentType: ContentType.success);
                    },
                  )
                ],
              ),
            ),
          );
        },
      ),
      body: CarRotationDashboard(
        action: RotateAction.support,
        sectorId: controller.sector.value!.id,
      ),
    );
  }

  Widget _buildRestoreWidget() {
    return Scaffold(
      body: CarRotationDashboard(
        action: RotateAction.restore,
        sectorId: controller.sector.value?.id??controller.selectedSectorId.value,
      ),
    );
  }

  Widget _buildNotesCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.note_add,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                'سبب الدعم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: controller.reasonController,
            maxLines: 3,
            textDirection: TextDirection.rtl,
            style: const TextStyle(fontFamily: 'Cairo'),
            decoration: InputDecoration(
              hintText: 'اكتب سبب الدعم',
              hintStyle: const TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // Handle notes input
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value, IconData icon) {
    // Determine if this column is currently selected
    bool isSelected = false;
    String statusName = '';

    switch (label) {
      case 'نشطة':
        statusName = 'active';
        isSelected = controller.selectedStatus.value == CarStatus.active;
        break;
      case 'استدعاء للورشة':
        statusName = 'calltoworkshop';
        isSelected =
            controller.selectedStatus.value == CarStatus.callToWorkshop;
        break;
      case 'عند الفني':
        statusName = 'pending';
        isSelected = controller.selectedStatus.value == CarStatus.pending;
        break;
      case 'أرسال مجموعة':
        statusName = 'sendgroup';
        isSelected = controller.selectedStatus.value == CarStatus.sendGroup;
        break;
      case 'تم الاستلام':
        statusName = 'receipt';
        isSelected = controller.selectedStatus.value == CarStatus.receipt;
        break;
      case 'ارسال طلب':
        statusName = 'sentrequest';
        isSelected = controller.selectedStatus.value == CarStatus.sentRequest;
        break;
      case 'السيارات الجاهزة':
        statusName = 'done';
        isSelected = controller.selectedStatus.value == CarStatus.done;
        break;
    }

    // Skip making 'عدد السيارات' clickable as it's the total count
    final bool isClickable = label != 'عدد السيارات';

    return InkWell(
      onTap:
          isClickable ? () => controller.filterByStatusName(statusName) : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected ? Border.all(color: Colors.white, width: 1) : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
            if (isSelected)
              const Padding(
                padding: EdgeInsets.only(top: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.filter_list,
                      color: Colors.white,
                      size: 12,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'مفعل',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
