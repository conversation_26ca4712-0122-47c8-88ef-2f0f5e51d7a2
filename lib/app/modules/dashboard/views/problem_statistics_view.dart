import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../data/models/problem_tag_model.dart';
import '../../../services/problem_tag_service.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/shared_app_bar.dart';

class ProblemStatisticsView extends StatefulWidget {
  const ProblemStatisticsView({Key? key}) : super(key: key);

  @override
  State<ProblemStatisticsView> createState() => _ProblemStatisticsViewState();
}

class _ProblemStatisticsViewState extends State<ProblemStatisticsView>
    with SingleTickerProviderStateMixin {
  final ProblemTagService _tagService = Get.find<ProblemTagService>();
  late TabController _tabController;
  final RxString _selectedCategory = 'all'.obs;
  final RxInt _timeRange = 30.obs; // Default to 30 days

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tagService.loadTags();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SharedAppBar(
        title: 'إحصائيات المشاكل الشائعة',
        // showBackButton: true,
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          TabBar(
            controller: _tabController,
            indicatorColor: AppColors.primary,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'رسم بياني'),
              Tab(text: 'قائمة'),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChartView(),
                _buildListView(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Time range selector
          Row(
            children: [
              const Text('الفترة الزمنية:'),
              const SizedBox(width: 16),
              Expanded(
                child: Obx(() => SegmentedButton<int>(
                      segments: const [
                        ButtonSegment(value: 7, label: Text('7 أيام')),
                        ButtonSegment(value: 30, label: Text('30 يوم')),
                        ButtonSegment(value: 90, label: Text('3 أشهر')),
                        ButtonSegment(value: 365, label: Text('سنة')),
                      ],
                      selected: {_timeRange.value},
                      onSelectionChanged: (Set<int> selection) {
                        _timeRange.value = selection.first;
                      },
                    )),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Category filter
          Obx(() {
            final categories = ['all', ..._tagService.getAllCategories()];
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: categories.map((category) {
                  final isSelected = _selectedCategory.value == category;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category == 'all' ? 'الكل' : category),
                      selected: isSelected,
                      onSelected: (selected) {
                        _selectedCategory.value = selected ? category : 'all';
                      },
                      backgroundColor: Colors.grey[200],
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                      labelStyle: TextStyle(
                        color: isSelected ? AppColors.primary : Colors.black87,
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildChartView() {
    return Obx(() {
      if (_tagService.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final filteredTags = _getFilteredTags();

      if (filteredTags.isEmpty) {
        return Center(
          child: Text(
            'لا توجد بيانات متاحة',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        );
      }

      return Padding(
        padding: const EdgeInsets.all(16),
        child: SfCircularChart(
          title: ChartTitle(
            text: 'توزيع المشاكل الشائعة',
            textStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          legend: Legend(
            isVisible: true,
            position: LegendPosition.bottom,
            overflowMode: LegendItemOverflowMode.wrap,
          ),
          tooltipBehavior: TooltipBehavior(enable: true),
          series: <CircularSeries>[
            PieSeries<ProblemTag, String>(
              dataSource: filteredTags,
              xValueMapper: (ProblemTag tag, _) => tag.name,
              yValueMapper: (ProblemTag tag, _) => tag.usageCount,
              dataLabelMapper: (ProblemTag tag, _) =>
                  '${tag.name} (${tag.usageCount})',
              dataLabelSettings: const DataLabelSettings(
                isVisible: true,
                labelPosition: ChartDataLabelPosition.outside,
              ),
              enableTooltip: true,
              explode: true,
              explodeIndex: 0,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildListView() {
    return Obx(() {
      if (_tagService.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final filteredTags = _getFilteredTags();

      if (filteredTags.isEmpty) {
        return Center(
          child: Text(
            'لا توجد بيانات متاحة',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        );
      }

      // Calculate total usage count for percentage
      final totalUsageCount =
          filteredTags.fold<int>(0, (sum, tag) => sum + tag.usageCount);

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTags.length,
        itemBuilder: (context, index) {
          final tag = filteredTags[index];
          final percentage = totalUsageCount > 0
              ? (tag.usageCount / totalUsageCount * 100).toStringAsFixed(1)
              : '0.0';

          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: ListTile(
              title: Text(tag.name),
              subtitle: Text('الفئة: ${tag.category}'),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${tag.usageCount} مرة',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$percentage%',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  List<ProblemTag> _getFilteredTags() {
    // Create a new list instead of using the reactive list directly
    final List<ProblemTag> allTags = List<ProblemTag>.from(_tagService.tags);

    // Apply category filter
    final List<ProblemTag> categoryFiltered = _selectedCategory.value != 'all'
        ? allTags
            .where((tag) => tag.category == _selectedCategory.value)
            .toList()
        : allTags;

    // Apply time range filter (this would require additional data in a real app)
    // For now, we'll just use all data

    // Create a sorted copy to avoid modifying the original list
    final List<ProblemTag> sortedTags = List<ProblemTag>.from(categoryFiltered)
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));

    // Take top 10 for better visualization
    final List<ProblemTag> result =
        sortedTags.length > 10 ? sortedTags.sublist(0, 10) : sortedTags;

    return result;
  }
}
