import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/maintenance_checklist_controller.dart';
import '../widgets/maintenance_checklist.dart';

class MaintenanceChecklistView extends GetView<MaintenanceChecklistController> {
  const MaintenanceChecklistView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('قائمة الصيانة'.tr),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.select_all),
            tooltip: 'تحديد الكل'.tr,
            onPressed: () => controller.selectAll(),
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            tooltip: 'مسح التحديد'.tr,
            onPressed: () => controller.clearSelection(),
          ),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  children: [
                    // Table view of the checklist
                    MaintenanceChecklistTable(
                      selectedItems: controller.selectedItems,
                      onItemToggle: (id) => controller.toggleItem(id),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Grid view of the checklist
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'عرض شبكي'.tr,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    MaintenanceChecklist(
                      selectedItems: controller.selectedItems,
                      onItemToggle: (id) => controller.toggleItem(id),
                    ),
                  ],
                ),
              ),
      ),
      bottomNavigationBar: Obx(
        () => controller.selectedItems.isNotEmpty
            ? BottomAppBar(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Text(
                        'تم تحديد ${controller.selectedItems.length} عنصر'.tr,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () {
                          // Save the checklist
                          final checklist = controller.exportChecklist();
                          Get.back(result: checklist);
                        },
                        child: Text('حفظ'.tr),
                      ),
                    ],
                  ),
                ),
              )
            : const SizedBox.shrink(),
      ),
    );
  }
}

// A simpler view that can be embedded in other screens
class EmbeddedMaintenanceChecklistView extends StatelessWidget {
  final MaintenanceChecklistController controller;
  final bool readOnly;
  
  const EmbeddedMaintenanceChecklistView({
    Key? key,
    required this.controller,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.isLoading.value
          ? const Center(child: CircularProgressIndicator())
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      Text(
                        'قائمة الصيانة'.tr,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (!readOnly) ...[
                        TextButton.icon(
                          icon: const Icon(Icons.select_all, size: 16),
                          label: Text('تحديد الكل'.tr),
                          onPressed: () => controller.selectAll(),
                        ),
                        const SizedBox(width: 8),
                        TextButton.icon(
                          icon: const Icon(Icons.clear_all, size: 16),
                          label: Text('مسح'.tr),
                          onPressed: () => controller.clearSelection(),
                        ),
                      ],
                    ],
                  ),
                ),
                MaintenanceChecklistTable(
                  selectedItems: controller.selectedItems,
                  onItemToggle: readOnly ? null : (id) => controller.toggleItem(id),
                  readOnly: readOnly,
                ),
              ],
            ),
    );
  }
}
