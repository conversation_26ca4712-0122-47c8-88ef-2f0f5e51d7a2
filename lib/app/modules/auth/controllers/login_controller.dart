import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../routes/app_pages.dart';

class LoginController extends GetxController {
  final formKey = GlobalKey<FormBuilderState>();
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final isLoading = false.obs;
  final errorMessage = ''.obs;

  final AuthController _authController = Get.find<AuthController>();

  Future<void> login() async {
    if (!formKey.currentState!.saveAndValidate()) return;

    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      // Get username and password from form
      final username = formKey.currentState?.value['username_or_email'];
      final password = formKey.currentState?.value['password'];

      // Call auth controller login method with username and password
      final result = await _authController.login(
        username,
        password,
      );

      if (result['success']) {
        // Use postFrameCallback to ensure navigation happens after the build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          switch (_authController.userRole) {
            case 'admin':
              Get.offAllNamed(Routes.HOME);
              break;
            case 'manager':
              Get.offAllNamed(Routes.SECTOR_MANAGER);
              break;
              case 'logisticsSupport':
              Get.offAllNamed(Routes.LOGASTIC_SUPPORT);
              break;
            case 'supervisor':
              Get.offAllNamed(Routes.SUPERVISOR);
              break;
            case 'user':
              Get.offAllNamed(Routes.CARS);
              break;
              case 'technician':
              Get.offAllNamed(Routes.TECHNICIAN);
              break;
            default:
              errorMessage.value = 'Invalid user role';
          }
        });
      } else {
        errorMessage.value = result['message'];
      }
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
