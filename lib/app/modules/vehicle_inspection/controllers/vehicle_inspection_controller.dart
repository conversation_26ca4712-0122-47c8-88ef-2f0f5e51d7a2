import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/vehicle_inspection.dart';
import '../../../data/models/inspection_status.dart';
import '../../../controllers/auth_controller.dart';
import '../../../services/car_service.dart';
import '../models/car_part.dart';
import '../../../data/models/car_model.dart';

class VehicleInspectionController extends GetxController {
  final inspection = VehicleInspection().obs;
  final isLoading = false.obs;
  final TextEditingController noteController = TextEditingController();
  final TextEditingController meterReadingController = TextEditingController();

  // Selected car parts
  final RxList<CarPart> selectedParts = <CarPart>[].obs;

  // Current car
  final Rx<Car?> currentCar = Rx<Car?>(null);

  // Car service for updating car data
  final CarService carService = Get.find<CarService>();

  // Logger for debugging
  final logger = Logger();

  @override
  void onInit() {
    super.onInit();
    // Initialize with a new inspection
    inspection.value = VehicleInspection();

    // Get the car from arguments if available
    if (Get.arguments != null && Get.arguments is Car) {
      currentCar.value = Get.arguments as Car;

      // Load meter reading from car if available
      if (currentCar.value?.meterReading != null) {
        meterReadingController.text = currentCar.value!.meterReading.toString();
      }
    }
  }

  bool validateForm() {
    // No need to validate all items anymore
    return true;
  }

  // Add a selected part
  void addSelectedPart(CarPart part) {
    // Check if part is already in the list
    final existingIndex = selectedParts.indexWhere((p) => p.id == part.id);

    if (existingIndex >= 0) {
      // Update existing part
      selectedParts[existingIndex] = part;
    } else {
      // Add new part
      selectedParts.add(part);
    }
  }

  Future<void> saveInspection() async {
    if (!validateForm()) {
      Get.snackbar(
        'Error'.tr,
        'allFieldsRequired'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      isLoading.value = true;

      // Add note to inspection
      inspection.value.note = noteController.text;

      // Add meter reading to inspection if provided
      if (meterReadingController.text.isNotEmpty) {
        final meterReading = int.tryParse(meterReadingController.text);
        if (meterReading != null) {
          inspection.value.meterReading = meterReading;

          // Update car meter reading if car is available
          if (currentCar.value != null) {
            // Update the car object with the new meter reading
            currentCar.value = currentCar.value!.copyWith(
              meterReading: meterReading,
            );

            logger.d("Updating car with meter reading: $meterReading");
          }
        }
      }

      // Add selected parts data to inspection
      final partsData = selectedParts
          .map((part) => {
                'id': part.id,
                'name': part.name,
                'view': part.view.toString(),
                'damageLevel': part.damageLevel ?? 0,
                'notes': part.notes ?? '',
              })
          .toList();

      inspection.value.damagedParts = partsData;

      // Log the inspection data for debugging
      logger.d("Inspection data: ${inspection.value.toJson()}");
      if (currentCar.value != null) {
        logger.d("Car meter reading: ${currentCar.value!.meterReading}");
      //
      //   try {
      //     // Update the car in Firestore directly
      //     await carService.updateCar(currentCar.value!);
      //     logger.d("Car updated successfully in Firestore");
      //   } catch (e) {
      //     logger.e("Error updating car in Firestore: $e");
      //     // Continue with the process even if the car update fails
      //   }
      }

      // Return both the inspection and updated car
      Get.back(result: {
        'inspection': inspection.value,
        'car': currentCar.value,
      });
      // Show success message
      Get.snackbar(
        'Success'.tr,
        'Inspection saved successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void dispose() {
    noteController.dispose();
    meterReadingController.dispose();
    super.dispose();
  }
}
