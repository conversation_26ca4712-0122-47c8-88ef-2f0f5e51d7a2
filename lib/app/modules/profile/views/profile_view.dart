import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../widgets/custom_button.dart';
import '../controllers/profile_controller.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: const Text(
          'الملف الشخصي',
          style: TextStyle(color: Colors.white),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header with curved bottom
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(40),
                  bottomRight: Radius.circular(40),
                ),
              ),
              child: SlideTransition(
                position: controller.slideAnimation,
                child: FadeTransition(
                  opacity: controller.fadeAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        const CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.white,
                          child: Icon(
                            Icons.person,
                            size: 60,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Obx(() =>
                            Text(
                              controller.userName.value ?? 'المستخدم',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            )),
                        const SizedBox(height: 8),
                        Obx(() =>
                            Text(
                              controller.role.value ?? 'الدور',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white70,
                              ),
                            )),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // Profile Details
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: SlideTransition(
                position: controller.slideAnimation,
                child: FadeTransition(
                  opacity: controller.fadeAnimation,
                  child: Column(
                    children: [
                      Obx(() {
                        return _buildInfoCard(
                          icon: Icons.badge,
                          title: 'أسم المستخدم',
                          value: controller.userName.value ?? '-',
                        );
                      }),
                      const SizedBox(height: 16),
                      Obx(() {
                        return _buildInfoCard(
                          icon: Icons.verified_user,
                          title: 'حالة الحساب',
                          value: controller.userName.value != ''
                              ? 'نشط'
                              : 'غير نشط',
                          valueColor: controller.userName.value != ''
                              ? AppColors.success
                              : AppColors.error,
                        );
                      }),
                      const SizedBox(height: 16),
                      _buildActionCard(
                        icon: Icons.settings,
                        title: 'إعدادات الحساب',
                        onTap: () {
                          _showPasswordResetSheet(context);
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildActionCard(
                        icon: Icons.logout,
                        title: 'تسجيل الخروج',
                        onTap: () {
                          Get.find<AuthController>().logout();
                          // TODO: Navigate to help
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  color: valueColor ?? AppColors.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textLight,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  void _showPasswordResetSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.lock_reset,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'تغيير كلمة المرور',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // _buildPasswordField(
              //   controller: controller.currentPasswordController,
              //   label: 'كلمة المرور الحالية',
              //   isVisible: controller.isCurrentPasswordVisible,
              //   onToggleVisibility: controller.toggleCurrentPasswordVisibility,
              // ),
              // const SizedBox(height: 16),
              _buildPasswordField(
                controller: controller.newPasswordController,
                label: 'كلمة المرور الجديدة',
                isVisible: controller.isNewPasswordVisible,
                onToggleVisibility: controller.toggleNewPasswordVisibility,
              ),
              const SizedBox(height: 16),

              _buildPasswordField(
                controller: controller.confirmPasswordController,
                label: 'تأكيد كلمة المرور الجديدة',
                isVisible: controller.isConfirmPasswordVisible,
                onToggleVisibility: controller.toggleConfirmPasswordVisibility,
              ),
              const SizedBox(height: 24),
              Obx(() =>
                  CustomButton(
                    isLoading: controller.isLoading.value,
                    onPressed: () =>
                    controller.isLoading.value
                        ? null
                        : () => controller.resetPassword(),
                    text: 'تغيير كلمة المرور',
                  )),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required RxBool isVisible,
    required VoidCallback onToggleVisibility,
  }) {
    return
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Obx(() =>
              TextField(
                controller: controller,
                obscureText: !isVisible.value,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: AppColors.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      isVisible.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: onToggleVisibility,
                  ),
                ),
              )),
        ],
      );
  }
}
