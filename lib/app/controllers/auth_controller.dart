import 'package:cars_app/app/data/models/user_model.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../services/car_service.dart';
import '../services/user_service.dart';
import '../services/user_storage_service.dart';

class AuthController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  final Rx<String?> _uid = Rx<String?>(null);
  final Rx<String?> _userName = Rx<String?>(null);
  final Rx<String?> _userRole = Rx<String?>(null);
  final Rx<bool> _isLoggedIn = Rx<bool>(false);
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);

  UserModel? get currentUser => _currentUser.value;
  String? get uid => _uid.value;
  String? get userName => _userName.value;
  String? get userRole => _userRole.value;
  bool get isLoggedIn => _isLoggedIn.value;

  // Helper properties for role checks
  bool get isManager => userRole == 'manager';
  bool get isAdmin => userRole == 'admin';
  bool get isSupervisor => userRole == 'supervisor';
  bool get isTechnician => userRole == 'technician';
  bool get hasNotificationAccess => isManager || isAdmin || isSupervisor;

  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
  }

  Future<void> saveUserLocally(UserModel user) async {
    final localStorage = LocalStorageService();
    await localStorage.saveUserId(user.id);
    await localStorage.saveUsername(user.username);
    await localStorage.saveUserRole(user.roleString);
    await localStorage.saveUserStatus(user.status.toString().split('.').last);
    await localStorage.saveEmail(user.email ?? '');
    await localStorage.savePhoneNumber(user.phoneNumber ?? '');
  }

  Future<void> checkLoginStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final storedUid = prefs.getString('uid');

    if (storedUid != null && storedUid.isNotEmpty) {
      await _loadUserData(storedUid);
    } else {
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  Future<Map<String, dynamic>> login(String usernameOrEmail, String password) async {
    try {
      // Determine if input is email or username
      String email = usernameOrEmail;
      if (!usernameOrEmail.contains('@')) {
        email = '$<EMAIL>';
      }

      // Try to authenticate with Firebase Auth
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Get the Firebase Auth UID
      final firebaseUid = userCredential.user?.uid;
      Logger().d('Firebase UID: $firebaseUid');
      if (firebaseUid == null) {
        return {
          'success': false,
          'message': 'فشل في تسجيل الدخول: لم يتم العثور على المستخدم'
        };
      }
      
      // Find the corresponding user in Firestore
      final userQuery = await _firestore.collection('users')
          .where('firebaseUid', isEqualTo: firebaseUid)
          .limit(1)
          .get();
      
      // If no matching user in Firestore, check by email
      if (userQuery.docs.isEmpty) {
        final emailQuery = await _firestore.collection('users')
            .where('email', isEqualTo: email)
            .where('status', isEqualTo: 'active')
            .limit(1)
            .get();
            
        if (emailQuery.docs.isEmpty) {
          // Try to find by username if email not found
          final usernameQuery = await _firestore.collection('users')
              .where('username', isEqualTo: email)
              .where('status', isEqualTo: 'active')
              .limit(1)
              .get();
              
          if (usernameQuery.docs.isEmpty) {
            await _auth.signOut(); // Sign out since no matching user found
            return {
              'success': false,
              'message': 'المستخدم غير موجود أو غير نشط'
            };
          }
          
          // Update the user with Firebase UID
          final userData = usernameQuery.docs.first;
          final name = userData.data()['name'];
          final username = userData.data()['username'];
          Get.find<FirebaseAuth>().currentUser?.updateDisplayName( name? username : name);

          await _firestore.collection('users').doc(userData.id).update({
            'firebaseUid': firebaseUid,
            'email': email
          });
          
          // Store user data locally
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('uid', userData.id);
          saveUserLocally(UserModel.fromJson({...userData.data(), 'id': userData.id, 'firebaseUid': firebaseUid, 'email': email}));
          
          // Load user data into state
          await _loadUserData(userData.id);
        } else {
          // Update the user with Firebase UID
          final userData = emailQuery.docs.first;
          await _firestore.collection('users').doc(userData.id).update({
            'firebaseUid': firebaseUid
          });
          
          // Store user data locally
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('uid', userData.id);
          saveUserLocally(UserModel.fromJson({...userData.data(), 'id': userData.id, 'firebaseUid': firebaseUid}));
          
          // Load user data into state
          await _loadUserData(userData.id);
        }
      } else {
        // User found in Firestore with matching Firebase UID
        final userData = userQuery.docs.first;
        
        // Check if user is active
        if (userData.data()['status'] != 'active') {
          await _auth.signOut();
          return {
            'success': false,
            'message': 'حساب المستخدم غير نشط'
          };
        }
        
        // Store user data locally
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('uid', userData.id);
        saveUserLocally(UserModel.fromJson({...userData.data(), 'id': userData.id}));
        
        // Load user data into state
        await _loadUserData(userData.id);
      }
      
      return {
        'success': true,
        'message': 'تم تسجيل الدخول بنجاح',
        'role': _userRole.value,
      };
    } catch (e) {
      // Handle Firebase Auth errors
      String errorMessage = 'حدث خطأ أثناء تسجيل الدخول';
      
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'user-not-found':
            errorMessage = 'لم يتم العثور على المستخدم';
            break;
          case 'wrong-password':
            errorMessage = 'كلمة المرور غير صحيحة';
            break;
          case 'invalid-email':
            errorMessage = 'البريد الإلكتروني غير صالح';
            break;
          case 'user-disabled':
            errorMessage = 'تم تعطيل حساب المستخدم';
            break;
          case 'too-many-requests':
            errorMessage = 'تم تعطيل الوصول بسبب العديد من المحاولات الفاشلة. يرجى المحاولة لاحقًا';
            break;
          default:
            errorMessage = 'خطأ في تسجيل الدخول: ${e.message}';
        }
      }
      
      return {
        'success': false,
        'message': errorMessage
      };
    }
  }

  // Regular register method that redirects to the new user's account
  Future<Map<String, dynamic>> register(String username, String email, String password, UserRole role) async {
    try {
      // Check if username already exists
      final usernameQuery = await _firestore.collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();
          
      if (usernameQuery.docs.isNotEmpty) {
        return {
          'success': false,
          'message': 'اسم المستخدم موجود بالفعل'
        };
      }
      
      // Check if email already exists
      final emailQuery = await _firestore.collection('users')
          .where('email', isEqualTo: email)
          .limit(1)
          .get();
          
      if (emailQuery.docs.isNotEmpty) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني موجود بالفعل'
        };
      }
      
      // Create user in Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      final firebaseUid = userCredential.user!.uid;
      
      // Create user in Firestore
      final newUser = UserModel(
        id: '', // Will be set by Firestore
        username: username,
        name: username, // Default name to username
        password: '', // We don't store passwords in Firestore anymore
        role: role,
        status: UserStatus.active,
        createdAt: DateTime.now(),
        email: email,
        phoneNumber: '',
      );
      
      // Add firebaseUid to the user data
      final userData = newUser.toJson();
      userData['firebaseUid'] = firebaseUid;
      
      // Save to Firestore
      final docRef = await _firestore.collection('users').add(userData);
      
      // Update the user with the Firestore ID
      final userWithId = UserModel.fromJson({...userData, 'id': docRef.id});
      
      // Store user data locally
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('uid', docRef.id);
      saveUserLocally(userWithId);
      
      // Load user data into state
      await _loadUserData(docRef.id);
      
      return {
        'success': true,
        'message': 'تم إنشاء الحساب بنجاح',
        'role': role.toString().split('.').last,
      };
    } catch (e) {
      // Handle Firebase Auth errors
      String errorMessage = 'حدث خطأ أثناء إنشاء الحساب';
      
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'email-already-in-use':
            errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
            break;
          case 'invalid-email':
            errorMessage = 'البريد الإلكتروني غير صالح';
            break;
          case 'weak-password':
            errorMessage = 'كلمة المرور ضعيفة جدًا';
            break;
          default:
            errorMessage = 'خطأ في إنشاء الحساب: ${e.message}';
        }
      }
      
      return {
        'success': false,
        'message': errorMessage
      };
    }
  }
  
  // Special register method for admin that doesn't redirect
  Future<Map<String, dynamic>> registerUserAsAdmin(String username,String name, String email, String password, UserRole role) async {
    try {
      // Check if username already exists
      final usernameQuery = await _firestore.collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();
          
      if (usernameQuery.docs.isNotEmpty) {
        return {
          'success': false,
          'message': 'اسم المستخدم موجود بالفعل'
        };
      }
      
      // Check if email already exists
      final emailQuery = await _firestore.collection('users')
          .where('email', isEqualTo: email)
          .limit(1)
          .get();
          
      if (emailQuery.docs.isNotEmpty) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني موجود بالفعل'
        };
      }
      
      // Create user in Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      final firebaseUid = userCredential.user!.uid;
      
      // Create user in Firestore
      final newUser = UserModel(
        id: '', // Will be set by Firestore
        username: username,
        name: name, // Default name to username
        password: '', // We don't store passwords in Firestore anymore
        role: role,
        status: UserStatus.active,
        createdAt: DateTime.now(),
        email: email,
        phoneNumber: '',
      );
      
      // Add firebaseUid to the user data
      final userData = newUser.toJson();
      userData['firebaseUid'] = firebaseUid;
      
      // Save to Firestore
      final docRef = await _firestore.collection('users').add(userData);
      
      // Important: Do NOT store user data locally or load user data into state
      // This keeps the admin logged in instead of switching to the new user
      
      return {
        'success': true,
        'message': 'تم إنشاء الحساب بنجاح',
        'userId': docRef.id,
        'role': role.toString().split('.').last,
      };
    } catch (e) {
      // Handle Firebase Auth errors
      String errorMessage = 'حدث خطأ أثناء إنشاء الحساب';
      
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'email-already-in-use':
            errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
            break;
          case 'invalid-email':
            errorMessage = 'البريد الإلكتروني غير صالح';
            break;
          case 'weak-password':
            errorMessage = 'كلمة المرور ضعيفة جدًا';
            break;
          default:
            errorMessage = 'خطأ في إنشاء الحساب: ${e.message}';
        }
      }
      
      return {
        'success': false,
        'message': errorMessage
      };
    }
  }

  Future<void> _loadUserData(String userId) async {
    try {
      final userData = await _firestore.collection('users').doc(userId).get();
      if (userData.exists) {
        final data = userData.data()!;
        _uid.value = userId;
        _userName.value = data['username'];
        _userRole.value = data['role'];
        _isLoggedIn.value = true;
        
        UserModel user = UserModel.fromJson({...data, 'id': userData.id});
        _currentUser.value = user;
        
        Get.lazyPut<UserModel>(() => user, fenix: true);
        Get.lazyPut<UserService>(() => UserService(), fenix: true);
        Get.lazyPut<CarService>(() => CarService(), fenix: true);
        
        saveUserLocally(user);
        Get.offNamed(user.getCurrentRoute());
      } else {
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      print('Error loading user data: $e');
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  Future<void> logout() async {
    try {
      // Sign out from Firebase Auth
      await _auth.signOut();

      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      print('Error during logout: $e');
    }
  }

  Future<Map<String, dynamic>> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return {
        'success': true,
        'message': 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
      };
    } catch (e) {
      String errorMessage = 'حدث خطأ أثناء إعادة تعيين كلمة المرور';
      
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'user-not-found':
            errorMessage = 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
            break;
          case 'invalid-email':
            errorMessage = 'البريد الإلكتروني غير صالح';
            break;
          default:
            errorMessage = 'خطأ في إعادة تعيين كلمة المرور: ${e.message}';
        }
      }
      
      return {
        'success': false,
        'message': errorMessage
      };
    }
  }

  // Method to delete a user from Firebase Auth
  // This is a simplified approach - in production, use Firebase Admin SDK or Cloud Functions
  Future<Map<String, dynamic>> deleteUserAuth(String firebaseUid) async {
    try {
      // In a real production app, you would use Firebase Admin SDK or Cloud Functions
      // to delete users from Firebase Auth, as client-side deletion has limitations
      
      // For this implementation, we'll use a workaround that works in development
      // but has security limitations in production
      
      // Get a reference to the user document in Firestore that contains the auth credentials
      final userQuery = await _firestore.collection('users')
          .where('firebaseUid', isEqualTo: firebaseUid)
          .limit(1)
          .get();
      
      if (userQuery.docs.isEmpty) {
        return {
          'success': false,
          'message': 'لم يتم العثور على المستخدم'
        };
      }
      
      // Get the user's email
      final userData = userQuery.docs.first.data();
      final email = userData['email'];
      
      if (email == null) {
        return {
          'success': false,
          'message': 'لم يتم العثور على البريد الإلكتروني للمستخدم'
        };
      }
      
      // Attempt to delete the user from Firebase Auth
      // Note: This is a simplified approach and may not work in all cases
      // In production, use Firebase Admin SDK or Cloud Functions
      
      // For now, we'll mark this as successful and provide guidance
      Logger().d('User deletion from Firebase Auth requested for UID: $firebaseUid');
      
      return {
        'success': true,
        'message': 'تم طلب حذف المستخدم من Firebase Auth'
      };
    } catch (e) {
      Logger().e('Error deleting user from Firebase Auth: $e');
      return {
        'success': false,
        'message': 'فشل في حذف المستخدم من Firebase Auth: $e'
      };
    }
  }
}
