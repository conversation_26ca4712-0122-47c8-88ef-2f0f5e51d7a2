import 'package:cars_app/app/modules/logastic_support/controller/logistics_controller.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:cars_app/app/widgets/supervistor_car_card.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/notification_icon_widget.dart';
import '../../../widgets/sector_car_card.dart';
import '../../../data/models/car_model.dart';

class LogisticsView extends GetView<LogisticsController> {
  const LogisticsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title:Text(
          'الدعم اللوجستي',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions:  [
        IconButton(onPressed: () {
          Get.toNamed(Routes.PROFILE);
                      }, icon:Icon( Icons.person,color: Colors.white,))
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }


        return StreamBuilder<List<Car>>(
          stream: controller.getCarsStream(),
          builder: (context, snapshot) {
            final cars = snapshot.data ?? [];
            if (cars.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.directions_car_outlined,
                      size: 64,
                      color: AppColors.primary.withOpacity(0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد سيارات في هذا القطاع',
                      style: TextStyle(
                        fontSize: 18,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            return   Column(
              children: [
                // Sector Info Header
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildInfoColumn(
                                  'عدد السيارات',
                                  '${cars.length}',
                                  Icons.directions_car,
                                ),
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.white.withOpacity(0.2),
                                ),
                                _buildInfoColumn(
                                  'أرسال مجموعة',
                                  '${cars.where((car) => car.status == CarStatus.sendGroup).length}',
                                  Icons.group,
                                ),
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.white.withOpacity(0.2),
                                ),
                                _buildInfoColumn(
                                  'ارسال للدعم اللوجستي',
                                  '${cars.where((car) => car.status == CarStatus.sendToLogisticsSupport).length}',
                                  Icons.receipt,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Status Filter
                // const Expanded(
                //   flex: 1,
                //   child: SmallFilterWidget(),
                // ),
                // Cars List
                Expanded(
                  flex: 8,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    child:  ListView.builder(
                      itemCount: cars.length,
                      padding: const EdgeInsets.only(top: 8, bottom: 16),
                      itemBuilder: (context, index) {
                        final car = cars[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: SupervistorCarCard(currentAction: controller.getCurrentActions(car.id!, car),car: car, uid: '',),
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          },

        );
      }),
    );
  }

  Widget _buildInfoColumn(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
