import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';
import '../controllers/car_report_controller.dart';
import '../widgets/car_details_card.dart';
import '../widgets/car_search_box.dart';
import '../widgets/status_distribution_chart.dart';
import '../widgets/workshop_entry_card.dart';

class CarReportView extends GetView<CarReportController> {
  const CarReportView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'تقرير المركبة'.tr,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.loadCars(),
            tooltip: 'تحديث البيانات'.tr,
          ),
          Obx(() => controller.selectedCar.value != null
              ? _buildExportMenu()
              : const SizedBox.shrink()),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // Search box
            CarSearchBox(controller: controller),

            // Main content
            Expanded(
              child: controller.selectedCar.value == null
                  ? _buildCarList()
                  : _buildCarDetails(),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildCarList() {
    return Obx(() {
      if (controller.filteredCars.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                controller.searchQuery.value.isEmpty
                    ? 'لا توجد مركبات'.tr
                    : 'لا توجد نتائج للبحث'.tr,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.filteredCars.length,
        itemBuilder: (context, index) {
          final car = controller.filteredCars[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () => controller.selectCar(car),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.directions_car,
                        color: AppColors.primary,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${car.plateNumber}-${car.plateCharacters}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            car.carModel,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          car.sectorName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getStatusColor(car.status),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            car.status.toString().split('.').last.tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildCarDetails() {
    final car = controller.selectedCar.value!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          InkWell(
            onTap: () => controller.clearSelection(),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  const Icon(Icons.arrow_back_ios, size: 16),
                  const SizedBox(width: 8),
                  Text('العودة إلى قائمة المركبات'.tr),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Car details card
          CarDetailsCard(car: car),
          const SizedBox(height: 24),

          // Status distribution chart
          if (controller.statusDistribution.isNotEmpty) ...[
            Text(
              'توزيع الحالات'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            StatusDistributionChart(
              statusDistribution: controller.statusDistribution.toList(),
            ),
            const SizedBox(height: 24),
          ],

          // Workshop entries
          Text(
            'سجلات الورشة'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (controller.workshopEntries.isEmpty)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.build_circle_outlined,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد سجلات ورشة لهذه المركبة'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.workshopEntries.length,
              itemBuilder: (context, index) {
                final entry = controller.workshopEntries[index];
                return WorkshopEntryCard(
                  entry: entry,
                  index: index + 1,
                );
              },
            ),
        ],
      ),
    );
  }

  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return AppColors.success;
      case CarStatus.maintenance:
      case CarStatus.inWorkshop:
        return AppColors.warning;
      case CarStatus.rejected:
      case CarStatus.destroyed:
      case CarStatus.outOfService:
        return AppColors.error;
      case CarStatus.pending:
        return AppColors.info;
      default:
        return AppColors.accent;
    }
  }

  Widget _buildExportMenu() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.download),
      tooltip: 'تصدير التقرير'.tr,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'pdf',
          child: Row(
            children: [
              Icon(Icons.picture_as_pdf, color: Colors.red.shade700),
              const SizedBox(width: 8),
              Text('تصدير كملف PDF'.tr),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'excel',
          child: Row(
            children: [
              Icon(Icons.table_view, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text('تصدير كملف Excel'.tr),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'pdf':
            controller.exportToPDF();
            break;
          case 'excel':
            controller.exportToExcel();
            break;
        }
      },
    );
  }
}
