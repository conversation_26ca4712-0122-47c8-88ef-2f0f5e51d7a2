import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../modules/car/controller/car_controller.dart';
import '../modules/sector/controller/sector_controller.dart';
import '../data/models/car_model.dart';
import '../data/models/sector_model.dart';

class AddCarForm extends StatefulWidget {
  const AddCarForm({Key? key}) : super(key: key);

  @override
  State<AddCarForm> createState() => _AddCarFormState();
}

class _AddCarFormState extends State<AddCarForm> {
  final CarController carController = Get.find();
  final SectorController sectorController = Get.find();
  final _formKey = GlobalKey<FormState>();
  
  final plateNumberController = TextEditingController();
  final plateCharactersController = TextEditingController();
  final encodingController = TextEditingController();
  final carTypeController = TextEditingController();
  int selectedYear = DateTime.now().year;
  Sector? selectedSector;
  bool isInternal = true;

  @override
  void initState() {
    super.initState();
    sectorController.loadSectors();
  }

  Future<void> _selectYear(BuildContext context) async {
    final int? picked = await showDialog<int>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Year'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: YearPicker(
              firstDate: DateTime(1990),
              lastDate: DateTime.now(),
              selectedDate: DateTime(selectedYear),
              onChanged: (DateTime dateTime) {
                setState(() {
                  selectedYear = dateTime.year;
                });
                Navigator.pop(context, dateTime.year);
              },
            ),
          ),
        );
      },
    );

    if (picked != null) {
      setState(() {
        selectedYear = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New Car'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Car Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                controller: plateCharactersController,
                                decoration: const InputDecoration(
                                  labelText: 'Plate Characters',
                                  border: OutlineInputBorder(),
                                  hintText: 'ABC',
                                ),
                                inputFormatters: [
                                  // FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z]')),
                                  LengthLimitingTextInputFormatter(3),
                                ],
                                textCapitalization: TextCapitalization.characters,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter plate characters';
                                  }
                                  if (value.length != 3) {
                                    return 'Must be 3 characters';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                controller: plateNumberController,
                                decoration: const InputDecoration(
                                  labelText: 'Plate Numbers',
                                  border: OutlineInputBorder(),
                                  hintText: '1233',
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(4),
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter plate numbers';
                                  }
                                  if (value.length != 4) {
                                    return 'Must be 3 numbers';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Additional Details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: carTypeController,
                          decoration: const InputDecoration(
                            labelText: 'Car Type',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.directions_car),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter car type';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        InkWell(
                          onTap: () => _selectYear(context),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'Model Year',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            child: Text(selectedYear.toString()),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Obx(() => DropdownButtonFormField<Sector>(
                          value: selectedSector,
                          decoration: const InputDecoration(
                            labelText: 'Sector',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.location_city),
                          ),
                          items: [
                            const DropdownMenuItem<Sector>(
                              value: null,
                              child: Text('Select Sector'),
                            ),
                            ...sectorController.sectors.map((sector) => 
                              DropdownMenuItem<Sector>(
                                value: sector,
                                child: Text(sector.name),
                              ),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedSector = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Please select a sector';
                            }
                            return null;
                          },
                        )),
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('Internal Car'),
                          subtitle: Text(
                            isInternal ? 'This car belongs to the organization' : 'This is an external car',
                            style: TextStyle(
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                          value: isInternal,
                          onChanged: (bool value) {
                            setState(() {
                              isInternal = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _submitForm,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Car'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      try {
        final encoding = '${plateCharactersController.text}-${plateNumberController.text}';
        await carController.createCar(
          Car(
              id: Uuid().v4(),
              sectorId: selectedSector!.id,
              tempSectorId: '',
              plateNumber: plateNumberController.text,
              plateCharacters: plateCharactersController.text,
              encoding: encoding,
              carModel: selectedYear.toString(),
              sectorName: selectedSector!.name,
              tempSectorName: '',
              carType: carTypeController.text,
              rotations: [],
              isInternal: isInternal,
              createAt: DateTime.now(),
              status: CarStatus.active,
              workshopHistory: [])
        );
        Get.back();
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to create car: $e',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  @override
  void dispose() {
    plateNumberController.dispose();
    plateCharactersController.dispose();
    encodingController.dispose();
    carTypeController.dispose();
    super.dispose();
  }
}

class UpdateCarForm extends StatefulWidget {
  final Car car;

  const UpdateCarForm({Key? key, required this.car}) : super(key: key);

  @override
  State<UpdateCarForm> createState() => _UpdateCarFormState();
}

class _UpdateCarFormState extends State<UpdateCarForm> {
  final CarController carController = Get.find();
  final _formKey = GlobalKey<FormState>();
  
  late final TextEditingController plateNumberController;
  late final TextEditingController plateCharactersController;
  late final TextEditingController encodingController;
  late final TextEditingController carModelController;
  late final TextEditingController carTypeController;
  late bool isInternal;

  @override
  void initState() {
    super.initState();
    plateNumberController = TextEditingController(text: widget.car.plateNumber);
    plateCharactersController = TextEditingController(text: widget.car.plateCharacters);
    encodingController = TextEditingController(text: widget.car.encoding);
    carModelController = TextEditingController(text: widget.car.carModel);
    carTypeController = TextEditingController(text: widget.car.carType);
    isInternal = widget.car.isInternal;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Car'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: plateNumberController,
                decoration: const InputDecoration(
                  labelText: 'Plate Number',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter plate number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: plateCharactersController,
                decoration: const InputDecoration(
                  labelText: 'Plate Characters',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter plate characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: encodingController,
                decoration: const InputDecoration(
                  labelText: 'Encoding',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter encoding';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: carModelController,
                decoration: const InputDecoration(
                  labelText: 'Car Model',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter car model';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: carTypeController,
                decoration: const InputDecoration(
                  labelText: 'Car Type',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter car type';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Internal Car'),
                value: isInternal,
                onChanged: (bool value) {
                  setState(() {
                    isInternal = value;
                  });
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _submitForm,
                child: const Text('Update Car'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      carController.updateCar(
        widget.car
        // widget.car.id,
        // sectorId: widget.car.sectorId,
        // plateNumber: plateNumberController.text,
        // plateCharacters: plateCharactersController.text,
        // encoding: encodingController.text,
        // carModel: carModelController.text,
        // carType: carTypeController.text,
        // isInternal: isInternal,
      );
      Get.back();
    }
  }

  @override
  void dispose() {
    plateNumberController.dispose();
    plateCharactersController.dispose();
    encodingController.dispose();
    carModelController.dispose();
    carTypeController.dispose();
    super.dispose();
  }
}
