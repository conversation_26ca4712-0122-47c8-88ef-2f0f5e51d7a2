import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final EdgeInsets padding;
  final bool animate;
  final Duration animationDuration;
  final Curve animationCurve;

  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.spacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.animate = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of columns based on available width
        int crossAxisCount = _calculateCrossAxisCount(constraints.maxWidth);

        return Padding(
          padding: padding,
          child: MasonryGridView.count(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: spacing,
            crossAxisSpacing: spacing,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: children.length,
            itemBuilder: (context, index) {
              if (animate) {
                return AnimatedBuilder(
                  animation: CurvedAnimation(
                    parent: ModalRoute.of(context)?.animation ?? 
                        const AlwaysStoppedAnimation(1.0),
                    curve: Interval(
                      0.1 * (index / children.length),
                      1.0,
                      curve: animationCurve,
                    ),
                  ),
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: Tween<double>(
                        begin: 0,
                        end: 1,
                      ).animate(CurvedAnimation(
                        parent: ModalRoute.of(context)?.animation ?? 
                            const AlwaysStoppedAnimation(1.0),
                        curve: Interval(
                          0.1 * (index / children.length),
                          1.0,
                          curve: animationCurve,
                        ),
                      )),
                      child: SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(0, 0.2),
                          end: Offset.zero,
                        ).animate(CurvedAnimation(
                          parent: ModalRoute.of(context)?.animation ?? 
                              const AlwaysStoppedAnimation(1.0),
                          curve: Interval(
                            0.1 * (index / children.length),
                            1.0,
                            curve: animationCurve,
                          ),
                        )),
                        child: child,
                      ),
                    );
                  },
                  child: children[index],
                );
              } else {
                return children[index];
              }
            },
          ),
        );
      },
    );
  }

  int _calculateCrossAxisCount(double width) {
    if (width < 600) {
      return 1; // Mobile
    } else if (width < 900) {
      return 2; // Tablet
    } else if (width < 1200) {
      return 3; // Small desktop
    } else {
      return 4; // Large desktop
    }
  }
}
