import 'package:cars_app/app/services/user_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../data/models/notification_model.dart';

class NotificationService extends GetxService {
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final RxString fcmToken = ''.obs;
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxInt unreadCount = 0.obs;
  final RxBool isLoading = false.obs;

  // FCM HTTP v1 API endpoint
  static const String _fcmUrl = 'https://fcm.googleapis.com/v1/projects/seen-jeem-36b83/messages:send';

  // Your Firebase project credentials
  static const String _projectId = 'seen-jeem-36b83'; // Get from Firebase Console
  static const String _clientEmail = '<EMAIL>'; // Get from service account JSON
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  Future<NotificationService> init() async {
    try {
      // Request permission for notifications
      await _fcm.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        announcement: true,
        carPlay: true,
        criticalAlert: true,
        provisional: true,
        providesAppNotificationSettings: true,
      );

      // Initialize local notifications
      try {
        const initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');
        const initializationSettingsIOS = DarwinInitializationSettings();
        const initializationSettings = InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );
        await _localNotifications.initialize(initializationSettings);
      } catch (e) {
        print('Error initializing local notifications: $e');
        // Continue without local notifications
      }

      // Get FCM token
      fcmToken.value = await _fcm.getToken() ?? '';
      print('FCM Token: ${fcmToken.value}');
      updateUserFcmToken(fcmToken.value);

      // Handle token refresh
      _fcm.onTokenRefresh.listen((newToken) {
        fcmToken.value = newToken;
        // Update token in your backend
        updateUserFcmToken(newToken);
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      return this;
    } catch (e) {
      print('Error initializing notification service: $e');
      // Return the service even if initialization fails
      return this;
    }
  }

  Future<String> _getAccessToken() async {
    try {
      final credentials = ServiceAccountCredentials.fromJson({
        "type": "service_account",
        "project_id": _projectId,
        "private_key_id": "your_private_key_id",
        "private_key": _privateKey,
        "client_email": _clientEmail,
        "client_id": "your_client_id",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "your_cert_url"
      });

      final client = await clientViaServiceAccount(
        credentials,
        ['https://www.googleapis.com/auth/firebase.messaging'],
      );

      return client.credentials.accessToken.data;
    } catch (e) {
      print('Error getting access token: $e');
      rethrow;
    }
  }

  Future<void> sendNotificationToTokens({
    required List<String> tokens,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final accessToken = await _getAccessToken();

      for (final token in tokens) {
        final response = await http.post(
          Uri.parse(_fcmUrl),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $accessToken',
          },
          body: jsonEncode({
            'message': {
              'token': token,
              'notification': {
                'title': title,
                'body': body,
              },
              'data': data ?? {},
              'android': {
                'priority': 'high',
                'notification': {
                  'channel_id': 'cars_app_channel',
                },
              },
              'apns': {
                'payload': {
                  'aps': {
                    'sound': 'default',
                    'badge': 1,
                  },
                },
              },
            },
          }),
        );

        if (response.statusCode == 200) {
          print('Successfully sent notification to token: $token');
        } else {
          print('Failed to send notification. Status: ${response.statusCode}');
          print('Response: ${response.body}');
        }
      }
    } catch (e) {
      print('Error sending notification: $e');
    }
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Handling foreground message: ${message.messageId}');

    try {
      // Show local notification
      await _showLocalNotification(message);
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'cars_app_channel',
        'Cars App Notifications',
        channelDescription: 'Notifications for car status updates',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails();

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        message.notification?.title,
        message.notification?.body,
        details,
      );
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  Future<void> updateUserFcmToken(String token) async {
    try {
      final userid =await LocalStorageService().getUserId();
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userid)
          .update({'fcmToken': token});
    } catch (e) {
      print('Error updating FCM token: $e');
    }
  }

  Stream<List<NotificationModel>> getNotificationsForUser(String userId) {
    return _firestore
        .collection('notifications')
        .where('userIds', arrayContains: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    });
  }

  Future<void> markAsRead(String notificationId, String userId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'readBy.$userId': true,
      });
    } catch (e) {
      print('Error marking notification as read: $e');
      rethrow;
    }
  }

  Future<void> markAllAsRead(String userId) async {
    try {
      final batch = _firestore.batch();
      final notifications = await _firestore
          .collection('notifications')
          .where('userIds', arrayContains: userId)
          .get();

      for (var doc in notifications.docs) {
        batch.update(doc.reference, {
          'readBy.$userId': true,
        });
      }

      await batch.commit();
    } catch (e) {
      print('Error marking all notifications as read: $e');
      rethrow;
    }
  }

  Future<void> createNotification({
    required String title,
    required String content,
    required NotificationType type,
    required List<String> userIds,
  }) async {
    try {
      await _firestore.collection('notifications').add({
        'title': title,
        'content': content,
        'type': type.toString(),
        'createdAt': FieldValue.serverTimestamp(),
        'userIds': userIds,
        'readBy': {},
      });
    } catch (e) {
      print('Error creating notification: $e');
      rethrow;
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      print('Error deleting notification: $e');
      rethrow;
    }
  }

  void updateUnreadCount(List<NotificationModel> notifications, String userId) {
    unreadCount.value = notifications
        .where((notification) => !(notification.readBy[userId] ?? false))
        .length;
  }
  
  // Get unread notifications count by role
  Stream<int> getUnreadNotificationCountByRole(String role) {
    return _firestore
        .collection('notifications')
        .where('role', isEqualTo: role)
        .snapshots()
        .map((snapshot) {
          final notifications = snapshot.docs
              .map((doc) => NotificationModel.fromFirestore(doc))
              .toList();
          
          // Count notifications that haven't been read by anyone
          return notifications
              .where((notification) => notification.readBy.isEmpty)
              .length;
        });
  }
  
  // Mark all notifications for a role as read
  Future<void> markAllAsReadForRole(String role) async {
    try {
      final batch = _firestore.batch();
      final notifications = await _firestore
          .collection('notifications')
          .where('role', isEqualTo: role)
          .get();

      // Get all users with this role
      final userIds = await _getUserIdsByRole(role);
      
      for (var doc in notifications.docs) {
        // Create a map of userId: true for all users with this role
        final readByUpdates = Map.fromEntries(
          userIds.map((userId) => MapEntry('readBy.$userId', true))
        );
        
        batch.update(doc.reference, readByUpdates);
      }

      await batch.commit();
    } catch (e) {
      print('Error marking all notifications as read for role: $e');
      rethrow;
    }
  }
  
  // Helper method to get user IDs by role
  Future<List<String>> _getUserIdsByRole(String role) async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: role)
          .get();
          
      return usersSnapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      print('Error getting users by role: $e');
      return [];
    }
  }
}

// This needs to be a top-level function
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
  // Handle background message
} 