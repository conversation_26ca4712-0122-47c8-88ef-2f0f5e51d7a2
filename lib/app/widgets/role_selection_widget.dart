import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../data/models/notification_model.dart';
import '../data/models/sector_model.dart';
import '../routes/app_pages.dart';
import '../services/notification_service_selector.dart';
import '../services/sector_service.dart';
import '../themes/app_colors.dart';

class RoleSelectionWidget extends StatefulWidget {
  const RoleSelectionWidget({Key? key}) : super(key: key);

  @override
  State<RoleSelectionWidget> createState() => _RoleSelectionWidgetState();
}

class _RoleSelectionWidgetState extends State<RoleSelectionWidget> {
  String? selectedRole;
  List<Sector> sectorsList = [];
  bool isLoadingSectors = true;
  String? errorMessage;
  String? selectedSector;
  final notificationService = Get.find<NotificationService>();

  @override
  void initState() {
    super.initState();
    _loadSectors();
  }

  Future<void> _loadSectors() async {
    try {
      final sectorService = Get.find<SectorService>();
      final sectors = await sectorService.getSectors();
      setState(() {
        sectorsList = sectors;
        isLoadingSectors = false;
      });
    } catch (e) {
      setState(() {
        isLoadingSectors = false;
        errorMessage = e.toString();
      });
    }
  }

  void _selectRole(String role) {

    Logger().d(role);
    // Mark notifications as read immediately for these roles
    if (role == 'supervisor' || role == 'logistic_support' || role == 'technician') {
      notificationService.markAllAsReadForRole(role);
    }
    
    setState(() {
      selectedRole = role;
    });
  }

  void _selectSector(String sectorId) {
    setState(() {
      selectedSector = sectorId;
      notificationService.markAllAsReadForRole('sector_${sectorId}');

      Logger().d('sector_${sectorId}');
      selectedRole = null; // Clear role selection when sector is selected
    });
  }

  void _confirmSelection() {

    // For sectors, only mark notifications as read when confirmed
    // if (selectedSector != null) {
    //   notificationService.markAllAsReadForRole('sector_${selectedSector!}');
    // }
    
    Get.back(); // Close dialog first
    
    if (selectedRole != null) {
      // Navigate based on role
      if (selectedRole == 'technician') {
        Get.toNamed(Routes.TECHNICIAN, arguments: {'isAdmin': true});
        Get.snackbar(
          '\u062a\u0645',
          '\u062a\u0645 \u0627\u0644\u0627\u0646\u062a\u0642\u0627\u0644 \u0625\u0644\u0649 \u0635\u0641\u062d\u0629 \u0627\u0644\u0641\u0646\u064a',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else if (selectedRole == 'logistic_support') {
        Get.toNamed(Routes.LOGASTIC_SUPPORT);
        Get.snackbar(
          '\u062a\u0645',
          '\u062a\u0645 \u0627\u0644\u0627\u0646\u062a\u0642\u0627\u0644 \u0625\u0644\u0649 \u0635\u0641\u062d\u0629 \u0627\u0644\u062f\u0639\u0645 \u0627\u0644\u0644\u0648\u062c\u0633\u062a\u064a',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else if (selectedRole == 'supervisor') {
        Get.toNamed(Routes.SUPERVISOR);
        Get.snackbar(
          '\u062a\u0645',
          '\u062a\u0645 \u0627\u0644\u0627\u0646\u062a\u0642\u0627\u0644 \u0625\u0644\u0649 \u0635\u0641\u062d\u0629 \u0627\u0644\u0645\u0634\u0631\u0641',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } else if (selectedSector != null) {
      // Navigate to specific sector
      Logger().d(selectedSector);
      Get.toNamed(
        Routes.SECTOR_MANAGER,
        arguments: {"sectorId": selectedSector},
      );
      
      // Find the sector name for the snackbar message
      String sectorName = '';
      for (var sector in sectorsList) {
        if (sector.id == selectedSector) {
          sectorName = sector.name;
          break;
        }
      }
      
      Get.snackbar(
        '\u062a\u0645',
        '\u062a\u0645 \u0627\u0644\u0627\u0646\u062a\u0642\u0627\u0644 \u0625\u0644\u0649 \u0642\u0637\u0627\u0639 $sectorName',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Role selection cards
          Wrap(
            spacing: 10,
            runSpacing: 10,
            alignment: WrapAlignment.center,
            children: [
              _buildRoleCard(
                'technician',
                '\u0641\u0646\u064a',
                Icons.build_rounded,
                Colors.blue,
              ),
              _buildRoleCard(
                'logistic_support',
                '\u062f\u0639\u0645 \u0644\u0648\u062c\u0633\u062a\u064a',
                Icons.local_shipping_rounded,
                Colors.orange,
              ),
              _buildRoleCard(
                'supervisor',
                '\u0645\u0634\u0631\u0641',
                Icons.supervisor_account_rounded,
                Colors.green,
              ),
            ],
          ),
          
          // Divider between roles and sectors
          const SizedBox(height: 20),
          const Divider(),
          const SizedBox(height: 10),
          const Text(
            '\u0627\u0644\u0642\u0637\u0627\u0639\u0627\u062a',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          
          // Sectors list
          if (isLoadingSectors) ...[
            // Show loading indicator while fetching sectors
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
          ] else if (errorMessage != null) ...[
            // Show error message if sectors failed to load
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 48,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '\u0641\u0634\u0644 \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0642\u0637\u0627\u0639\u0627\u062a',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _loadSectors,
                      child: const Text('\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629'),
                    ),
                  ],
                ),
              ),
            ),
          ] else if (sectorsList.isEmpty) ...[
            // Show message if no sectors found
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('\u0644\u0627 \u062a\u0648\u062c\u062f \u0642\u0637\u0627\u0639\u0627\u062a \u0645\u062a\u0627\u062d\u0629'),
              ),
            ),
          ] else ...[
            // Show sector cards in the same style as role cards
            Wrap(
              spacing: 10,
              runSpacing: 10,
              alignment: WrapAlignment.center,
              children: sectorsList.map((sector) {
                final isSelected = selectedSector == sector.id;
                return _buildSectorCard(sector, isSelected);
              }).toList(),
            ),
          ],
          
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('\u0625\u0644\u063a\u0627\u0621'),
              ),
              const SizedBox(width: 10),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                ),
                onPressed: _confirmSelection,
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoleCard(
    String roleValue,
    String roleTitle,
    IconData icon,
    Color color,
  ) {
    final isSelected = selectedRole == roleValue;
    final notificationService = Get.find<NotificationService>();
    
    return InkWell(
      onTap: () => _selectRole(roleValue),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 130,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isSelected ? color : Colors.grey,
                  size: 36,
                ),
                const SizedBox(height: 10),
                Text(
                  roleTitle,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? color : Colors.black87,
                  ),
                ),
              ],
            ),
            
            // Notification badge using StreamBuilder
            StreamBuilder<int>(
              stream: notificationService.getUnreadNotificationCountByRole(roleValue),
              builder: (context, snapshot) {
                // Show badge only if we have data and count > 0
                final notificationCount = snapshot.data ?? 0;
                if (notificationCount > 0) {
                  return Positioned(
                    top: 0,
                    right: 0,
                    child: GestureDetector(
                      // Mark all as read when badge is tapped for supervisor, logistics, and technician
                      onTap: () {
                        Logger().d(roleValue);
                        if (roleValue == 'supervisor' || roleValue == 'logistic_support' || roleValue == 'technician') {
                          notificationService.markAllAsReadForRole(roleValue);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 20,
                          minHeight: 20,
                        ),
                        child: Text(
                          '$notificationCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  );
                } else {
                  return const SizedBox.shrink(); // No badge if count is 0
                }
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSectorCard(Sector sector, bool isSelected) {
    final color = Colors.purple; // Use a consistent color for sectors
    final notificationService = Get.find<NotificationService>();
    
    return InkWell(
      onTap: () => _selectSector(sector.id),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 130, // Same width as role cards
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 10), // Same padding as role cards
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12), // Same border radius as role cards
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.location_city,
                  color: isSelected ? color : Colors.grey,
                  size: 36, // Same size as role cards
                ),
                const SizedBox(height: 10), // Same spacing as role cards
                Text(
                  sector.name,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? color : Colors.black87,
                    // Same text style as role cards
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            
            // Notification badge using StreamBuilder
            StreamBuilder<int>(
              stream: notificationService.getUnreadNotificationCountByRole('sector_${sector.id}'),
              builder: (context, snapshot) {
                // Show badge only if we have data and count > 0
                final notificationCount = snapshot.data ?? 0;
                if (notificationCount > 0) {
                  return Positioned(
                    top: 0,
                    right: 0,
                    child: GestureDetector(
                      // Mark all as read when badge is tapped
                      onTap: () {
                        notificationService.markAllAsReadForRole('sector_${sector.id}');
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 20,
                          minHeight: 20,
                        ),
                        child: Text(
                          '$notificationCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  );
                } else {
                  return const SizedBox.shrink(); // No badge if count is 0
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
