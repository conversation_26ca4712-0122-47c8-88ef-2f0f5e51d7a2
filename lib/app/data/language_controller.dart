import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class LanguageController extends GetxController {
  final _box = GetStorage();
  final _key = 'isArabic';

  bool get isArabic => _box.read(_key) ?? false;

  ThemeData get getTheme => isArabic
      ? ThemeData(textTheme: const TextTheme(bodyMedium: TextStyle(fontFamily: 'Cairo')))
      : ThemeData(
      textTheme: const TextTheme(
      bodyMedium: TextStyle(fontFamily: 'Roboto')));

  void changeLanguage() {
    _box.write(_key, !isArabic);
    if (!isArabic) {
      Get.updateLocale(const Locale('ar', 'SA'));
    } else {
      Get.updateLocale(const Locale('en', 'US'));
    }
    update();
  }

  void loadSelectedLanguage() {
    if (isArabic) {
      Get.updateLocale(const Locale('ar', 'SA'));
    } else {
      Get.updateLocale(const Locale('en', 'US'));
    }
    update();
  }
}
