import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';

class CarDetailsCard extends StatelessWidget {
  final Car car;

  const CarDetailsCard({
    Key? key,
    required this.car,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [
              Color(0xFF4158D0),
              Color(0xFFC850C0),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.directions_car,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${car.plateNumber}-${car.plateCharacters}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        car.carModel,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(car.status),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    car.status.toString().split('.').last.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildInfoRow(
              Icons.business,
              'القطاع'.tr,
              car.sectorName,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.category,
              'النوع'.tr,
              car.carType,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الإنشاء'.tr,
              DateFormat('yyyy-MM-dd').format(car.createAt),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.build,
              'عدد سجلات الورشة'.tr,
              car.workshopHistory.length.toString(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.7),
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return Colors.green.shade600;
      case CarStatus.maintenance:
      case CarStatus.inWorkshop:
        return Colors.orange.shade600;
      case CarStatus.rejected:
      case CarStatus.destroyed:
      case CarStatus.outOfService:
        return Colors.red.shade600;
      case CarStatus.pending:
        return Colors.blue.shade600;
      default:
        return Colors.purple.shade600;
    }
  }
}
