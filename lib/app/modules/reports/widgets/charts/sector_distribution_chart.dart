import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';

class SectorDistributionChart extends StatelessWidget {
  final ReportsController controller;

  const SectorDistributionChart({Key? key, required this.controller})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.purple.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.location_city, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'توزيع حسب القطاع'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildChartTypeToggle(),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => SizedBox(
                  height: 300,
                  child: controller.sectorChartType.value == SectorChartType.pie
                      ? _buildPieChart()
                      : _buildDoughnutChart(),
                )),
            const SizedBox(height: 8),
            Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Obx(() {
                  final totalSectors = controller.sectorDistribution.length;

                  return Text(
                    'عدد القطاعات: $totalSectors',
                    style: const TextStyle(
                      color: Colors.purple,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeToggle() {
    return Obx(() => SegmentedButton<SectorChartType>(
          segments: const [
            ButtonSegment<SectorChartType>(
              value: SectorChartType.pie,
              icon: Icon(Icons.pie_chart),
              label: Text('دائري'),
            ),
            ButtonSegment<SectorChartType>(
              value: SectorChartType.doughnut,
              icon: Icon(Icons.donut_large),
              label: Text('حلقي'),
            ),
          ],
          selected: {controller.sectorChartType.value},
          onSelectionChanged: (Set<SectorChartType> newSelection) {
            controller.sectorChartType.value = newSelection.first;
          },
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return Colors.purple;
                }
                return Colors.white;
              },
            ),
            foregroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return Colors.white;
                }
                return Colors.purple;
              },
            ),
          ),
        ));
  }

  Widget _buildPieChart() {
    return SfCircularChart(
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        overflowMode: LegendItemOverflowMode.wrap,
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CircularSeries>[
        PieSeries<SectorCount, String>(
          dataSource: controller.sectorDistribution,
          xValueMapper: (SectorCount data, _) => data.sectorName,
          yValueMapper: (SectorCount data, _) => data.count,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            connectorLineSettings: ConnectorLineSettings(
              type: ConnectorType.curve,
              length: '10%',
            ),
            textStyle: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          explode: true,
          explodeIndex: 0,
          explodeOffset: '5%',
          radius: '70%',
          enableTooltip: true,
        )
      ],
      palette: const [
        Color(0xFF9C27B0),
        Color(0xFFBA68C8),
        Color(0xFFCE93D8),
        Color(0xFFE1BEE7),
        Color(0xFFF3E5F5),
        Color(0xFF7B1FA2),
        Color(0xFF6A1B9A),
        Color(0xFF4A148C),
        Color(0xFF8E24AA),
      ],
    );
  }

  Widget _buildDoughnutChart() {
    return SfCircularChart(
      legend: const Legend(
        isVisible: true,
        position: LegendPosition.bottom,
        overflowMode: LegendItemOverflowMode.wrap,
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CircularSeries>[
        DoughnutSeries<SectorCount, String>(
          dataSource: controller.sectorDistribution,
          xValueMapper: (SectorCount data, _) => data.sectorName,
          yValueMapper: (SectorCount data, _) => data.count,
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelPosition: ChartDataLabelPosition.outside,
            connectorLineSettings: ConnectorLineSettings(
              type: ConnectorType.curve,
              length: '10%',
            ),
            textStyle: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          innerRadius: '40%',
          radius: '70%',
          explode: true,
          explodeIndex: 0,
          explodeOffset: '5%',
          enableTooltip: true,
        )
      ],
      palette: const [
        Color(0xFF9C27B0),
        Color(0xFFBA68C8),
        Color(0xFFCE93D8),
        Color(0xFFE1BEE7),
        Color(0xFFF3E5F5),
        Color(0xFF7B1FA2),
        Color(0xFF6A1B9A),
        Color(0xFF4A148C),
        Color(0xFF8E24AA),
      ],
    );
  }
}
