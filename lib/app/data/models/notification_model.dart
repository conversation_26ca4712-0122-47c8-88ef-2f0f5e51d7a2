import 'package:cloud_firestore/cloud_firestore.dart';

enum NotificationType {
  carStatus,
  newMessage,
  system,
  maintenance,
  other
}

class NotificationModel {
  final String id;
  final String title;
  final String content;
  final NotificationType type;
  final DateTime createdAt;
  final List<String> userIds;
  final Map<String, bool> readBy;

  NotificationModel({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.createdAt,
    required this.userIds,
    required this.readBy,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => NotificationType.other,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      userIds: List<String>.from(data['userIds'] ?? []),
      readBy: Map<String, bool>.from(data['readBy'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'content': content,
      'type': type.toString(),
      'createdAt': Timestamp.fromDate(createdAt),
      'userIds': userIds,
      'readBy': readBy,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? content,
    NotificationType? type,
    DateTime? createdAt,
    List<String>? userIds,
    Map<String, bool>? readBy,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      userIds: userIds ?? this.userIds,
      readBy: readBy ?? this.readBy,
    );
  }
}
