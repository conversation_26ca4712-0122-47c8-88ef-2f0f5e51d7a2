import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';

class WorkshopEntryCard extends StatelessWidget {
  final WorkshopEntry entry;
  final int index;

  const WorkshopEntryCard({
    Key? key,
    required this.entry,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.all(16),
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  index.toString(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سجل الورشة #${entry.id.substring(0, 6)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'نوع الصيانة: ${entry.maintenance_type ?? 'غير محدد'.tr}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(entry.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                entry.status.toString().split('.').last.tr,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Text(
            '${entry.statuses.length} حالة',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ),
        children: [
          const Divider(),
          const SizedBox(height: 8),
          Text(
            'تفاصيل الحالات'.tr,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          ...entry.statuses.map((status) => _buildStatusItem(status)).toList(),
          if (entry.inspection != null) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'تفاصيل الفحص'.tr,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            _buildInspectionDetails(entry.inspection!),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusItem(WorkshopEntryStatus status) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getStatusColor(status.status),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                status.status.toString().split('.').last.tr,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('yyyy-MM-dd HH:mm').format(status.createAt),
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          if (status.notes != null && status.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'الملاحظات: ${status.notes}',
              style: const TextStyle(fontSize: 14),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'بواسطة: ${status.senderName}',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
          if (status.usedProducts != null && status.usedProducts!.isNotEmpty) ...[
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 4),
            Text(
              'المنتجات المستخدمة:'.tr,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            ...status.usedProducts!.map((product) => _buildProductItem(product)),
          ],
        ],
      ),
    );
  }

  Widget _buildProductItem(Map<String, dynamic> product) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(
            Icons.inventory_2_outlined,
            size: 16,
            color: Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            '${product['name']} (${product['quantity']})',
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildInspectionDetails(dynamic inspection) {
    // This is a placeholder - you would need to implement this based on your VehicleInspection model
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الفحص متوفرة',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(dynamic status) {
    if (status is CarStatus) {
      switch (status) {
        case CarStatus.active:
          return AppColors.success;
        case CarStatus.maintenance:
        case CarStatus.inWorkshop:
          return AppColors.warning;
        case CarStatus.rejected:
        case CarStatus.destroyed:
        case CarStatus.outOfService:
          return AppColors.error;
        case CarStatus.pending:
          return AppColors.info;
        default:
          return AppColors.accent;
      }
    } else if (status is WorkshopStatus) {
      switch (status) {
        case WorkshopStatus.completed:
          return AppColors.success;
        case WorkshopStatus.inProgress:
        case WorkshopStatus.maintenance:
        case WorkshopStatus.inWorkshop:
          return AppColors.warning;
        case WorkshopStatus.pending:
          return AppColors.info;
        default:
          return AppColors.accent;
      }
    }
    return AppColors.accent;
  }
}
