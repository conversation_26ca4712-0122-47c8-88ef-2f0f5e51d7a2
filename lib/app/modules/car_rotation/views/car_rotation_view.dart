import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/car_rotate.dart';
import '../../../data/models/sector_model.dart';
import '../../../themes/app_colors.dart';
import '../controllers/car_rotation_controller.dart';

class CarRotationView extends GetView<CarRotationController> {
  const CarRotationView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'تدوير السيارات'.tr,
            style: const TextStyle(
              color: AppColors.textPrimary,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
            onPressed: () => Get.back(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStepIndicator(),
              const SizedBox(height: 24),
              Expanded(
                child: Card(
                  elevation: 0,
                  color: AppColors.surface,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Obx(() {
                      if (controller.isLoading.value) {
                        return const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        );
                      }
                      return _buildCurrentStep(context);
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Obx(() => Row(
      children: [
        _buildStepCircle(
          step: 1,
          title: 'اختيار القطاع'.tr,
          isActive: controller.currentStep.value >= 1,
          isCompleted: controller.currentStep.value > 1,
        ),
        _buildStepLine(controller.currentStep.value > 1),
        _buildStepCircle(
          step: 2,
          title: 'اختيار السيارة'.tr,
          isActive: controller.currentStep.value >= 2,
          isCompleted: controller.currentStep.value > 2,
        ),
        _buildStepLine(controller.currentStep.value > 2),
        _buildStepCircle(
          step: 3,
          title: 'تفاصيل التدوير'.tr,
          isActive: controller.currentStep.value >= 3,
          isCompleted: controller.currentStep.value > 3,
        ),
      ],
    ));
  }

  Widget _buildStepCircle({
    required int step,
    required String title,
    required bool isActive,
    required bool isCompleted,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted
                  ? AppColors.success
                  : isActive
                      ? AppColors.primary
                      : Colors.grey.shade300,
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 20)
                  : Text(
                      step.toString(),
                      style: TextStyle(
                        color: isActive ? Colors.white : Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: isActive ? AppColors.textPrimary : Colors.grey,
              fontSize: 12,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepLine(bool isCompleted) {
    return Container(
      width: 40,
      height: 2,
      color: isCompleted ? AppColors.primary : Colors.grey.shade300,
    );
  }

  Widget _buildCurrentStep(BuildContext context) {
    switch (controller.currentStep.value) {
      case 1:
        return _buildSectorSelection(context);
      case 2:
        return _buildCarSelection();
      case 3:
        return _buildRotationDetails();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSectorSelection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'اختر القطاع المراد النقل منه'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 24),
        Expanded(
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _calculateCrossAxisCount(context),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
            ),
            itemCount: controller.sectors.length,
            itemBuilder: (context, index) {
              final sector = controller.sectors[index];
              return InkWell(
                onTap: () {
                  controller.selectedFromSector.value = sector;
                  controller.goToStep(2);
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.2),
                    ),
                    color: Colors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.location_city,
                        size: 32,
                        color: AppColors.primary.withOpacity(0.7),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        sector.name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCarSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.primary),
              onPressed: () => controller.goToStep(1),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اختر السيارة'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'القطاع: ${controller.selectedFromSector.value?.name}'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        Expanded(
          child: ListView.builder(
            itemCount: controller.cars.length,
            itemBuilder: (context, index) {
              final car = controller.cars[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  onTap: () {
                    controller.selectedCar.value = car;
                    controller.goToStep(3);
                  },
                  contentPadding: const EdgeInsets.all(16),
                  leading: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.directions_car,
                      color: AppColors.primary,
                    ),
                  ),
                  title: Text(
                    car.plateNumber,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Text(
                    car.carModel,
                    style: TextStyle(
                      color: AppColors.textSecondary.withOpacity(0.7),
                    ),
                  ),
                  trailing: const Icon(
                    Icons.arrow_forward_ios,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  int _calculateCrossAxisCount(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    
    if (width > 1200) {
      return 6; // Extra large screens
    } else if (width > 900) {
      return 4; // Large screens
    } else if (width > 600) {
      return 3; // Medium screens
    } else {
      return 2; // Small screens (phones)
    }
  }

  Widget _buildRotationDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: AppColors.primary),
              onPressed: () => controller.goToStep(2),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل التدوير'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    '${'السيارة'.tr}: ${controller.selectedCar.value?.plateNumber}',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        DropdownButtonFormField<Sector>(
          decoration: InputDecoration(
            labelText: 'القطاع الجديد'.tr,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            prefixIcon: const Icon(Icons.location_on, color: AppColors.primary),
          ),
          value: controller.selectedNewSector.value,
          items: controller.newSectors
              .where((s) => s.id != controller.selectedFromSector.value?.id)
              .map((sector) {
            return DropdownMenuItem(
              value: sector,
              enabled: controller.carRotation==null,
              child: Text(sector.name),
            );
          }).toList(),
          onChanged: (sector) => controller.selectedNewSector.value = sector,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<RotateType>(
          decoration: InputDecoration(
            labelText: 'نوع التدوير'.tr,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            prefixIcon: const Icon(Icons.sync_alt, color: AppColors.primary),
          ),

          value: controller.selectedRotationType.value,
          items: RotateType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              enabled: controller.carRotation==null,
              child: Text(type == RotateType.temporary ? 'مؤقت'.tr : 'دائم'.tr),
            );
          }).toList(),
          onChanged: (type) => controller.selectedRotationType(type),
        ),
        const SizedBox(height: 16),
        TextField(
          enabled: controller.carRotation==null,
          controller: controller.reasonController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'سبب التدوير'.tr,
            alignLabelWithHint: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 48),
              child: Icon(Icons.note, color: AppColors.primary),
            ),
          ),
        ),
        const Spacer(),
        Obx(() => CustomButton(
          isSwipe: true,
          onPressed:() {
            controller.submitRotation();
          },
          text: 'إرسال طلب التدوير'.tr,
          isLoading: controller.isSubmitting.value,
        )),
      ],
    );
  }
}
