import 'package:flutter/material.dart';
import '../models/car_part.dart';

/// Custom painter that draws the vehicle and its parts
class CarPartsPainter extends CustomPainter {
  final List<CarPart> parts;
  final CarView currentView;
  final bool showLabels;

  CarPartsPainter({
    required this.parts,
    required this.currentView,
    this.showLabels = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Filter parts for the current view
    final viewParts = parts.where((part) => part.view == currentView).toList();

    // Draw each part
    for (var part in viewParts) {
      // Scale the path to fit the canvas
      final scaledPath = _scalePath(part.path, size);

      // Draw the part outline
      final outlinePaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;
      canvas.drawPath(scaledPath, outlinePaint);

      // Fill the part if selected
      if (part.isSelected) {
        final fillPaint = Paint()
          ..color = Colors.blue.withOpacity(0.3)
          ..style = PaintingStyle.fill;
        canvas.drawPath(scaledPath, fillPaint);
      }

      // Fill with damage color if damage level is set
      if (part.damageLevel != null && part.damageLevel! > 0) {
        final damagePaint = Paint()
          ..color = DamageLevel.values[part.damageLevel!].color.withOpacity(0.5)
          ..style = PaintingStyle.fill;
        canvas.drawPath(scaledPath, damagePaint);
      }

      // Draw labels if enabled
      if (showLabels) {
        _drawLabel(canvas, scaledPath, part.name);
      }
    }
  }

  /// Scales a path to fit within the given size
  Path _scalePath(Path originalPath, Size targetSize) {
    // Get the bounds of the original path
    final bounds = originalPath.getBounds();

    // Calculate scale factors
    final scaleX = targetSize.width / bounds.width;
    final scaleY = targetSize.height / bounds.height;
    final scale = scaleX < scaleY ? scaleX : scaleY;

    // Calculate translation to center the path
    final translationX =
        (targetSize.width - bounds.width * scale) / 2 - bounds.left * scale;
    final translationY =
        (targetSize.height - bounds.height * scale) / 2 - bounds.top * scale;

    // Create a matrix for the transformation
    final matrix = Matrix4.identity()
      ..scale(scale, scale)
      ..translate(translationX / scale, translationY / scale);

    // Apply the transformation to the path
    return originalPath.transform(matrix.storage);
  }

  /// Draws a label for a path
  void _drawLabel(Canvas canvas, Path path, String label) {
    // Get the center of the path
    final bounds = path.getBounds();
    final center = Offset(
      bounds.left + bounds.width / 2,
      bounds.top + bounds.height / 2,
    );

    // Create a text painter
    final textSpan = TextSpan(
      text: label,
      style: const TextStyle(
        color: Colors.black,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();

    // Draw a background for the text
    final backgroundPaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromCenter(
        center: center,
        width: textPainter.width + 8,
        height: textPainter.height + 4,
      ),
      backgroundPaint,
    );

    // Draw the text
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CarPartsPainter oldDelegate) {
    return oldDelegate.parts != parts ||
        oldDelegate.currentView != currentView ||
        oldDelegate.showLabels != showLabels;
  }

  /// Determines if a tap at the given offset hits a part
  @override
  bool? hitTest(Offset position) {
    // We'll implement our custom hit testing in the widget
    // This just tells the system if the position is within our painting area
    return true;
  }

  /// Custom hit test method to find which part was tapped
  CarPart? findHitPart(Offset position, Size size) {
    // Filter parts for the current view
    final viewParts = parts.where((part) => part.view == currentView).toList();

    // Check each part in reverse order (top-most first)
    for (var i = viewParts.length - 1; i >= 0; i--) {
      final part = viewParts[i];
      final scaledPath = _scalePath(part.path, size);

      if (scaledPath.contains(position)) {
        return part;
      }
    }

    return null;
  }
}
