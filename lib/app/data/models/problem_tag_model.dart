import 'package:cloud_firestore/cloud_firestore.dart';

class ProblemTag {
  final String id;
  final String name;
  final String category;
  final int usageCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ProblemTag({
    required this.id,
    required this.name,
    required this.category,
    this.usageCount = 0,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory ProblemTag.fromJson(Map<String, dynamic> json) {
    return ProblemTag(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      category: json['category'] ?? 'general',
      usageCount: json['usageCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      createdAt: (json['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (json['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'category': category,
      'usageCount': usageCount,
      'isActive': isActive,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? DateTime.now(),
    };
  }

  ProblemTag copyWith({
    String? id,
    String? name,
    String? category,
    int? usageCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProblemTag(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      usageCount: usageCount ?? this.usageCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return name;
  }
}
