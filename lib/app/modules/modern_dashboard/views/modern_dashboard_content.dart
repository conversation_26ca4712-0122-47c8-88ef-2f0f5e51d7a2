// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../data/models/car_model.dart';
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/stat_card.dart';
// import '../widgets/responsive_grid.dart';
// import '../widgets/section_header.dart';
// import '../widgets/maintenance_card.dart';
//
// class DashboardContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const DashboardContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Dashboard header
//         SectionHeader(
//           title: 'dashboard_overview'.tr,
//           subtitle: 'dashboard_overview_subtitle'.tr,
//           icon: Icons.dashboard_outlined,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 24),
//
//         // Stats cards in responsive grid
//         ResponsiveGrid(
//           children: [
//             // Active cars
//             Obx(() => StatCard(
//               title: 'active_cars'.tr,
//               count: controller.activeCars.toString(),
//               icon: Icons.directions_car,
//               color: AppColors.success,
//               isLoading: controller.isCarsLoading.value,
//               isRTL: isRTL,
//               onTap: () {
//                 controller.setCarStatusFilter(CarStatus.active);
//                 controller.changeView('cars');
//               },
//             )),
//             // Maintenance cars
//             Obx(() => StatCard(
//               title: 'maintenance_cars'.tr,
//               count: controller.maintenanceCars.toString(),
//               icon: Icons.build,
//               color: AppColors.warning,
//               isLoading: controller.isCarsLoading.value,
//               isRTL: isRTL,
//               onTap: () {
//                 controller.setCarStatusFilter(CarStatus.maintenance);
//                 controller.changeView('cars');
//               },
//             )),
//             // Workshop cars
//             Obx(() => StatCard(
//               title: 'workshop_cars'.tr,
//               count: controller.inWorkshopCars.toString(),
//               icon: Icons.home_repair_service,
//               color: AppColors.info,
//               isLoading: controller.isCarsLoading.value,
//               isRTL: isRTL,
//               onTap: () {
//                 controller.setCarStatusFilter(CarStatus.inWorkshop);
//                 controller.changeView('cars');
//               },
//             )),
//             // Pending cars
//             Obx(() => StatCard(
//               title: 'pending_cars'.tr,
//               count: controller.pendingCars.toString(),
//               icon: Icons.pending_actions,
//               color: AppColors.primary,
//               isLoading: controller.isCarsLoading.value,
//               isRTL: isRTL,
//               onTap: () {
//                 controller.setCarStatusFilter(CarStatus.pending);
//                 controller.changeView('cars');
//               },
//             )),
//             // Total sectors
//             Obx(() => StatCard(
//               title: 'total_sectors'.tr,
//               count: controller.totalSectors.toString(),
//               icon: Icons.location_on,
//               color: Colors.deepPurple,
//               isLoading: controller.isSectorsLoading.value,
//               isRTL: isRTL,
//               onTap: () => controller.changeView('sectors'),
//             )),
//             // Total users
//             Obx(() => StatCard(
//               title: 'total_users'.tr,
//               count: controller.totalUsers.toString(),
//               icon: Icons.people,
//               color: Colors.teal,
//               isLoading: controller.isUsersLoading.value,
//               isRTL: isRTL,
//               onTap: () => controller.changeView('users'),
//             )),
//             // Pending maintenance
//             Obx(() => StatCard(
//               title: 'pending_maintenance'.tr,
//               count: controller.pendingMaintenances.toString(),
//               icon: Icons.build_circle,
//               color: Colors.orange,
//               isLoading: controller.isMaintenanceLoading.value,
//               isRTL: isRTL,
//               onTap: () => controller.changeView('maintenance'),
//             )),
//             // Low stock products
//             Obx(() => StatCard(
//               title: 'low_stock'.tr,
//               count: controller.lowStockProducts.toString(),
//               icon: Icons.inventory_2,
//               color: Colors.red,
//               isLoading: controller.isProductsLoading.value,
//               isRTL: isRTL,
//               onTap: () {
//                 controller.setProductStockFilter(true);
//                 controller.changeView('products');
//               },
//             )),
//           ],
//         ),
//         const SizedBox(height: 24),
//
//         // Car status distribution chart
//         _buildCarStatusChart(),
//         const SizedBox(height: 24),
//
//         // Recent maintenance history
//         _buildRecentMaintenanceSection(),
//       ],
//     );
//   }
//
//   // Build car status distribution chart
//   Widget _buildCarStatusChart() {
//     return Obx(() {
//       if (controller.isCarsLoading.value) {
//         return _buildLoadingCard(200);
//       }
//
//       if (controller.carStatusData.isEmpty) {
//         return _buildEmptyCard('no_car_data'.tr);
//       }
//
//       // Prepare data for the chart
//       final data = controller.carStatusData;
//
//       return Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(16),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.black.withOpacity(0.05),
//               blurRadius: 10,
//               offset: const Offset(0, 4),
//             ),
//           ],
//         ),
//         child: Column(
//           crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//           children: [
//             Text(
//               'car_status_distribution'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.black87,
//               ),
//             ),
//             const SizedBox(height: 16),
//             SizedBox(
//               height: 200,
//               child: BarChart(
//                 BarChartData(
//                   alignment: BarChartAlignment.center,
//                   barTouchData: BarTouchData(
//                     enabled: true,
//                     touchTooltipData: BarTouchTooltipData(
//                       tooltipBgColor: Colors.blueGrey.withOpacity(0.8),
//                       getTooltipItem: (group, groupIndex, rod, rodIndex) {
//                         final status = data[groupIndex]['status'] as String;
//                         final count = data[groupIndex]['count'] as int;
//                         return BarTooltipItem(
//                           '$status: $count',
//                           GoogleFonts.cairo(
//                             color: Colors.white,
//                             fontWeight: FontWeight.bold,
//                           ),
//                         );
//                       },
//                     ),
//                   ),
//                   titlesData: FlTitlesData(
//                     show: true,
//                     bottomTitles: AxisTitles(
//                       sideTitles: SideTitles(
//                         showTitles: true,
//                         getTitlesWidget: (value, meta) {
//                           if (value >= data.length || value < 0) return const Text('');
//                           final status = data[value.toInt()]['status'] as String;
//                           return Padding(
//                             padding: const EdgeInsets.only(top: 8),
//                             child: Text(
//                               status,
//                               style: GoogleFonts.cairo(
//                                 fontSize: 12,
//                                 color: Colors.black54,
//                               ),
//                             ),
//                           );
//                         },
//                       ),
//                     ),
//                     leftTitles: AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     topTitles: AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     rightTitles: AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                   ),
//                   borderData: FlBorderData(show: false),
//                   gridData: FlGridData(show: false),
//                   barGroups: List.generate(
//                     data.length,
//                     (index) {
//                       final count = data[index]['count'] as int;
//                       return BarChartGroupData(
//                         x: index,
//                         barRods: [
//                           BarChartRodData(
//                             toY: count.toDouble(),
//                             color: _getStatusColor(data[index]['status'] as String),
//                             width: 20,
//                             borderRadius: const BorderRadius.only(
//                               topLeft: Radius.circular(6),
//                               topRight: Radius.circular(6),
//                             ),
//                           ),
//                         ],
//                       );
//                     },
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     });
//   }
//
//   // Build recent maintenance section
//   Widget _buildRecentMaintenanceSection() {
//     return Obx(() {
//       if (controller.isMaintenanceLoading.value) {
//         return _buildLoadingCard(300);
//       }
//
//       if (controller.recentMaintenanceHistory.isEmpty) {
//         return _buildEmptyCard('no_maintenance_data'.tr);
//       }
//
//       return Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(16),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.black.withOpacity(0.05),
//               blurRadius: 10,
//               offset: const Offset(0, 4),
//             ),
//           ],
//         ),
//         child: Column(
//           crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//           children: [
//             Row(
//               textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   'recent_maintenance'.tr,
//                   style: GoogleFonts.cairo(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.black87,
//                   ),
//                 ),
//                 TextButton.icon(
//                   onPressed: () => controller.changeView('maintenance'),
//                   icon: const Icon(Icons.arrow_forward, size: 16),
//                   label: Text('view_all'.tr),
//                   style: TextButton.styleFrom(
//                     foregroundColor: AppColors.primary,
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 16),
//             ...controller.recentMaintenanceHistory.map((item) {
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: MaintenanceCard(
//                   car: item.car,
//                   maintenance: item.maintenance,
//                   isRTL: isRTL,
//                   onTap: () => controller.changeView('maintenance'),
//                 ),
//               );
//             }).toList(),
//           ],
//         ),
//       );
//     });
//   }
//
//   // Helper method to get color for car status
//   Color _getStatusColor(String status) {
//     switch (status.toLowerCase()) {
//       case 'active': return AppColors.success;
//       case 'maintenance': return AppColors.warning;
//       case 'inworkshop': return AppColors.info;
//       case 'pending': return AppColors.primary;
//       case 'rejected': return AppColors.error;
//       case 'archived': return Colors.grey;
//       default: return Colors.grey;
//     }
//   }
//
//   // Build loading card placeholder
//   Widget _buildLoadingCard(double height) {
//     return Container(
//       height: height,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Shimmer.fromColors(
//         baseColor: Colors.grey[300]!,
//         highlightColor: Colors.grey[100]!,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Container(
//               width: 150,
//               height: 24,
//               color: Colors.white,
//             ),
//             const SizedBox(height: 16),
//             Expanded(
//               child: Container(
//                 color: Colors.white,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   // Build empty state card
//   Widget _buildEmptyCard(String message) {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.info_outline,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               message,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
