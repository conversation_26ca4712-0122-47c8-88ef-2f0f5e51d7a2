import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/splash_controller.dart';

class SplashView extends GetView<SplashController> {
  const SplashView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Stack(
        children: [
          // Main Content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Main App Logo
                FadeTransition(
                  opacity: controller.mainLogoAnimation,
                  child: ScaleTransition(
                    scale: controller.mainLogoAnimation,
                    child: Container(
                      width: 160,
                      height: 160,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Image.asset(
                        'assets/images/logo.png',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                // App Name
                FadeTransition(
                  opacity: controller.textsAnimation,
                  child: const Text(
                    'الهلال الأحمر السعودي',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // App Description
                FadeTransition(
                  opacity: controller.textsAnimation,
                  child: const Text(
                    'نظام إدارة المركبات',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Powered By Logo and Rights
          Positioned(
            left: 0,
            right: 0,
            bottom: 40,
            child: Column(
              children: [
                // Powered By Logo
                FadeTransition(
                  opacity: controller.poweredByLogoAnimation,
                  child: Container(
                    width: 120,
                    height: 40,
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    child: Image.asset(
                      'assets/images/logo.png',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                // Copyright Text
                FadeTransition(
                  opacity: controller.textsAnimation,
                  child: const Text(
                    'جميع الحقوق محفوظة © ٢٠٢٥',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white60,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
