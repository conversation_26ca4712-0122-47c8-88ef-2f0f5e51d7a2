import 'package:cars_app/app/data/models/sector_model.dart';
import 'package:cars_app/app/modules/car/view/car_form.dart';
import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/car_model.dart';
import '../../../routes/app_pages.dart';
import '../controller/car_controller.dart';
import '../../../data/models/user_model.dart';
import '../../../views/car_forms.dart';
import 'workshop_form.dart';
import 'car_history_view.dart';

class CarView extends GetView<CarController> {
  const CarView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [IconButton(onPressed: () => controller.processCarDataWithSectors(), icon: Icon(Icons.add), tooltip: 'add'.tr),],
      ),
      backgroundColor: AppColors.background,
      floatingActionButton: FloatingActionButton(
        onPressed: () =>
            Get.to(() => AddCarForm(), transition: Transition.rightToLeft),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
        tooltip: 'إضافة سيارة',
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 180,
              floating: true,
              pinned: true,
              snap: false,
              backgroundColor: AppColors.primary,
              elevation: 0,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              title: Obx(() {
                return controller.isSearchVisible.value
                    ? _buildSearchField()
                    : const Text(
                  'إدارة السيارات',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }),
              centerTitle: true,
              actions: [
                IconButton(
                  icon: Obx(() {
                    return Icon(
                      controller.isSearchVisible.value ? Icons.close : Icons.search,
                      color: Colors.white,
                    );
                  }),
                  onPressed: () {
                    controller.isSearchVisible.value = !controller.isSearchVisible.value;
                    if (!controller.isSearchVisible.value) {
                      controller.searchQuery.value = '';
                    }
                  },
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Obx(() {
                  return controller.isSearchVisible.value? const SizedBox() : Container(
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Obx(() =>
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildInfoColumn(
                                  'إجمالي السيارات',
                                  '${controller.cars.length}',
                                  Icons.directions_car,
                                ),
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.white.withOpacity(0.2),
                                ),
                                _buildInfoColumn(
                                  'القطاعات',
                                  '${controller.sectors.length}',
                                  Icons.location_on,
                                ),
                                Container(
                                  width: 1,
                                  height: 40,
                                  color: Colors.white.withOpacity(0.2),
                                ),
                                _buildInfoColumn(
                                  'في الصيانة',
                                  '${controller.cars
                                      .where((car) =>
                                  car.status == CarStatus.maintenance)
                                      .length}',
                                  Icons.build,
                                ),
                              ],
                            )),
                      ),
                    ),
                  );
                }),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            const SizedBox(height: 16),
            // Filters
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.filter_list,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'تصفية',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 200,
                            child: Obx(() =>
                                DropdownButtonFormField<CarStatus>(
                                  value: controller.selectedStatus.value,
                                  decoration: InputDecoration(
                                    labelText: 'الحالة',
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide.none,
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  items: [
                                    const DropdownMenuItem<CarStatus>(
                                      value: null,
                                      child: Text('جميع الحالات'),
                                    ),
                                    ...CarStatus.values.map((status) =>
                                        DropdownMenuItem<CarStatus>(
                                          value: status,
                                          child: Text(_getStatusText(status)),
                                        )),
                                  ],
                                  onChanged: (value) =>
                                  controller.selectedStatus.value = value,
                                )),
                          ),
                          const SizedBox(width: 16),
                          SizedBox(
                            width: 200,
                            child: Obx(() =>
                                DropdownButtonFormField<Sector>(
                                  value: controller.selectedSector.value,
                                  decoration: InputDecoration(
                                    labelText: 'القطاع',
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide.none,
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  items: [
                                    const DropdownMenuItem<Sector>(
                                      value: null,
                                      child: Text('جميع القطاعات'),
                                    ),
                                    ...controller.sectors.map((sector) =>
                                        DropdownMenuItem<Sector>(
                                          value: sector,
                                          child: Text(sector.name),
                                        )),
                                  ],
                                  onChanged: (value) =>
                                  controller.selectedSector.value = value,
                                )),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Cars List
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary),
                  ));
                }

                if (controller.cars.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.directions_car_outlined,
                            size: 64,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد سيارات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () =>
                              Get.to(() => AddCarForm(),
                                  transition: Transition.rightToLeft),
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة سيارة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: controller.filteredCars.length,
                  itemBuilder: (context, index) {
                    final car = controller.filteredCars[index];
                    return _buildCarCard(context, car);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildCarCard(BuildContext context, Car car) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getStatusColor(car.status).withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.directions_car,
            color: _getStatusColor(car.status),
          ),
        ),
        title: Text(
          car.plateNumber ?? '',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          _getStatusText(car.status),
          style: TextStyle(
            color: _getStatusColor(car.status),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('نوع السيارة', car.carType ?? '-'),
                const SizedBox(height: 8),
                _buildInfoRow('الترميز', car.encoding ?? '-'),
                const SizedBox(height: 8),
                _buildInfoRow('الموديل', car.carModel ?? '-'),
                const SizedBox(height: 16),
                _buildInfoRow('القطاع', car.sectorName ?? '-'),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.history,
                      label: 'السجل',
                      onPressed: () => Get.to(() => CarHistoryView(car: car)),
                    ),
                    _buildActionButton(
                      icon: Icons.build,
                      label: 'الصيانة',
                      onPressed: () =>
                          Get.toNamed(Routes.WORKSHOP_FORM, arguments: {
                            'car': car,}),
                    ),
                    _buildActionButton(
                      icon: Icons.edit,
                      label: 'تعديل',
                      onPressed: () => Get.to(() => CarForm(car: car)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: AppColors.primary, size: 20),
      label: Text(
        label,
        style: TextStyle(
          color: AppColors.primary,
          fontSize: 14,
        ),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
      ),
    );
  }

  String _getStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'نشط';
      case CarStatus.destroyed:
        return 'معطل';
      case CarStatus.maintenance:
        return 'صيانة';
      case CarStatus.inWorkshop:
        return 'في الورشة';
      case CarStatus.outOfService:
        return 'خارج الخدمة';
      case CarStatus.sentRequest:
        return 'تم الارسال';
      case CarStatus.sendGroup:
        return 'مجموعة';
      case CarStatus.receipt:
        return 'استلام';
      case CarStatus.callToWorkshop:
        return 'طلب ورشة';
      case CarStatus.pending:
        return 'معلق';
      case CarStatus.done:
        return 'مكتمل';
      default:
        return 'غير معروف';
    }
  }

  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return Colors.green;
      case CarStatus.destroyed:
        return AppColors.error;
      case CarStatus.maintenance:
        return Colors.orange;
      case CarStatus.inWorkshop:
        return AppColors.info;
      case CarStatus.outOfService:
        return Colors.grey;
      case CarStatus.sentRequest:
        return AppColors.accent;
      case CarStatus.sendGroup:
        return AppColors.secondary;
      case CarStatus.receipt:
        return Colors.indigo;
      case CarStatus.callToWorkshop:
        return Colors.amber;
      case CarStatus.pending:
        return Colors.brown;
      case CarStatus.done:
        return Colors.green.shade800;
      default:
        return Colors.grey;
    }
  }

  Widget _buildSearchField() {
    return TextField(
      onChanged: (value) => controller.searchQuery.value = value,
      style: const TextStyle(color: Colors.white),
      cursorColor: Colors.white,
      textDirection: TextDirection.rtl,
      decoration: InputDecoration(
        hintText: 'بحث عن سيارة...',
        hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
        hintTextDirection: TextDirection.rtl,
        border: InputBorder.none,
        prefixIcon: const Icon(Icons.search, color: Colors.white),
        contentPadding: const EdgeInsets.symmetric(vertical: 12),
        isDense: true,
      ),
    );
  }
}
