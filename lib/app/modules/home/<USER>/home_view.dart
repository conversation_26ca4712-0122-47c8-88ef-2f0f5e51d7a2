import 'package:cars_app/app/modules/reports/views/reports_view.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../admin/views/problem_tags_management_view.dart';
import '../../car/view/car_view.dart';
import '../../car_rotation/views/car_rotation_view.dart';
import '../../dashboard/views/modern_dashboard_view.dart';
import '../../dashboard/views/problem_statistics_view.dart';
import '../../inventory/views/inventory_list_screen.dart';
import '../../notifications/views/notifications_view.dart';
import '../../sector/view/sector_view.dart';
import '../../settings/views/settings_view.dart';
import '../../user/view/user_view.dart';
import '../../vehicle_inspection/views/vehicle_inspection_view.dart';
import '../controllers/home_controller.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/sector_model.dart';
import '../../../services/sector_service.dart';
import '../../../widgets/notification_icon_widget.dart';
import '../../../widgets/role_selection_widget.dart';

class HomeView extends GetView<HomeController> {
  HomeView({Key? key}) : super(key: key);

  final _auth = Get.find<AuthController>();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.primary,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
          title: Text(
            'لوحة التحكم',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            // Only show role selection icon for admin users
            // if (_auth.isAdmin)
              Visibility(
                child: IconButton(
                  icon: const Icon(Icons.person_outline),
                  tooltip: 'تغيير الدور', // Change role
                  onPressed: () {
                    _showRoleSelectionDialog(context);
                  },
                ),
              ),
          ],
        ),
        drawer: Obx(() => _buildSidebar()),
        body: Row(
          children: [
            Expanded(
              child: Obx(() => _buildPage(controller.currentPage.value)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(String page) {
    switch (page) {

      case 'dashboard':
        return const DashboardView();
      case 'cars':

        return const CarView();
      case 'car-rotation':

        return const CarRotationView();
      case 'sectors':
        return SectorView();

      case 'users':
        return UserView();

      case 'settings':
        return const SettingsView();

      case 'reports':
        return const ReportsView();

        case 'InventoryListScreen':
        return const InventoryListScreen();

        case 'PROBLEM_TAGS_MANAGEMENT':
        return const ProblemTagsManagementView();
        case 'PROBLEM_STATISTICS':
        return const ProblemStatisticsView();
      default:
        return const DashboardView();
    }
  }

  Widget _buildSidebar() {
    return Container(
      width: 280,
      color: Colors.white,
      child: Column(
        children: [
          _buildSidebarHeader(),
          const Divider(height: 1),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildNavItem(
                  'لوحة التحكم',
                  Icons.dashboard_rounded,
                  'dashboard',
                ),
                _buildNavItem(
                  'السيارات',
                  Icons.directions_car_rounded,
                  'cars',
                ),

                _buildNavItem(
                  'القطاعات',
                  Icons.location_on_rounded,
                  'sectors',
                ),
                _buildNavItem(
                  'المستخدمين',
                  Icons.people_rounded,
                  'users',
                ),
                const Divider(),

                  _buildNavItem(
                    'تقارير الصيانة',
                    Icons.analytics_rounded,
                    'reports',
                  ),
                const Divider(),

                  _buildNavItem(
                    'قطع الغيار',
                    Icons.analytics_rounded,
                    'InventoryListScreen',
                  ),
                _buildNavItem(
                    'إدارة المشاكل الشائعة',
                    Icons.analytics_rounded,
                    'PROBLEM_TAGS_MANAGEMENT',
                  ),
                _buildNavItem(
                    'إحصائيات المشاكل',
                    Icons.analytics_rounded,
                    'PROBLEM_STATISTICS',
                  ),

                _buildNavItem(
                  'الإعدادات',
                  Icons.settings_rounded,
                  'settings',
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          _buildUserProfile(),
        ],
      ),
    );
  }

  Widget _buildSidebarHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.car_rental,
              color: AppColors.primary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          const Text(
            'الهلال الأحمر السعودي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(String title, IconData icon, String page) {
    final isSelected = controller.currentPage.value == page;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
          size: 22,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? AppColors.primary : AppColors.textPrimary,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 15,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        selected: isSelected,
        onTap: () {
          controller.setPage(page);
          Get.back();
        },
      ),
    );
  }

  Widget _buildUserProfile() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.person_outline,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _auth.userName ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  _auth.userRole ?? '',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.logout,
              color: AppColors.error,
              size: 20,
            ),
            onPressed: () => Get.find<AuthController>().logout(),
          ),
        ],
      ),
    );
  }

  // Show role selection dialog with cards instead of dropdowns
  void _showRoleSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر الدور',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 16),
                // Use Expanded with a Container of fixed height to avoid layout issues
                Container(
                  height: 400, // Fixed height to avoid intrinsic dimension errors
                  child: const RoleSelectionWidget(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
