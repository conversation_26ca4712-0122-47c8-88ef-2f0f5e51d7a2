import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../modules/car/controller/car_controller.dart';
import '../modules/sector/controller/sector_controller.dart';
import '../data/models/car_model.dart';
import '../data/models/sector_model.dart';
import '../data/language_controller.dart';
import '../themes/app_colors.dart';

class ImprovedAddCarForm extends StatefulWidget {
  final Car? car;
  
  const ImprovedAddCarForm({Key? key, this.car}) : super(key: key);

  @override
  State<ImprovedAddCarForm> createState() => _ImprovedAddCarFormState();
}

class _ImprovedAddCarFormState extends State<ImprovedAddCarForm> {
  final CarController carController = Get.find();
  final SectorController sectorController = Get.find();
  final LanguageController languageController = Get.find<LanguageController>();
  final _formKey = GlobalKey<FormState>();
  
  late final TextEditingController plateNumberController;
  late final TextEditingController plateCharactersController;
  late final TextEditingController encodingController;
  late final TextEditingController carTypeController;
  late int selectedYear;
  late Sector? selectedSector;
  late bool isInternal;
  late final List<int> yearsList;
  
  final RxBool isLoading = false.obs;
  final RxInt currentStep = 0.obs;

  @override
  void initState() {
    super.initState();
    
    // Initialize controllers
    plateNumberController = TextEditingController(text: widget.car?.plateNumber);
    plateCharactersController = TextEditingController(text: widget.car?.plateCharacters);
    encodingController = TextEditingController(text: widget.car?.encoding);
    carTypeController = TextEditingController(text: widget.car?.carType);
    
    // Initialize other values
    selectedYear = widget.car != null 
        ? int.tryParse(widget.car!.carModel) ?? DateTime.now().year 
        : DateTime.now().year;
    isInternal = widget.car?.isInternal ?? true;
    
    // Generate years list (last 30 years)
    final currentYear = DateTime.now().year;
    yearsList = List.generate(30, (index) => currentYear - index);
    
    // Load sectors
    sectorController.loadSectors();
    
    // Set selected sector if editing
    if (widget.car != null) {
      selectedSector = sectorController.sectors.firstWhere(
        (sector) => sector.id == widget.car!.sectorId,
        orElse: () => sectorController.sectors.isNotEmpty ? sectorController.sectors.first : null,
      );
    } else {
      selectedSector = sectorController.sectors.isNotEmpty ? sectorController.sectors.first : null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = languageController.isArabic;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          widget.car == null ? 'add_car'.tr : 'edit_car'.tr,
          style: TextStyle(
            color: Colors.white,
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'help'.tr,
          ),
        ],
      ),
      body: Obx(() => isLoading.value 
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: AppColors.primary),
                SizedBox(height: 16.h),
                Text(
                  'please_wait'.tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          )
        : _buildForm(context, isRTL),
      ),
    );
  }

  Widget _buildForm(BuildContext context, bool isRTL) {
    return Directionality(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Stepper(
        currentStep: currentStep.value,
        onStepContinue: () {
          if (currentStep.value < 2) {
            currentStep.value++;
          } else {
            _submitForm();
          }
        },
        onStepCancel: () {
          if (currentStep.value > 0) {
            currentStep.value--;
          } else {
            Get.back();
          }
        },
        controlsBuilder: (context, details) {
          return Padding(
            padding: EdgeInsets.only(top: 20.h),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: details.onStepContinue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      currentStep.value == 2 ? 'submit'.tr : 'next'.tr,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton(
                    onPressed: details.onStepCancel,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: BorderSide(color: AppColors.primary),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      currentStep.value == 0 ? 'cancel'.tr : 'back'.tr,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        steps: [
          Step(
            title: Text('basic_info'.tr, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
            subtitle: Text('enter_car_basic_info'.tr, style: TextStyle(fontSize: 14.sp)),
            content: Form(
              key: _formKey,
              child: Column(
                children: [
                  _buildSectorDropdown(),
                  SizedBox(height: 16.h),
                  _buildCarTypeField(),
                  SizedBox(height: 16.h),
                  _buildYearDropdown(),
                  SizedBox(height: 16.h),
                  _buildInternalSwitch(),
                ],
              ),
            ),
            isActive: currentStep.value >= 0,
          ),
          Step(
            title: Text('plate_info'.tr, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
            subtitle: Text('enter_plate_details'.tr, style: TextStyle(fontSize: 14.sp)),
            content: Column(
              children: [
                _buildPlateNumberField(),
                SizedBox(height: 16.h),
                _buildPlateCharactersField(),
                SizedBox(height: 24.h),
                _buildPlatePreview(),
              ],
            ),
            isActive: currentStep.value >= 1,
          ),
          Step(
            title: Text('review'.tr, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
            subtitle: Text('review_car_info'.tr, style: TextStyle(fontSize: 14.sp)),
            content: _buildReviewCard(),
            isActive: currentStep.value >= 2,
          ),
        ],
      ),
    );
  }

  Widget _buildSectorDropdown() {
    return Obx(() {
      if (sectorController.sectors.isEmpty) {
        return Center(
          child: Column(
            children: [
              CircularProgressIndicator(color: AppColors.primary),
              SizedBox(height: 8.h),
              Text('loading_sectors'.tr),
            ],
          ),
        );
      }
      
      return DropdownButtonFormField<Sector>(
        decoration: InputDecoration(
          labelText: 'sector'.tr,
          hintText: 'select_sector'.tr,
          prefixIcon: const Icon(Icons.location_city),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        ),
        value: selectedSector,
        items: sectorController.sectors.map((sector) {
          return DropdownMenuItem<Sector>(
            value: sector,
            child: Text(sector.name),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            selectedSector = value;
          });
        },
        validator: (value) => value == null ? 'please_select_sector'.tr : null,
      );
    });
  }

  Widget _buildCarTypeField() {
    return TextFormField(
      controller: carTypeController,
      decoration: InputDecoration(
        labelText: 'car_type'.tr,
        hintText: 'enter_car_type'.tr,
        prefixIcon: const Icon(Icons.directions_car),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'please_enter_car_type'.tr;
        }
        return null;
      },
    );
  }

  Widget _buildYearDropdown() {
    return DropdownButtonFormField<int>(
      decoration: InputDecoration(
        labelText: 'car_model_year'.tr,
        hintText: 'select_year'.tr,
        prefixIcon: const Icon(Icons.calendar_today),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      ),
      value: selectedYear,
      items: yearsList.map((year) {
        return DropdownMenuItem<int>(
          value: year,
          child: Text(year.toString()),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            selectedYear = value;
          });
        }
      },
    );
  }

  Widget _buildInternalSwitch() {
    return Card(
      elevation: 0,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'car_ownership'.tr,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: Text('internal'.tr),
                    value: true,
                    groupValue: isInternal,
                    onChanged: (value) {
                      setState(() {
                        isInternal = value!;
                      });
                    },
                    activeColor: AppColors.primary,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: Text('external'.tr),
                    value: false,
                    groupValue: isInternal,
                    onChanged: (value) {
                      setState(() {
                        isInternal = value!;
                      });
                    },
                    activeColor: AppColors.primary,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                isInternal ? 'internal_car_description'.tr : 'external_car_description'.tr,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlateNumberField() {
    return TextFormField(
      controller: plateNumberController,
      decoration: InputDecoration(
        labelText: 'plate_number'.tr,
        hintText: 'enter_plate_number'.tr,
        prefixIcon: const Icon(Icons.pin),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(4),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'please_enter_plate_number'.tr;
        }
        return null;
      },
    );
  }

  Widget _buildPlateCharactersField() {
    return TextFormField(
      controller: plateCharactersController,
      decoration: InputDecoration(
        labelText: 'plate_characters'.tr,
        hintText: 'enter_plate_characters'.tr,
        prefixIcon: const Icon(Icons.text_fields),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'please_enter_plate_characters'.tr;
        }
        return null;
      },
    );
  }

  Widget _buildPlatePreview() {
    final plateNumber = plateNumberController.text.isEmpty ? '0000' : plateNumberController.text;
    final plateChars = plateCharactersController.text.isEmpty ? 'أ ب ج' : plateCharactersController.text;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'plate_preview'.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          width: double.infinity,
          height: 80.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 20.w,
                decoration: BoxDecoration(
                  color: Colors.green.shade700,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(languageController.isArabic ? 0 : 8.r),
                    bottomLeft: Radius.circular(languageController.isArabic ? 0 : 8.r),
                    topRight: Radius.circular(languageController.isArabic ? 8.r : 0),
                    bottomRight: Radius.circular(languageController.isArabic ? 8.r : 0),
                  ),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      plateChars,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      height: 40.h,
                      width: 1,
                      color: Colors.grey.shade300,
                    ),
                    Text(
                      plateNumber,
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReviewCard() {
    final encoding = '${plateCharactersController.text}-${plateNumberController.text}';
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildReviewItem(
              icon: Icons.location_city,
              title: 'sector'.tr,
              value: selectedSector?.name ?? 'not_selected'.tr,
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.directions_car,
              title: 'car_type'.tr,
              value: carTypeController.text,
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.calendar_today,
              title: 'car_model_year'.tr,
              value: selectedYear.toString(),
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.pin,
              title: 'plate_number'.tr,
              value: plateNumberController.text,
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.text_fields,
              title: 'plate_characters'.tr,
              value: plateCharactersController.text,
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.qr_code,
              title: 'encoding'.tr,
              value: encoding,
            ),
            Divider(height: 24.h),
            _buildReviewItem(
              icon: Icons.business,
              title: 'ownership'.tr,
              value: isInternal ? 'internal'.tr : 'external'.tr,
              valueColor: isInternal ? AppColors.success : AppColors.warning,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewItem({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary, size: 24.sp),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: valueColor ?? AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('help'.tr),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('help_add_car_description'.tr),
              SizedBox(height: 16.h),
              Text('help_fields'.tr, style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8.h),
              _buildHelpItem('sector'.tr, 'help_sector'.tr),
              _buildHelpItem('car_type'.tr, 'help_car_type'.tr),
              _buildHelpItem('plate_number'.tr, 'help_plate_number'.tr),
              _buildHelpItem('plate_characters'.tr, 'help_plate_characters'.tr),
              _buildHelpItem('ownership'.tr, 'help_ownership'.tr),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14.sp,
            ),
          ),
          Text(
            description,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate() && selectedSector != null) {
      try {
        isLoading.value = true;
        
        final encoding = '${plateCharactersController.text}-${plateNumberController.text}';
        
        if (widget.car == null) {
          // Create new car
          await carController.createCar(
            Car(
              id: const Uuid().v4(),
              sectorId: selectedSector!.id,
              tempSectorId: '',
              plateNumber: plateNumberController.text,
              plateCharacters: plateCharactersController.text,
              encoding: encoding,
              carModel: selectedYear.toString(),
              sectorName: selectedSector!.name,
              tempSectorName: '',
              carType: carTypeController.text,
              rotations: [],
              isInternal: isInternal,
              createAt: DateTime.now(),
              status: CarStatus.active,
              workshopHistory: [],
            ),
          );
          
          Get.back();
          Get.snackbar(
            'success'.tr,
            'car_added_successfully'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success.withOpacity(0.7),
            colorText: Colors.white,
            margin: EdgeInsets.all(8.r),
            borderRadius: 8.r,
            duration: const Duration(seconds: 3),
          );
        } else {
          // Update existing car
          final updatedCar = Car(
            id: widget.car!.id,
            sectorId: selectedSector!.id,
            tempSectorId: widget.car!.tempSectorId,
            plateNumber: plateNumberController.text,
            plateCharacters: plateCharactersController.text,
            encoding: encoding,
            carModel: selectedYear.toString(),
            sectorName: selectedSector!.name,
            tempSectorName: widget.car!.tempSectorName,
            carType: carTypeController.text,
            rotations: widget.car!.rotations,
            isInternal: isInternal,
            createAt: widget.car!.createAt,
            status: widget.car!.status,
            workshopHistory: widget.car!.workshopHistory,
            lasUpdate: DateTime.now(),
            action: widget.car!.action,
            currentWorkshopEntryId: widget.car!.currentWorkshopEntryId,
            metadata: widget.car!.metadata,
          );
          
          await carController.updateCar(updatedCar);
          
          Get.back();
          Get.snackbar(
            'success'.tr,
            'car_updated_successfully'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success.withOpacity(0.7),
            colorText: Colors.white,
            margin: EdgeInsets.all(8.r),
            borderRadius: 8.r,
            duration: const Duration(seconds: 3),
          );
        }
      } catch (e) {
        Get.snackbar(
          'error'.tr,
          'failed_to_save_car'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withOpacity(0.7),
          colorText: Colors.white,
          margin: EdgeInsets.all(8.r),
          borderRadius: 8.r,
          duration: const Duration(seconds: 3),
        );
      } finally {
        isLoading.value = false;
      }
    }
  }

  @override
  void dispose() {
    plateNumberController.dispose();
    plateCharactersController.dispose();
    encodingController.dispose();
    carTypeController.dispose();
    super.dispose();
  }
}
