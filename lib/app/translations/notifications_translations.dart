abstract class NotificationsTranslations {
  static const Map<String, Map<String, String>> translations = {
    'ar': {
      'notifications': 'الإشعارات',
      'no_notifications': 'لا توجد إشعارات',
      'from_sector': 'من القطاع',
      'to_sector': 'إلى القطاع',
      'approved': 'تمت الموافقة',
      'reject': 'رفض',
      'approve': 'موافقة',
      'request_approved': 'تمت الموافقة على الطلب',
      'request_rejected': 'تم رفض الطلب',
      'car_rotated': 'تم تحويل السيارة',
      'failed_to_load_notifications': 'فشل في تحميل الإشعارات',
      'operation_failed': 'فشلت العملية',
      'success': 'نجاح',
      'error': 'خطأ',
    },
    'en': {
      'notifications': 'Notifications',
      'no_notifications': 'No Notifications',
      'from_sector': 'From Sector',
      'to_sector': 'To Sector',
      'approved': 'Approved',
      'reject': 'Reject',
      'approve': 'Approve',
      'request_approved': 'Request Approved',
      'request_rejected': 'Request Rejected',
      'car_rotated': 'Car Rotated',
      'failed_to_load_notifications': 'Failed to load notifications',
      'operation_failed': 'Operation failed',
      'success': 'Success',
      'error': 'Error',
    }
  };
}
