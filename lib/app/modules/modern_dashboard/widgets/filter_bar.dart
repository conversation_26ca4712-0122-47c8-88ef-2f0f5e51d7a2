import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../themes/app_colors.dart';

class FilterBar<T> extends StatelessWidget {
  final String title;
  final List<FilterOption<T>> options;
  final T? selectedValue;
  final Function(T?) onChanged;
  final bool showClearButton;
  final bool isRTL;
  final TextEditingController? searchController;
  final Function(String)? onSearch;

  const FilterBar({
    Key? key,
    required this.title,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    this.showClearButton = true,
    this.isRTL = false,
    this.searchController,
    this.onSearch,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (showClearButton && selectedValue != null)
                TextButton.icon(
                  onPressed: () => onChanged(null),
                  icon: const Icon(Icons.clear, size: 16),
                  label: Text('clear'.tr),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    visualDensity: VisualDensity.compact,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (searchController != null && onSearch != null) ...[  
            TextField(
              controller: searchController,
              onChanged: onSearch,
              decoration: InputDecoration(
                hintText: 'search'.tr,
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
            const SizedBox(height: 12),
          ],
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: options.map((option) {
                final isSelected = selectedValue == option.value;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    selected: isSelected,
                    label: Text(
                      option.label,
                      style: GoogleFonts.cairo(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                    backgroundColor: Colors.grey[100],
                    selectedColor: AppColors.primary,
                    checkmarkColor: Colors.white,
                    onSelected: (_) => onChanged(option.value),
                    avatar: option.icon != null
                        ? Icon(
                            option.icon,
                            size: 16,
                            color: isSelected ? Colors.white : Colors.grey,
                          )
                        : null,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

class FilterOption<T> {
  final String label;
  final T value;
  final IconData? icon;

  FilterOption({
    required this.label,
    required this.value,
    this.icon,
  });
}
