import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import '../../data/models/car_model.dart';
import '../../modules/reports/controllers/reports_controller.dart';

/// Service for generating Excel reports
class ExcelExportService {
  /// Generate a workshop report Excel
  Future<Uint8List> generateWorkshopReport({
    required List<WorkshopReportEntry> entries,
    required DateTime startDate,
    required DateTime endDate,
    required List<StatusCount> statusDistribution,
    required List<SectorCount> sectorDistribution,
  }) async {
    // Create a new Excel document with UTF-8 encoding for Arabic support
    final excel = Excel.createExcel();

    // Get the default sheet name
    final sheetName = excel.getDefaultSheet() ?? 'Sheet1';
    final mainSheet = excel[sheetName];

    // Set column widths for better readability
    for (int i = 0; i < 8; i++) {
      mainSheet.setColumnWidth(i, 18);
    }

    // Add title with Arabic text (matching PDF format)
    final titleCell =
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = TextCellValue(
        'تقرير ورشة - ${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for title
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0),
        CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0));

    // Add summary section header (matching PDF format)
    final summaryHeaderCell =
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2));
    summaryHeaderCell.value = TextCellValue('ملخص الإحصائيات');
    summaryHeaderCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
    );

    // Add summary data
    int row = 4;

    // Period
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الفترة:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value =
        TextCellValue(
            '${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}');
    row++;

    // Number of records
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('عدد السجلات:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('${entries.length}');
    row++;

    // Most common status
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('أكثر حالة تكراراً:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value =
        TextCellValue(statusDistribution.isNotEmpty
            ? statusDistribution.first.status.toString().split('.').last
            : 'N/A');
    row++;

    // Number of sectors
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('عدد القطاعات:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('${sectorDistribution.length}');
    row += 2;

    // Add detailed records section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('تفاصيل السجلات');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true, fontSize: 14);
    row += 2;

    // Add headers for detailed records
    final headers = [
      'اللوحة',
      'الموديل',
      'القطاع',
      'الحالة',
      'التاريخ',
      'الملاحظات',
      'نوع الصيانة',
      'تم التحديث بواسطة'
    ];

    for (int i = 0; i < headers.length; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(headers[i]);
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(bold: true);
    }
    row++;

    // Add data rows
    for (final entry in entries) {
      final rowData = [
        '${entry.car.plateNumber}-${entry.car.plateCharacters}',
        entry.car.carModel,
        entry.car.sectorName,
        entry.status.status.toString().split('.').last,
        DateFormat('yyyy-MM-dd HH:mm').format(entry.status.createAt),
        entry.status.notes ?? '',
        entry.workshopEntry.maintenance_type ?? '',
        entry.status.senderName,
      ];

      for (int j = 0; j < rowData.length; j++) {
        mainSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row))
            .value = TextCellValue(rowData[j]);
      }
      row++;
    }

    row += 2;

    // Add status distribution section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('توزيع الحالات');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true, fontSize: 14);
    row += 2;

    // Status distribution headers
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الحالة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true);

    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('العدد');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .cellStyle = CellStyle(bold: true);
    row++;

    // Status distribution data
    for (final status in statusDistribution) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(status.status.toString().split('.').last);
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(status.count.toString());
      row++;
    }

    row += 2;

    // Add sector distribution section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('توزيع القطاعات');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true, fontSize: 14);
    row += 2;

    // Sector distribution headers
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('القطاع');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true);

    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('العدد');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .cellStyle = CellStyle(bold: true);
    row++;

    // Sector distribution data
    for (final sector in sectorDistribution) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(sector.sectorName);
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(sector.count.toString());
      row++;
    }

    // Save with UTF-8 encoding for proper Arabic support
    return Uint8List.fromList(excel.save()!);
  }

  /// Generate a car report Excel
  Future<Uint8List> generateCarReport({
    required Car car,
    required List<WorkshopReportEntry> entries,
    required List<StatusCount> statusDistribution,
  }) async {
    // Create a new Excel document with UTF-8 encoding for Arabic support
    final excel = Excel.createExcel();

    // Get the default sheet name
    final sheetName = excel.getDefaultSheet() ?? 'Sheet1';
    final mainSheet = excel[sheetName];

    // Set column widths for better readability
    for (int i = 0; i < 8; i++) {
      mainSheet.setColumnWidth(i, 18);
    }

    // Add title with Arabic text
    final titleCell =
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = TextCellValue(
        'تقرير المركبة - ${car.plateNumber}-${car.plateCharacters}');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for title
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0),
        CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0));

    // Add car details section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2))
        .value = TextCellValue('معلومات المركبة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2))
        .cellStyle = CellStyle(bold: true, fontSize: 14);

    // Car details
    int row = 4;

    // Model
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الموديل:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(car.carModel);
    row++;

    // Sector
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('القطاع:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(car.sectorName);
    row++;

    // Type
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('النوع:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(car.carType);
    row++;

    // Status
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الحالة:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(car.status.toString().split('.').last);
    row++;

    // Creation date
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('تاريخ الإنشاء:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(DateFormat('yyyy-MM-dd').format(car.createAt));
    row++;

    // Workshop entries count
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('عدد سجلات الورشة:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue(car.workshopHistory.length.toString());
    row += 2;

    // Add status distribution section
    if (statusDistribution.isNotEmpty) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue('توزيع الحالات');
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .cellStyle = CellStyle(bold: true, fontSize: 14);
      row += 2;

      // Status distribution headers
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue('الحالة');
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .cellStyle = CellStyle(bold: true);

      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue('العدد');
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .cellStyle = CellStyle(bold: true);

      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue('النسبة');
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .cellStyle = CellStyle(bold: true);
      row++;

      // Calculate total
      final total =
      statusDistribution.fold<int>(0, (sum, status) => sum + status.count);

      // Status distribution data
      for (final status in statusDistribution) {
        final percentage = (status.count / total * 100).toStringAsFixed(1);

        mainSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
            .value = TextCellValue(status.status.toString().split('.').last);
        mainSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
            .value = TextCellValue(status.count.toString());
        mainSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
            .value = TextCellValue('$percentage%');
        row++;
      }

      row += 2;
    }

    // Add workshop entries section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('سجلات الورشة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(bold: true, fontSize: 14);
    row += 2;

    // Workshop entries headers
    final headers = [
      'رقم',
      'نوع الصيانة',
      'الحالة',
      'التاريخ',
      'الملاحظات',
      'بواسطة'
    ];

    for (int i = 0; i < headers.length; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(headers[i]);
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(bold: true);
    }
    row++;

    // Workshop entries data
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final rowData = [
        (i + 1).toString(),
        entry.workshopEntry.maintenance_type ?? 'غير محدد',
        entry.status.status.toString().split('.').last,
        DateFormat('yyyy-MM-dd HH:mm').format(entry.status.createAt),
        entry.status.notes ?? '',
        entry.status.senderName,
      ];

      for (int j = 0; j < rowData.length; j++) {
        mainSheet
            .cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row))
            .value = TextCellValue(rowData[j]);
      }
      row++;
    }

    // Save with UTF-8 encoding for proper Arabic support
    return Uint8List.fromList(excel.save()!);
  }

  /// Generate a status summary report Excel
  Future<Uint8List> generateStatusSummaryReport({
    required Map<CarStatus, List<Car>> carsByStatus,
    required DateTime startDate,
    required DateTime endDate,
    String? sectorName,
  }) async {
    // Create a new Excel document with UTF-8 encoding for Arabic support
    final excel = Excel.createExcel();

    // Get the default sheet name
    final sheetName = excel.getDefaultSheet() ?? 'Sheet1';
    final mainSheet = excel[sheetName];

    // Set column widths for better readability
    for (int i = 0; i < 8; i++) {
      mainSheet.setColumnWidth(i, 18);
    }

    // Add title with Arabic text
    final titleCell =
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = TextCellValue('تقرير ملخص حالات المركبات');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for title
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0),
        CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0));

    // Add date range
    final dateRangeCell =
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1));
    dateRangeCell.value = TextCellValue(
        'الفترة: ${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}');
    dateRangeCell.cellStyle = CellStyle(
      fontSize: 12,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for date range
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1),
        CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 1));

    // Add sector if provided
    if (sectorName != null && sectorName.isNotEmpty) {
      final sectorCell = mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2));
      sectorCell.value = TextCellValue('القطاع: $sectorName');
      sectorCell.cellStyle = CellStyle(
        fontSize: 12,
        horizontalAlign: HorizontalAlign.Center,
      );

      // Merge cells for sector
      mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2),
          CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 2));
    }

    // Add summary section
    int row = sectorName != null && sectorName.isNotEmpty ? 4 : 3;

    // Create a header for the summary section
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('ملخص الإحصائيات');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
    );

    // Merge cells for the header
    mainSheet.merge(
      CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row),
      CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row),
    );
    row += 2;

    // Create a styled summary table
    // Headers
    final summaryHeaders = ['الحالة', 'العدد', 'النسبة المئوية'];
    for (int i = 0; i < summaryHeaders.length; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .value = TextCellValue(summaryHeaders[i]);
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        bold: true,
        fontSize: 12,
      );
    }
    row++;

    // Calculate total cars
    final activeCount = carsByStatus[CarStatus.active]?.length ?? 0;
    final sentRequestCount = carsByStatus[CarStatus.sentRequest]?.length ?? 0;
    final pendingCount = carsByStatus[CarStatus.pending]?.length ?? 0;
    final doneCount = carsByStatus[CarStatus.done]?.length ?? 0;
    final totalCars = activeCount + sentRequestCount + pendingCount+doneCount;

    // Active cars
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('المركبات النشطة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('$activeCount');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
        .value =
        TextCellValue(totalCars > 0
            ? '${(activeCount / totalCars * 100).toStringAsFixed(1)}%'
            : '0%');

    // Style the active cars row
    for (int i = 0; i < 3; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        fontSize: 11,
        bold: i == 0, // Make the status name bold
      );
    }
    row++;

    // Sent request cars
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('المركبات المعطلة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('$sentRequestCount');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
        .value =
        TextCellValue(totalCars > 0
            ? '${(sentRequestCount / totalCars * 100).toStringAsFixed(1)}%'
            : '0%');

    // Style the sent request cars row
    for (int i = 0; i < 3; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        fontSize: 11,
        bold: i == 0, // Make the status name bold
      );
    }
    row++;

    // Pending cars
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('في الورشة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('$pendingCount');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
        .value =
        TextCellValue(totalCars > 0
            ? '${(pendingCount / totalCars * 100).toStringAsFixed(1)}%'
            : '0%');

    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('خرجت من الورشة');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('$doneCount');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
        .value =
        TextCellValue(totalCars > 0
            ? '${(doneCount / totalCars * 100).toStringAsFixed(1)}%'
            : '0%');

    // Style the pending cars row
    for (int i = 0; i < 3; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        fontSize: 11,
        bold: i == 0, // Make the status name bold
      );
    }
    row++;

    // Total row
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الإجمالي');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value = TextCellValue('$totalCars');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
        .value = TextCellValue('100%');

    // Style the total row
    for (int i = 0; i < 3; i++) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row))
          .cellStyle = CellStyle(
        fontSize: 11,
        bold: true, // Make all cells in the total row bold
      );
    }
    row += 3;

    // Add date range information
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('معلومات التقرير');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 12,
    );
    row++;

    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('الفترة:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value =
        TextCellValue(
            '${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}');
    row++;

    if (sectorName != null && sectorName.isNotEmpty) {
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue('القطاع:');
      mainSheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(sectorName);
      row++;
    }

    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
        .value = TextCellValue('تاريخ إنشاء التقرير:');
    mainSheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
        .value =
        TextCellValue(DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()));
    row += 2;

    // No detailed tables as per user request - only showing summary numbers

    // Save with UTF-8 encoding for proper Arabic support
    return Uint8List.fromList(excel.save()!);
  }

  /// Generate a workshop entries report Excel
  Future<Uint8List> generateWorkshopEntriesReport({
    required List<Map<String, dynamic>> entries,
    required DateTime startDate,
    required DateTime endDate,
    String? sectorName,
  }) async {
    // Sort entries by date (newest first)
    final sortedEntries = List<Map<String, dynamic>>.from(entries)
      ..sort((a, b) => (b['startDate'] as DateTime).compareTo(a['startDate'] as DateTime));

    // Create a new Excel document with UTF-8 encoding for Arabic support
    final excel = Excel.createExcel();

    // Get the default sheet name
    final sheetName = excel.getDefaultSheet() ?? 'Sheet1';
    final mainSheet = excel[sheetName];

    // Set column widths for better readability
    mainSheet.setColumnWidth(0, 15); // Status 1
    mainSheet.setColumnWidth(1, 15); // Status 2
    mainSheet.setColumnWidth(2, 15); // Status 3
    mainSheet.setColumnWidth(3, 15); // Car Plate
    mainSheet.setColumnWidth(4, 20); // Car Model
    mainSheet.setColumnWidth(5, 15); // Sector
    mainSheet.setColumnWidth(6, 20); // Maintenance Type
    mainSheet.setColumnWidth(7, 15); // Start Date
    mainSheet.setColumnWidth(8, 10); // Duration (days)

    // Add title
    final titleCell = mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = TextCellValue('تقرير سجلات الورشة');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for title
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0),
        CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: 0));

    // Add date range
    final dateRangeCell = mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1));
    dateRangeCell.value = TextCellValue(
        'الفترة: ${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}');
    dateRangeCell.cellStyle = CellStyle(
      fontSize: 12,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for date range
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1),
        CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: 1));

    // Add headers
    final headers = [
      'طلب صيانة',
      'قيد الصيانة',
      'نشط',
      'اللوحة',
      'الموديل',
      'القطاع',
      'نوع الصيانة',
      'تاريخ البدء',
      'المدة (أيام)',
    ];

    // Add headers with styling
    for (int i = 0; i < headers.length; i++) {
      final cell = mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 3));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        fontSize: 12,
        // backgroundColor: ExcelColor.blue,
        // fontColor: ExcelColor.white,
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    // Add data
    for (var i = 0; i < sortedEntries.length; i++) {
      final entry = sortedEntries[i];
      final row = i + 4; // Start from row 4 (after title, date range, and headers)

      // Status cells
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = TextCellValue(entry['isSentRequest'] ? '1' : '0');
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(entry['isPending'] ? '1' : '0');
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(entry['isActive'] ? '1' : '0');

      // Other data cells
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(entry['carPlate']);
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = TextCellValue(entry['carModel']);
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue(entry['sector']);
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = TextCellValue(entry['maintenanceType']);
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = TextCellValue(DateFormat('yyyy-MM-dd').format(entry['startDate']));
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = TextCellValue(entry['duration'].toString());

      // Center align all cells
      for (int j = 0; j < headers.length; j++) {
        mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: row))
            .cellStyle = CellStyle(horizontalAlign: HorizontalAlign.Center);
      }
    }

    // Add summary information
    final lastRow = sortedEntries.length + 4;
    final summaryRow = lastRow + 2;

    // Summary title
    final summaryTitleCell = mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow));
    summaryTitleCell.value = TextCellValue('ملخص التقرير');
    summaryTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      horizontalAlign: HorizontalAlign.Center,
    );

    // Merge cells for summary title
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow),
        CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: summaryRow));

    // Date range
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow + 1))
        .value = TextCellValue('الفترة:');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: summaryRow + 1))
        .value = TextCellValue('${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}');

    // Sector if provided
    if (sectorName != null && sectorName.isNotEmpty) {
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow + 2))
          .value = TextCellValue('القطاع:');
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: summaryRow + 2))
          .value = TextCellValue(sectorName);
    }

    // Number of records
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow + 3))
        .value = TextCellValue('عدد السجلات:');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: summaryRow + 3))
        .value = TextCellValue(sortedEntries.length.toString());

    // Calculate and add average duration
    final totalDuration = sortedEntries.fold<int>(0, (sum, entry) => sum + (entry['duration'] as int));
    final averageDuration = sortedEntries.isEmpty ? 0 : totalDuration / sortedEntries.length;
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryRow + 4))
        .value = TextCellValue('متوسط المدة (أيام):');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: summaryRow + 4))
        .value = TextCellValue(averageDuration.toStringAsFixed(1));

    // Save with UTF-8 encoding for proper Arabic support
    return Uint8List.fromList(excel.save()!);
  }

  /// Generate an advanced workshop report Excel
  Future<Uint8List> generateAdvancedWorkshopReport({
    required List<Car> cars,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final excel = Excel.createExcel();
    final sheetName = excel.getDefaultSheet() ?? 'Sheet1';
    final mainSheet = excel[sheetName];

    // Set column widths
    for (int i = 0; i < 8; i++) {
      mainSheet.setColumnWidth(i, 18);
    }

    // Title
    final titleCell = mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = TextCellValue('تقرير متقدم للورشة - ${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}');
    titleCell.cellStyle = CellStyle(bold: true, fontSize: 16, horizontalAlign: HorizontalAlign.Center);
    mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0), CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0));

    // Summary
    int row = 2;
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('عدد المركبات:');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = TextCellValue('${cars.length}');
    row++;
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('إجمالي سجلات الورشة:');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = TextCellValue('${cars.fold(0, (sum, car) => sum + car.workshopHistory.length)}');
    row++;
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('الفترة:');
    mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = TextCellValue('${DateFormat('yyyy-MM-dd').format(startDate)} - ${DateFormat('yyyy-MM-dd').format(endDate)}');
    row += 2;

    // For each car, add a section
    for (final car in cars.where((c) => c.workshopHistory.isNotEmpty)) {
      // Car header
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('المركبة: ${car.plateNumber}-${car.plateCharacters} | ${car.carModel} | ${car.sectorName}');
      mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).cellStyle = CellStyle(bold: true, fontSize: 12);
      mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row), CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row));
      row++;
      // Table headers
      final headers = ['#', 'نوع الصيانة', 'الحالة', 'تاريخ البدء', 'المدة (أيام)', 'الملاحظات'];
      for (int i = 0; i < headers.length; i++) {
        mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row)).value = TextCellValue(headers[i]);
        mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row)).cellStyle = CellStyle(bold: true);
      }
      row++;
      if (car.workshopHistory.isNotEmpty) {
        for (int i = 0; i < car.workshopHistory.length; i++) {
          final entry = car.workshopHistory[i];
          final startDateEntry = entry.statuses.isNotEmpty ? entry.statuses.first.createAt : null;
          final endDateEntry = entry.statuses.isNotEmpty ? entry.statuses.last.createAt : null;
          final duration = (startDateEntry != null && endDateEntry != null) ? endDateEntry.difference(startDateEntry).inDays : 0;
          final lastStatus = entry.statuses.isNotEmpty ? entry.statuses.last.status.toString().split('.').last : '';
          final notes = entry.statuses.isNotEmpty ? (entry.statuses.last.notes ?? '') : '';
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue((i + 1).toString());
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = TextCellValue(entry.maintenance_type ?? 'غير محدد');
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = TextCellValue(lastStatus);
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = TextCellValue(startDateEntry != null ? DateFormat('yyyy-MM-dd').format(startDateEntry) : '');
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = TextCellValue(duration.toString());
          mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = TextCellValue(notes);
          row++;
        }
      } else {
        mainSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('لا توجد سجلات ورشة لهذه المركبة');
        mainSheet.merge(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row), CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row));
        row++;
      }
      row++;
    }
    // Save with UTF-8 encoding for proper Arabic support
    return Uint8List.fromList(excel.save()!);
  }
}
