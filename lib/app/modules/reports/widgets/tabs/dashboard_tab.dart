import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../../data/models/car_model.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';
import '../charts/daily_activity_chart.dart';
import '../charts/sector_distribution_chart.dart';
import '../charts/status_distribution_chart.dart';

class DashboardTab extends StatelessWidget {
  final ReportsController controller;

  const DashboardTab({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return _buildLoadingState();
      }

      if (controller.filteredEntries.isEmpty) {
        return _buildEmptyState();
      }

      return _buildDashboard(context);
    });
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 100,
            height: 100,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              strokeWidth: 6,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جاري تحميل البيانات...'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Lottie.network(
            'https://assets9.lottiefiles.com/packages/lf20_wnqlfojb.json',
            width: 200,
            height: 200,
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد بيانات متاحة للعرض'.tr,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'حاول تغيير معايير التصفية للعثور على بيانات'.tr,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.clearFilters(),
            icon: const Icon(Icons.refresh),
            label: Text('إعادة ضبط الفلاتر'.tr),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatCards(),
          const SizedBox(height: 24),

          // Responsive layout for charts
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth > 900) {
                // Desktop layout - 2 columns
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          StatusDistributionChart(controller: controller),
                          const SizedBox(height: 24),
                          SectorDistributionChart(controller: controller),
                        ],
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: DailyActivityChart(controller: controller),
                    ),
                  ],
                );
              } else {
                // Mobile layout - stacked
                return Column(
                  children: [
                    StatusDistributionChart(controller: controller),
                    const SizedBox(height: 24),
                    DailyActivityChart(controller: controller),
                    const SizedBox(height: 24),
                    SectorDistributionChart(controller: controller),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatCards() {
    return Obx(() {
      // Calculate stats
      final totalEntries = controller.filteredEntries.length;
      final pendingEntries = controller.filteredEntries
          .where((e) => e.status.status == CarStatus.pending)
          .length;
      final completedEntries = controller.filteredEntries
          .where((e) => e.status.status == CarStatus.done)
          .length;
      final rejectedEntries = controller.filteredEntries
          .where((e) => e.status.status == CarStatus.rejected)
          .length;

      return LayoutBuilder(
        builder: (context, constraints) {
          if (constraints.maxWidth > 700) {
            // Desktop layout - row of cards
            return Row(
              children: [
                Expanded(
                    child: _buildStatCard('إجمالي السجلات'.tr, totalEntries,
                        Icons.summarize, AppColors.primary)),
                const SizedBox(width: 16),
                Expanded(
                    child: _buildStatCard('قيد المعالجة'.tr, pendingEntries,
                        Icons.pending, Colors.orange)),
                const SizedBox(width: 16),
                Expanded(
                    child: _buildStatCard('مكتمل'.tr, completedEntries,
                        Icons.check_circle, Colors.green)),
                const SizedBox(width: 16),
                Expanded(
                    child: _buildStatCard(
                        'مرفوض'.tr, rejectedEntries, Icons.cancel, Colors.red)),
              ],
            );
          } else {
            // Mobile layout - grid of cards
            return GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildStatCard('إجمالي السجلات'.tr, totalEntries,
                    Icons.summarize, AppColors.primary),
                _buildStatCard('قيد المعالجة'.tr, pendingEntries, Icons.pending,
                    Colors.orange),
                _buildStatCard('مكتمل'.tr, completedEntries, Icons.check_circle,
                    Colors.green),
                _buildStatCard(
                    'مرفوض'.tr, rejectedEntries, Icons.cancel, Colors.red),
              ],
            );
          }
        },
      );
    });
  }

  Widget _buildStatCard(String title, int value, IconData icon, Color color) {
    List<Color> gradientColors;

    if (title == 'إجمالي السجلات'.tr) {
      gradientColors = AppColors.totalGradient;
    } else if (title == 'قيد المعالجة'.tr) {
      gradientColors = AppColors.pendingGradient;
    } else if (title == 'مكتمل'.tr) {
      gradientColors = AppColors.doneGradient;
    } else if (title == 'مرفوض'.tr) {
      gradientColors = AppColors.rejectedGradient;
    } else {
      gradientColors = [color, color.withOpacity(0.7)];
    }

    return Card(
      elevation: 8,
      shadowColor: gradientColors[0].withOpacity(0.4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradientColors,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              value.toString(),
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
