import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/user_model.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/awesome_snackbar_content.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

class UserController extends GetxController {
  final users = <UserModel>[].obs;
  final isLoading = false.obs;
  final selectedRole = Rx<UserRole?>(null);
  final selectedStatus = Rx<UserStatus?>(null);
  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final searchQuery = ''.obs;
  final isFilterVisible = true.obs;
  final isSearchVisible = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadUsers();
    // TODO: Replace with actual authentication
    currentUser.value = UserModel(
      id: 'temp_user',
      username: 'admin',
      name: 'Administrator',
      password: '',
      role: UserRole.admin,
      status: UserStatus.active,
      createdAt: DateTime.now(),
    );


    // createUser('100643','عبدالعزيز صالح سلمان السناني', '12345678', UserRole.technician);
    // createUser('102081','علي حمدان حميد العمري', '12345678', UserRole.technician);
    // createUser('102080','حامد مطير حامد البلوي', '12345678', UserRole.technician);
    // createUser('101607','زياد محبوب سالم القحطاني', '12345678', UserRole.technician);
    // createUser('101617','تركي صالح عباس عامودي', '12345678', UserRole.technician);
    // createUser('102144','محمد وصل الله عرميط العمري', '12345678', UserRole.technician);

    // createUser('101549','محمد بن منصور بن درويش الريس', '12345678', UserRole.technician);
    // createUser('101423','احمد حامد احمد العبدلي', '12345678', UserRole.supervisor);
    // createUser('102669','يوسف عبدالجواد عبدالمحسن النخلي', '12345678', UserRole.supervisor);

  }

  Future<void> loadUsers() async {
    try {
      isLoading.value = true;
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('users')
          .get();

      users.value = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
      
      print('Loaded ${users.length} users');
    } catch (e) {
      print('Error loading users: $e');
      Get.snackbar('Error', 'Failed to load users: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createUser(String username,String name, String password, UserRole role,
      {String? email, String? phoneNumber}) async {
    try {
      isLoading.value = true;
      
      // Validate email
      String userEmail = email ?? '';
      if (userEmail.isEmpty) {
        // If no email provided, use <EMAIL> format
        userEmail = '$<EMAIL>';
      }
      
      // Get auth controller instance
      final AuthController authController = Get.find<AuthController>();
      
      // Create user with Firebase Auth and Firestore but don't redirect admin
      final result = await authController.registerUserAsAdmin(
        username,
        name,
        userEmail,
        password,
        role,
      );
      
      if (result['success']) {
        // If phone number was provided, update the user
        if (phoneNumber != null && phoneNumber.isNotEmpty) {
          // Find the newly created user
          final userQuery = await FirebaseFirestore.instance
              .collection('users')
              .where('username', isEqualTo: username)
              .limit(1)
              .get();
              
          if (userQuery.docs.isNotEmpty) {
            final userId = userQuery.docs.first.id;
            await FirebaseFirestore.instance
                .collection('users')
                .doc(userId)
                .update({'phoneNumber': phoneNumber});
          }
        }
        
        await loadUsers();
        showCustomSnackBar(
          title: 'تم بنجاح',
          message: 'تم إنشاء المستخدم بنجاح',
          contentType: ContentType.success,
        );
      } else {
        showCustomSnackBar(
          title: 'خطأ',
          message: result['message'] ?? 'فشل في إنشاء المستخدم',
          contentType: ContentType.failure,
        );
      }
    } catch (e) {
      print('Error creating user: $e');
      showCustomSnackBar(
        title: 'خطأ',
        message: 'فشل في إنشاء المستخدم: $e',
        contentType: ContentType.failure,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateUser(String userId, {
    String? username,
    String? name,
    String? password,
    UserRole? role,
    UserStatus? status,
    String? email,
    String? phoneNumber,
  }) async {
    try {
      isLoading.value = true;
      
      // Get the current user data
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();
          
      if (!userDoc.exists) {
        throw Exception('User not found');
      }
      
      final userData = userDoc.data()!;
      final firebaseUid = userData['firebaseUid'];
      final currentEmail = userData['email'] as String?;
      
      // Prepare Firestore updates
      final updates = <String, dynamic>{
        if (username != null) 'username': username,
        if (name != null) 'name': name,
        if (role != null) 'role': role.toString().split('.').last,
        if (status != null) 'status': status.toString().split('.').last,
        if (email != null) 'email': email,
        if (phoneNumber != null) 'phoneNumber': phoneNumber,
      };
      
      // Update in Firestore
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .update(updates);
      
      // If Firebase UID exists and password or email was changed, update in Firebase Auth
      if (firebaseUid != null) {
        try {
          // Get Firebase Auth instance
          final auth = FirebaseAuth.instance;
          
          // If password was changed
          if (password != null && password.isNotEmpty) {
            // We need admin SDK or custom functions to update password for other users
            // This is a simplified approach for demo purposes
            // In production, use Firebase Admin SDK or Cloud Functions
            showCustomSnackBar(
              title: 'تنبيه',
              message: 'لتغيير كلمة المرور، يرجى استخدام وظيفة إعادة تعيين كلمة المرور',
              contentType: ContentType.warning,
            );
          }
          
          // If email was changed and we have the current email
          if (email != null && email != currentEmail && currentEmail != null) {
            // Email updates should be handled with care
            // In production, use Firebase Admin SDK or Cloud Functions
            showCustomSnackBar(
              title: 'تنبيه',
              message: 'تم تحديث البريد الإلكتروني في Firestore فقط. للتحديث في Firebase Auth، استخدم وظيفة إعادة تعيين كلمة المرور',
              contentType: ContentType.warning,
            );
          }
        } catch (authError) {
          print('Error updating Firebase Auth: $authError');
        }
      }
      
      await loadUsers();
      showCustomSnackBar(
        title: 'تم بنجاح',
        message: 'تم تحديث المستخدم بنجاح',
        contentType: ContentType.success,
      );
    } catch (e) {
      print('Error updating user: $e');
      showCustomSnackBar(
        title: 'خطأ',
        message: 'فشل في تحديث المستخدم: $e',
        contentType: ContentType.failure,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      isLoading.value = true;
      
      // Get the user data to check for Firebase UID
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();
          
      if (!userDoc.exists) {
        throw Exception('User not found');
      }
      
      final userData = userDoc.data()!;
      final firebaseUid = userData['firebaseUid'];
      
      // Delete from Firestore first
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .delete();
      
      // If Firebase UID exists, attempt to delete the user from Firebase Auth
      if (firebaseUid != null) {
        try {
          // Get Firebase Auth instance
          final auth = FirebaseAuth.instance;
          
          // Get the current user
          final currentUser = auth.currentUser;
          
          // If the user is trying to delete themselves, we need special handling
          if (currentUser != null && currentUser.uid == firebaseUid) {
            // Re-authenticate may be required for sensitive operations
            // For now, we'll just show a warning
            showCustomSnackBar(
              title: 'تنبيه',
              message: 'لا يمكن حذف الحساب الحالي. يرجى تسجيل الخروج أولاً.',
              contentType: ContentType.warning,
            );
          } else {
            // For deleting other users, we need admin privileges
            // This is a simplified approach - in production, use Firebase Admin SDK or Cloud Functions
            // We'll use a custom auth controller method to delete the user
            final AuthController authController = Get.find<AuthController>();
            final result = await authController.deleteUserAuth(firebaseUid);
            
            if (result['success']) {
              showCustomSnackBar(
                title: 'تم بنجاح',
                message: 'تم حذف المستخدم بنجاح من Firestore و Firebase Auth',
                contentType: ContentType.success,
              );
            } else {
              showCustomSnackBar(
                title: 'تنبيه',
                message: 'تم حذف المستخدم من Firestore ولكن فشل حذفه من Firebase Auth: ${result['message']}',
                contentType: ContentType.warning,
              );
            }
          }
        } catch (authError) {
          print('Error deleting Firebase Auth user: $authError');
          showCustomSnackBar(
            title: 'تنبيه',
            message: 'تم حذف المستخدم من Firestore ولكن فشل حذفه من Firebase Auth: $authError',
            contentType: ContentType.warning,
          );
        }
      } else {
        showCustomSnackBar(
          title: 'تم بنجاح',
          message: 'تم حذف المستخدم بنجاح',
          contentType: ContentType.success,
        );
      }
      
      await loadUsers();
    } catch (e) {
      print('Error deleting user: $e');
      showCustomSnackBar(
        title: 'خطأ',
        message: 'فشل في حذف المستخدم: $e',
        contentType: ContentType.failure,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateUserStatus(String userId, UserStatus newStatus) async {
    await updateUser(userId, status: newStatus);
  }

  Future<void> updateUserRole(String userId, UserRole newRole) async {
    await updateUser(userId, role: newRole);
  }

  List<UserModel> get filteredUsers {
    return users.where((user) {
      bool matchesRole = selectedRole.value == null || user.role == selectedRole.value;
      bool matchesStatus = selectedStatus.value == null || user.status == selectedStatus.value;
      bool matchesSearch = searchQuery.isEmpty || 
          user.name.toLowerCase().contains(searchQuery.value.toLowerCase()) || 
          user.username.toLowerCase().contains(searchQuery.value.toLowerCase()) || 
          (user.phoneNumber != null && user.phoneNumber!.contains(searchQuery.value));
      return matchesRole && matchesStatus && matchesSearch;
    }).toList();
  }
}
