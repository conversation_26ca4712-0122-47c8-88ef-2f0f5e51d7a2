import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/car_model.dart';
import '../themes/app_colors.dart';
import 'package:intl/intl.dart';

class WorkshopHistoryTimeline extends StatelessWidget {
  final List<WorkshopEntry> entries;
  final bool showDriverInfo;

  const WorkshopHistoryTimeline({
    Key? key,
    required this.entries,
    this.showDriverInfo = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];
        final isLast = index == entries.length - 1;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'workshop_entry'.tr + ' #${index + 1}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(

                      DateFormat('dd/MM/yyyy', Get.locale?.languageCode)
                          .format(entry.statuses.first.createAt??DateTime.now()),
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStatusTimeline(entry.statuses),
                if (showDriverInfo) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: AppColors.primary,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'technician'.tr,
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            entry.senderId ?? 'unknown'.tr,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusTimeline(List<WorkshopEntryStatus> statuses) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: statuses.length,
      itemBuilder: (context, index) {
        final status = statuses[index];
        final isLast = index == statuses.length - 1;
        final isActive = index == statuses.length - 1;

        return IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 24,
                child: Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: isActive ? AppColors.success : AppColors.textLight,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isActive ? AppColors.success : Colors.grey[300]!,
                          width: 2,
                        ),
                      ),
                    ),
                    if (!isLast)
                      Expanded(
                        child: Container(
                          width: 2,
                          color: Colors.grey[300],
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusLabel(status.status),
                      style: TextStyle(
                        fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                        color: isActive ? AppColors.textPrimary : AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      DateFormat('yyyy-MM-dd HH:mm', Get.locale?.languageCode).format(status.createAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (status.notes?.isNotEmpty == true) ...[
                      const SizedBox(height: 4),
                      Text(
                        status.notes!,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    if (status.senderName?.isNotEmpty == true) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${'by'.tr}: ${status.senderName}',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                    if (!isLast) const SizedBox(height: 16),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getStatusLabel(CarStatus status) {
    switch (status) {
      case CarStatus.sentRequest:
        return 'sent_request'.tr;
      case CarStatus.receipt:
        return 'receipt'.tr;
      case CarStatus.callToWorkshop:
        return 'call_workshop'.tr;
      case CarStatus.pending:
        return 'pending'.tr;
      case CarStatus.done:
        return 'done'.tr;
      default:
        return status.toString().split('.').last.tr;
    }
  }
}
