import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../themes/app_colors.dart';

class ConnectionUtils {
  static final Connectivity _connectivity = Connectivity();
  static final RxBool isConnected = true.obs;
  static StreamSubscription<ConnectivityResult>? _subscription;

  // Initialize connection monitoring
  static void initConnectionListener() {
    _subscription = _connectivity.onConnectivityChanged.listen((result) {
      isConnected.value = (result != ConnectivityResult.none);
    });
    
    // Check initial connection state
    checkConnection();
  }

  // Check current connection status
  static Future<bool> checkConnection() async {
    try {
      final result = await _connectivity.checkConnectivity();
      isConnected.value = (result != ConnectivityResult.none);
      return isConnected.value;
    } catch (e) {
      isConnected.value = false;
      return false;
    }
  }

  // Dispose connection listener
  static void disposeConnectionListener() {
    _subscription?.cancel();
  }

  // Show no connection banner
  static Widget buildNoConnectionBanner() {
    return Obx(() => !isConnected.value
        ? Container(
            width: double.infinity,
            color: Colors.red,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.wifi_off, color: Colors.white),
                SizedBox(width: 8),
                Text(
                  'لا يوجد اتصال بالإنترنت',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink());
  }

  // Show a full screen no connection view
  static Widget buildNoConnectionView({VoidCallback? onRetry}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.wifi_off,
            size: 80,
            color: AppColors.primary,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا يوجد اتصال بالإنترنت',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          if (onRetry != null)
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
        ],
      ),
    );
  }
}
