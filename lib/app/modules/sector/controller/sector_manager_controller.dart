import 'package:cars_app/app/controllers/auth_controller.dart';
import 'package:cars_app/app/services/car_service.dart';
import 'package:cars_app/app/services/user_storage_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/sector_model.dart';
import '../../../data/models/user_model.dart';
import '../../../services/firestore_service.dart';

class SectorManagerController extends GetxController {
  final cars = <Car>[].obs;
  final sector = Rxn<Sector>();
  final reasonController = TextEditingController();
  final searchController = TextEditingController();
  final isLoading = false.obs;
  final isSearching = false.obs;
  final searchQuery = ''.obs;
  final selectedStatus = Rxn<CarStatus>();
  final LocalStorageService localStorageService = LocalStorageService();
  final RxString uid = ''.obs;
  final RxString userName = ''.obs;
  final CarService carService = CarService();
  final RxString selectedSectorId = ''.obs;
  @override
  void onInit() async {
    super.onInit();
    uid.value = (await localStorageService.getUserId())!;
    userName.value = (await localStorageService.getUsername())!;
    selectedSectorId.value = Get.arguments['sectorId']??'';
    loadSectorAndCars();
  }

  Future<void> loadSectorAndCars() async {
    try {
      isLoading.value = true;

      Logger().d(selectedSectorId);
      print('**********************');
      // Get current user
      // final user = FirebaseFirestore.instance.collection('users').doc(authController.uid);
      // final userData = await user.get();

      // if (!userData.exists || userData.data()?['role'] != 'supervisor') {
      //   Get.snackbar('Error', 'Unauthorized access');
      //   Get.back();
      //   return;
      // }
      if(selectedSectorId.value.isNotEmpty){


        final sectorSnapshot = await FirebaseFirestore.instance
            .collection('sectors')
            .doc(selectedSectorId.value)
            .get();


        if (sectorSnapshot.exists == false) {
          Get.snackbar('Error', 'No sector found for this manager');
          return;
        }

        sector.value = Sector.fromJson({
          ...sectorSnapshot.data()!,
          'id': selectedSectorId.value
        });
      }else{
        // Get sector where user is manager
        final sectorSnapshot = await FirebaseFirestore.instance
            .collection('sectors')
            .where('managerId', isEqualTo: uid.value)
            .limit(1)
            .get();


        if (sectorSnapshot.docs.isEmpty) {
          Get.snackbar('Error', 'No sector found for this manager');
          return;
        }

        sector.value = Sector.fromJson({
          ...sectorSnapshot.docs.first.data(),
          'id': sectorSnapshot.docs.first.id
        });


      }



      // Get all cars for this sector
      final carSnapshot = await FirebaseFirestore.instance
          .collection('cars')
          // .where('status', isEqualTo: 'maintenance')
          .where('sectorId', isEqualTo: sector.value!.id)
          .get();

      cars.value = carSnapshot.docs.map((doc) {
        return Car.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
    } catch (e) {
      print('Error loading sector data: $e');
      Get.snackbar('Error', 'Failed to load sector data');
    } finally {
      isLoading.value = false;
    }
  }

  String getCarStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'Active';
      case CarStatus.destroyed:
        return 'Destroyed';
      case CarStatus.maintenance:
        return 'Under Maintenance';
      case CarStatus.inWorkshop:
        return 'In Workshop';
      case CarStatus.outOfService:
        return 'Out of Service';
      default:
        return 'Unknown';
    }
  }

  Stream<List<Car>> getCarsStream() {
    try {
      return FirebaseFirestore.instance
          .collection('cars')
          .where(Filter.or(Filter('sectorId', isEqualTo: sector.value!.id),
              Filter('tempSectorId', isEqualTo: sector.value!.id)))
          .orderBy('lasUpdate', descending: true)
          .snapshots()
          .map((snapshot) {
        final allCars = snapshot.docs.map((doc) {
          return Car.fromJson({...doc.data(), 'id': doc.id});
        }).toList();

        Logger().d(allCars);
        // Apply status filter if selected
        var filteredCars = selectedStatus.value == null
            ? allCars
            : allCars
                .where((car) => car.status == selectedStatus.value)
                .toList();

        // Apply search filter if query exists
        if (searchQuery.value.isNotEmpty) {
          final query = searchQuery.value.toLowerCase();
          filteredCars = filteredCars.where((car) {
            return car.plateNumber.toLowerCase().contains(query) ||
                car.plateCharacters.toLowerCase().contains(query) ||
                car.encoding.toLowerCase().contains(query) ||
                car.carType.toLowerCase().contains(query) ||
                car.carModel.toLowerCase().contains(query);
          }).toList();
        }

        return filteredCars;
      });
    } catch (e) {
      Logger().e(e);
      return Stream.value([]);
    }
  }

  // Alias for getCarsStream to match the view's expectation
  Stream<List<Car>> getFilteredCarsStream() => getCarsStream();

  void setSelectedStatus(CarStatus? status) {
    // If the same status is selected again, clear the filter
    if (selectedStatus.value == status) {
      selectedStatus.value = null;
    } else {
      selectedStatus.value = status;
    }
  }

  // Filter cars by status name (string version)
  void filterByStatusName(String statusName) {
    // Convert string status name to CarStatus enum
    CarStatus? status;

    switch (statusName.toLowerCase()) {
      case 'active':
        status = CarStatus.active;
        break;
      case 'calltoworkshop':
        status = CarStatus.callToWorkshop;
        break;
      case 'pending':
        status = CarStatus.pending;
        break;
      case 'sendgroup':
        status = CarStatus.sendGroup;
        break;
      case 'receipt':
        status = CarStatus.receipt;
        break;
      case 'sentrequest':
        status = CarStatus.sentRequest;
        break;
      case 'done':
        status = CarStatus.done;
        break;
      default:
        status = null;
        break;
    }

    setSelectedStatus(status);
  }

  void toggleSearch() {
    isSearching.value = !isSearching.value;
    if (!isSearching.value) {
      // Clear search when closing search bar
      searchController.clear();
      searchQuery.value = '';
    }
  }

  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  @override
  void onClose() {
    searchController.dispose();
    reasonController.dispose();
    super.onClose();
  }
}
