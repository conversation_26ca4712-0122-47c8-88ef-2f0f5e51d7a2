import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../../modules/inventory/models/product_model.dart';
import '../../modules/inventory/models/transaction_model.dart';

class InventoryFirestoreProvider {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _productsCollection = 'products';
  final String _transactionsCollection = 'transactions';

  // --- Product Methods ---

  Stream<List<ProductModel>> getProductsStream() {
    return _firestore
        .collection(_productsCollection)
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) => ProductModel.fromFirestore(doc)).toList();
    });
    // Add error handling (e.g., .handleError) if needed
  }

  Future<void> addProduct(ProductModel product) async {
    try {
      await _firestore.collection(_productsCollection).add(product.toJson());
    } catch (e) {
      print("Error adding product: $e");
      // Rethrow or handle specific Firestore exceptions
      rethrow;
    }
  }
  Future<void> deleteProduct(String productId) async {
    try {
      await _firestore.collection(_productsCollection).doc(productId).delete();
    } catch (e) {
      print("Error deleting product: $e");
      rethrow;
    }
  }

  Future<void> updateProduct(ProductModel product) async {
    try {
      // Ensure 'updatedAt' is always updated
      // product.['updatedAt'] = FieldValue.serverTimestamp();
      await _firestore.collection(_productsCollection).doc(product.id).update(product.toJson());
    } catch (e) {
      print("Error updating product $product.: $e");
      rethrow;
    }
  }

  // --- Transaction Methods (CRITICAL PART) ---

  /// Atomically updates product quantity and logs the transaction.
  Future<void> recordTransaction({
    required String productId,
    required String productName, // Denormalized
    required TransactionType type,
    required int quantityChange, // Negative for decrease, Positive for increase
    required String userId,
    String? relatedJobId,
    String? notes,
  }) async {
    final productRef = _firestore.collection(_productsCollection).doc(productId);
    final transactionRef = _firestore.collection(_transactionsCollection).doc(); // Auto-generate ID

    return _firestore.runTransaction((transaction) async {
      // 1. Get the current product state
      final productSnapshot = await transaction.get(productRef);

      if (!productSnapshot.exists) {
        throw Exception("Product with ID $productId does not exist!");
      }

      final currentQuantity = productSnapshot.data()?['currentQuantity'] ?? 0;

      // 2. Check for sufficient stock if decreasing quantity
      if (quantityChange < 0 && currentQuantity < quantityChange.abs()) {
        throw Exception("Insufficient stock for product '$productName'. Available: $currentQuantity, Required: ${quantityChange.abs()}");
      }

      // 3. Calculate new quantity
      final newQuantity = currentQuantity + quantityChange;
      final quantityBefore = currentQuantity; // Capture state before change

      // 4. Create the transaction log
      final transactionLog = TransactionModel(
        productId: productId,
        productName: productName,
        type: type,
        quantityChange: quantityChange,
        quantityBefore: quantityBefore,
        quantityAfter: newQuantity,
        timestamp: Timestamp.now(), // Use server timestamp if preferred for consistency
        userId: userId,
        relatedJobId: relatedJobId,
        notes: notes,
      );

      // 5. Update the product quantity atomically
      transaction.update(productRef, {
        'currentQuantity': newQuantity, // Use FieldValue.increment(quantityChange) alternatively
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // 6. Write the transaction log atomically
      transaction.set(transactionRef, transactionLog.toJson());

      print("Transaction recorded successfully for $productName. New Qty: $newQuantity");

    }).catchError((error) {
      print("Firestore Transaction Failed: $error");
      // Rethrow the specific error message for the controller
      throw Exception("Failed to record transaction: $error");
    });
  }

  // --- Transaction History Methods ---
  Stream<List<TransactionModel>> getTransactionHistoryStream({String? productIdFilter}) {
    Query query = _firestore.collection(_transactionsCollection).orderBy('timestamp', descending: true);

    if (productIdFilter != null && productIdFilter.isNotEmpty) {
      query = query.where('productId', isEqualTo: productIdFilter);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => TransactionModel.fromFirestore(doc)).toList();
    });
  }
}