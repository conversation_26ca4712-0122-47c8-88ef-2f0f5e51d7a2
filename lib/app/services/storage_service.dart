import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService extends GetxService {
  late SharedPreferences _prefs;
  
  Future<StorageService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  // String methods
  String? getString(String key) {
    return _prefs.getString(key);
  }

  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  // User related methods
  Future<bool> saveUser(Map<String, dynamic> user) async {
    return await _prefs.setString('user', jsonEncode(user));
  }

  Map<String, dynamic>? getUser() {
    String? userStr = _prefs.getString('user');
    if (userStr != null) {
      return jsonDecode(userStr) as Map<String, dynamic>;
    }
    return null;
  }

  Future<bool> clearUser() async {
    return await _prefs.remove('user');
  }

  // Auth token methods
  Future<bool> saveToken(String token) async {
    return await _prefs.setString('token', token);
  }

  String? getToken() {
    return _prefs.getString('token');
  }

  Future<bool> clearToken() async {
    return await _prefs.remove('token');
  }

  // Theme preference
  Future<bool> saveThemeMode(bool isDark) async {
    return await _prefs.setBool('isDark', isDark);
  }

  bool? getThemeMode() {
    return _prefs.getBool('isDark');
  }

  // Generic methods
  Future<bool> saveString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  Future<bool> saveBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  Future<bool> clear() async {
    return await _prefs.clear();
  }
}
