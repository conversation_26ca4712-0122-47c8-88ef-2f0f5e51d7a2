import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/data/models/vehicle_inspection.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

enum CarStatus {
  active,
  rejected,
  agreeDeliveryToWorkShop,
  destroyed,
  maintenance,
  inWorkshop,
  sentRequest,
  sendToLogisticsSupport,
  sendGroup,
  receipt,
  deliveryToSector,
  callToWorkshop,
  pending,
  done,
  outOfService
}

enum WorkshopStatus { pending, inProgress, maintenance, inWorkshop, completed }

class WorkshopEntryStatus {
  final CarStatus status;
  final DateTime createAt;
  final String? notes;
  final String senderId;
  final String senderName;
  final List<Map<String, dynamic>>?
      usedProducts; // Added field for used products

  WorkshopEntryStatus({
    required this.status,
    required this.createAt,
    this.notes,
    required this.senderId,
    required this.senderName,
    this.usedProducts,
  });

  factory WorkshopEntryStatus.fromJson(Map<String, dynamic> json) {
    return WorkshopEntryStatus(
      status: CarStatus.values
          .firstWhere((e) => e.toString().split('.').last == json['status']),
      createAt: (json['createAt'] as Timestamp).toDate(),
      notes: json['notes'],
      senderId: json['senderId'],
      senderName: json['senderName'],
      usedProducts: json['usedProducts'] != null
          ? List<Map<String, dynamic>>.from(json['usedProducts'])
          : null,
    );
  }
  //toJson

  Map<String, dynamic> toJson() {
    return {
      'status': status.toString().split('.').last,
      'createAt': Timestamp.fromDate(createAt),
      'notes': notes,
      'senderId': senderId,
      'senderName': senderName,
      'usedProducts': usedProducts,
    };
  }
}

class WorkshopEntry {
  final DateTime createAt;
  final String id;
  final String carId;
  final String senderId;
  // Status tracking
  final WorkshopStatus status;
  final VehicleInspection? inspection;
  final bool? isThereIsReplacement;
  final String? maintenance_type;
  final List<String>? problemTagIds; // IDs of problem tags
  List<WorkshopEntryStatus> statuses;

  WorkshopEntry( {
    required this.id,
    required this.createAt,
    required this.carId,
    required this.senderId,
    this.statuses = const [],
    this.inspection,
    // this.createAt,
    this.maintenance_type,
    this.isThereIsReplacement,
    this.problemTagIds,
    required this.status,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'carId': carId,
        'senderId': senderId,
        'createAt': Timestamp.fromDate(createAt ?? DateTime.now()),
        'maintenance_type': maintenance_type,
        'isThereIsReplacement': isThereIsReplacement,
        'problemTagIds': problemTagIds,
        'statuses': statuses.map((e) => e.toJson()).toList(),
        'status': status.toString().split('.').last,
        'inspection': inspection?.toJson(),
      };

  factory WorkshopEntry.fromJson(Map<String, dynamic> json) => WorkshopEntry(
        id: json['id'],
        carId: json['carId'],
        senderId: json['senderId'],
      createAt: json['createAt'] != null
        ? (json['createAt'] as Timestamp).toDate()
        : DateTime.now(),
      statuses: json['statuses'] != null
            ? List<WorkshopEntryStatus>.from(
                json['statuses'].map((x) => WorkshopEntryStatus.fromJson(x)))
            : [],
        inspection: json['inspection'] != null
            ? VehicleInspection.fromJson(json['inspection'])
            : null,
        maintenance_type: json['maintenance_type'],
        isThereIsReplacement: json['isThereIsReplacement'],
        problemTagIds: json['problemTagIds'] != null
            ? List<String>.from(json['problemTagIds'])
            : null,
        status: WorkshopStatus.values.firstWhere(
          (e) => e.toString().split('.').last == json['status'],
          orElse: () => WorkshopStatus.maintenance,
        ),
      );

  WorkshopEntry copyWith({
    String? id,
    String? carId,
    String? senderId,
    String? receiverId,
    WorkshopStatus? status,
    DateTime? maintenanceDate,
    DateTime? inWorkshopDate,
    DateTime? completedDate,
    String? maintenanceNotes,
    String? maintenance_type,
    bool? isThereIsReplacement,
    VehicleInspection? inspection,
    List<String>? problemTagIds,
    String? inWorkshopNotes,
    String? completedNotes,
  }) {
    return WorkshopEntry(
      id: id ?? this.id,
      carId: carId ?? this.carId,
      senderId: senderId ?? this.senderId,
      status: status ?? this.status,
      inspection: inspection ?? this.inspection,
      isThereIsReplacement: isThereIsReplacement ?? this.isThereIsReplacement,
      maintenance_type: maintenance_type ?? this.maintenance_type,
      problemTagIds: problemTagIds ?? this.problemTagIds, createAt: DateTime.now(),
    );
  }
}

enum CarAction {
  restore,
  support,
  none,
}

class Car {
  final String id;
  final String sectorId;
  final String sectorName;
  final String tempSectorId;
  final String tempSectorName;
  final String plateNumber;
  final String plateCharacters;
  final String encoding;
  final String carModel;
  final DateTime? lasUpdate;
  final CarAction? action;
  final String carType;
  final bool isInternal;
  final DateTime createAt;
  final CarStatus status;
  final List<WorkshopEntry> workshopHistory;
  final List<CarRotate> rotations;
  final String? currentWorkshopEntryId;
  final Map<String, dynamic>? metadata;
  final int? meterReading; // Added meter reading field

  Car({
    required this.id,
    required this.sectorId,
    required this.tempSectorId,
    required this.plateNumber,
    required this.plateCharacters,
    required this.encoding,
    required this.carModel,
    this.lasUpdate,
    required this.sectorName,
    required this.tempSectorName,
    required this.carType,
    required this.rotations,
    required this.isInternal,
    required this.createAt,
    required this.status,
    required this.workshopHistory,
    this.currentWorkshopEntryId,
    this.metadata,
    this.action,
    this.meterReading, // Added meter reading parameter
  });

  factory Car.fromJson(Map<String, dynamic> json) {
    return Car(
      id: json['id'] ?? '',
      sectorId: json['sectorId'] ?? '',
      sectorName: json['sectorName'] ?? '',
      tempSectorName: json['tempSectorName'] ?? '',
      action: json['action'] != null
          ? CarAction.values.firstWhere(
              (e) => e.toString().split('.').last == json['action'],
              orElse: () => CarAction.none)
          : CarAction.none,
      tempSectorId: json['tempSectorId'] ?? '',
      plateNumber: json['plateNumber'] ?? '',
      plateCharacters: json['plateCharacters'] ?? '',
      encoding: json['encoding'] ?? '',
      carModel: json['carModel'] ?? '',
      carType: json['carType'] ?? '',
      isInternal: json['isInternal'] ?? false,
      createAt: json['createAt'] != null
          ? (json['createAt'] as Timestamp).toDate()
          : DateTime.now(),
      lasUpdate: json['lasUpdate'] != null
          ? (json['lasUpdate'] as Timestamp).toDate()
          : DateTime.now(),
      status: CarStatus.values.firstWhere(
        (status) =>
            status.toString() == 'CarStatus.${json['status'] ?? 'active'}',
        orElse: () => CarStatus.active,
      ),
      rotations: (json['rotations'] as List<dynamic>? ?? [])
          .map((entry) => CarRotate.fromJson(entry))
          .toList(),
      workshopHistory: (json['workshopEntries'] as List<dynamic>? ?? [])
          .map((entry) => WorkshopEntry.fromJson(entry))
          .toList(),
      currentWorkshopEntryId: json['currentWorkshopEntryId'],
      metadata: json['metadata'],
      meterReading:
          json['meterReading'] != null ? json['meterReading'] as int : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sectorId': sectorId,
      'tempSectorId': tempSectorId,
      'tempSectorName': tempSectorName,
      'sectorName': sectorName,
      'action': action.toString().split('.').last,
      'lasUpdate': FieldValue.serverTimestamp(),
      'plateNumber': plateNumber,
      'plateCharacters': plateCharacters,
      'encoding': encoding,
      'carModel': carModel,
      'rotations': rotations.map((r) => r.toJson()).toList(),
      'carType': carType,
      'isInternal': isInternal,
      'createAt': Timestamp.fromDate(createAt),
      'status': status.toString().split('.').last,
      'workshopEntries': workshopHistory.map((w) => w.toJson()).toList(),
      'currentWorkshopEntryId': currentWorkshopEntryId,
      'metadata': metadata,
      'meterReading': meterReading,
    };
  }

  Car copyWith({
    String? id,
    String? sectorId,
    String? sectorName,
    String? tempSectorId,
    String? plateNumber,
    CarAction? action,
    String? plateCharacters,
    String? encoding,
    String? carModel,
    String? carType,
    bool? isInternal,
    DateTime? createAt,
    CarStatus? status,
    List<WorkshopEntry>? workshopHistory,
    List<CarRotate>? rotations,
    String? currentWorkshopEntryId,
    Map<String, dynamic>? metadata,
    int? meterReading,
  }) {
    return Car(
      id: id ?? this.id,
      sectorId: sectorId ?? this.sectorId,
      tempSectorId: tempSectorId ?? this.tempSectorId,
      plateNumber: plateNumber ?? this.plateNumber,
      plateCharacters: plateCharacters ?? this.plateCharacters,
      encoding: encoding ?? this.encoding,
      carModel: carModel ?? this.carModel,
      carType: carType ?? this.carType,
      lasUpdate: lasUpdate ?? this.lasUpdate,
      sectorName: sectorName ?? this.sectorName,
      action: action ?? this.action,
      tempSectorName: tempSectorName ?? this.tempSectorName,
      rotations: rotations ?? this.rotations,
      isInternal: isInternal ?? this.isInternal,
      createAt: createAt ?? this.createAt,
      status: status ?? this.status,
      workshopHistory: workshopHistory ?? this.workshopHistory,
      currentWorkshopEntryId:
          currentWorkshopEntryId ?? this.currentWorkshopEntryId,
      metadata: metadata ?? this.metadata,
      meterReading: meterReading ?? this.meterReading,
    );
  }
}
