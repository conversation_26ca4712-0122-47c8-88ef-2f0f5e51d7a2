import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../data/models/sector_model.dart';
import '../../../themes/app_colors.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class SectorCard extends StatelessWidget {
  final Sector sector;
  final int carCount;
  final Function()? onTap;
  final Function()? onEdit;
  final Function()? onDelete;
  final Function()? onViewCars;
  final bool isRTL;

  const SectorCard({
    Key? key,
    required this.sector,
    this.carCount = 0,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onViewCars,
    this.isRTL = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(sector.id),
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        dismissible: DismissiblePane(onDismissed: () {}),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppColors.info,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'edit'.tr,
            ),
          if (onViewCars != null)
            SlidableAction(
              onPressed: (_) => onViewCars!(),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              icon: Icons.directions_car,
              label: 'view_cars'.tr,
            ),
        ],
      ),
      endActionPane: onDelete != null
          ? ActionPane(
              motion: const DrawerMotion(),
              children: [
                SlidableAction(
                  onPressed: (_) => onDelete!(),
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                  icon: Icons.delete,
                  label: 'delete'.tr,
                ),
              ],
            )
          : null,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: isRTL
                          ? CrossAxisAlignment.end
                          : CrossAxisAlignment.start,
                      children: [
                        Text(
                          sector.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildInfoItem(
                    Icons.directions_car,
                    '$carCount ${'cars'.tr}',
                    AppColors.info,
                  ),
                  _buildInfoItem(
                    Icons.person,
                    sector.name ?? 'unassigned'.tr,
                    AppColors.warning,
                  ),
                  _buildInfoItem(
                    Icons.location_city,
                    sector.name?? 'unknown'.tr,
                    AppColors.success,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}
