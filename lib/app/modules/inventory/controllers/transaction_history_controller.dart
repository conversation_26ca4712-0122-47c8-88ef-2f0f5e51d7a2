import 'package:get/get.dart';
import '../models/transaction_model.dart';
import '../../../data/providers/inventory_firestore_provider.dart';

class TransactionHistoryController extends GetxController {
  final InventoryFirestoreProvider provider = Get.find<InventoryFirestoreProvider>();

  final RxBool isLoading = true.obs;
  final RxList<TransactionModel> transactions = <TransactionModel>[].obs;
  final RxString error = ''.obs;

  // Optional filter state
  final RxnString productIdFilter = RxnString();
  // TODO: Add filters for date range, type etc.
  // final Rxn<DateTime> dateFromFilter = Rxn<DateTime>();
  // final Rxn<DateTime> dateToFilter = Rxn<DateTime>();
  // final Rxn<TransactionType> typeFilter = Rxn<TransactionType>();


  @override
  void onInit() {
    super.onInit();
    // Use worker to react to filter changes
    ever(productIdFilter, (_) => _bindTransactionsStream());
    // ever(dateFromFilter, (_) => _bindTransactionsStream()); // etc for other filters
    _bindTransactionsStream(); // Initial load
  }

  void _bindTransactionsStream() {
    // Show loading indicator when binding/rebinding
    if (!isLoading.value) isLoading.value = true;
    error.value = ''; // Clear previous errors

    transactions.bindStream(
      // TODO: Update provider method to accept all filters
        provider.getTransactionHistoryStream(
            productIdFilter: productIdFilter.value
          // Pass other filters here: dateFrom: dateFromFilter.value, ...
        ).handleError((err) {
          print("Error binding transaction stream: $err");
          error.value = "Failed to load history.";
          isLoading.value = false;
        })
    );

    // Update loading state only after the first batch of data comes in
    ever(transactions, (_) {
      if (isLoading.value) isLoading.value = false;
      if (error.isNotEmpty) error.value = '';
    });
  }

  // <<<---- ADD THIS METHOD ---->>>
  Future<void> refreshHistory() async {
    _bindTransactionsStream(); // Just re-bind the stream to trigger refresh
  }
  // <<<----------------------- >>>


  void setProductFilter(String? productId) {
    productIdFilter.value = productId; // Worker will trigger _bindTransactionsStream
  }

  // Add methods to set other filters (date, type)

  void clearFilters() {
    productIdFilter.value = null;
    // Clear other filters too
  }
}