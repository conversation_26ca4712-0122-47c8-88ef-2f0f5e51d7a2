import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/maintenance_types.dart';
import '../../../themes/app_colors.dart';
import '../../maintenance/controllers/maintenance_checklist_controller.dart';

class ImprovedMaintenanceChecklist extends StatelessWidget {
  final List<int> selectedItems;
  final Function(int) onItemToggle;
  final bool readOnly;

  const ImprovedMaintenanceChecklist({
    Key? key,
    required this.selectedItems,
    required this.onItemToggle,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'maintenance_items'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                if (!readOnly) ...[
                  TextButton.icon(
                    icon: const Icon(Icons.select_all, size: 16),
                    label: Text('select_all'.tr),
                    onPressed: _selectAll,
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ),
                  TextButton.icon(
                    icon: const Icon(Icons.clear_all, size: 16),
                    label: Text('clear_all'.tr),
                    onPressed: _clearAll,
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ),
                ],
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            _buildChecklistTable(),
          ],
        ),
      ),
    );
  }

  void _selectAll() {
    // Get the controller to set statuses
    final controller = Get.find<MaintenanceChecklistController>();

    for (var item in standardMaintenanceTypes) {
      if (!selectedItems.contains(item.id)) {
        onItemToggle(item.id);
      }
      // Always set status to فحص regardless of previous state
      controller.setItemStatus(item.id, 'فحص');
    }
  }

  void _clearAll() {
    for (var item in standardMaintenanceTypes) {
      if (selectedItems.contains(item.id)) {
        onItemToggle(item.id);
      }
    }
  }

  Widget _buildChecklistTable() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Table(
        border: TableBorder.all(
          color: Colors.grey.shade200,
          width: 1,
          borderRadius: BorderRadius.circular(8),
        ),
        columnWidths: const {
          0: FlexColumnWidth(1),
          1: FlexColumnWidth(4),
          2: FlexColumnWidth(2),
        },
        children: [
          TableRow(
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            children: [
              _buildTableHeader('#'),
              _buildTableHeader('item'.tr),
              _buildTableHeader('الحالة'),
            ],
          ),
          ...standardMaintenanceTypes.map((item) {
            final isSelected = selectedItems.contains(item.id);
            return _buildTableRow(item, isSelected);
          }).toList(),
        ],
      ),
    );
  }

  TableRow _buildTableRow(MaintenanceType item, bool isSelected) {
    // Get the controller to check the status
    final controller = Get.find<MaintenanceChecklistController>();

    // Use a widget that can be rebuilt when the status changes
    return TableRow(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      children: [
        _buildTableCell(
          '${item.id}',
          textAlign: TextAlign.center,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        // Use Obx for the name cell to update its color based on status
        Builder(builder: (context) {
          return Obx(() {
            final itemStatus = controller.getItemStatus(item.id);

            // Determine the background color based on the status
            Color? bgColor;
            if (isSelected) {
              if (itemStatus == 'فحص') {
                bgColor = Colors.green.withOpacity(0.05);
              } else if (itemStatus == 'اصلاح') {
                bgColor = Colors.red.withOpacity(0.05);
              } else {
                bgColor = Colors.grey.withOpacity(0.05);
              }
            }

            return Container(
              decoration: BoxDecoration(
                color: bgColor,
              ),
              child: _buildTableCell(
                item.name,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                textColor: isSelected
                    ? (itemStatus == 'اصلاح'
                        ? Colors.red.shade800
                        : Colors.green.shade800)
                    : Colors.black87,
              ),
            );
          });
        }),
        _buildCheckCell(item.id, isSelected),
      ],
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          color: AppColors.primary,
          fontFamily: 'Cairo',
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(
    String text, {
    TextAlign textAlign = TextAlign.start,
    FontWeight fontWeight = FontWeight.normal,
    Color? textColor,
  }) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          fontWeight: fontWeight,
          color: textColor,
          fontFamily: 'Cairo',
          height: 1.2,
        ),
        textAlign: textAlign,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );
  }

  Widget _buildCheckCell(int itemId, bool isSelected) {
    // Get the controller to check the status
    final controller = Get.find<MaintenanceChecklistController>();

    // Wrap in Obx to ensure this widget rebuilds when itemStatus changes
    return Obx(() {
      final itemStatus = controller.getItemStatus(itemId);

      // If the item is selected but no status is set, default to "فحص" (inspection)
      if (isSelected && itemStatus == null) {
        controller.setItemStatus(itemId, 'فحص');
      }

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
        child: Container(
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              // فحص (Inspection) option
              Expanded(
                child: InkWell(
                  onTap: readOnly
                      ? null
                      : () {
                          // Always ensure the item is selected
                          if (!isSelected) {
                            onItemToggle(itemId);
                          }
                          // Always set the status to فحص
                          controller.setItemStatus(itemId, 'فحص');
                        },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected && itemStatus == 'فحص'
                          ? Colors.green.withOpacity(0.2)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(17),
                        bottomLeft: Radius.circular(17),
                      ),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isSelected && itemStatus == 'فحص')
                            const Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: 14,
                            ),
                          const SizedBox(width: 4),
                          Text(
                            'فحص',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: isSelected && itemStatus == 'فحص'
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isSelected && itemStatus == 'فحص'
                                  ? Colors.green
                                  : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Divider
              Container(
                width: 1,
                color: Colors.grey.shade300,
              ),
              // اصلاح (Repair) option
              Expanded(
                child: InkWell(
                  onTap: readOnly
                      ? null
                      : () {
                          // Always ensure the item is selected
                          if (!isSelected) {
                            onItemToggle(itemId);
                          }
                          // Always set the status to اصلاح
                          controller.setItemStatus(itemId, 'اصلاح');
                        },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected && itemStatus == 'اصلاح'
                          ? Colors.red.withOpacity(0.2)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(17),
                        bottomRight: Radius.circular(17),
                      ),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isSelected && itemStatus == 'اصلاح')
                            const Icon(
                              Icons.build_circle,
                              color: Colors.red,
                              size: 14,
                            ),
                          const SizedBox(width: 4),
                          Text(
                            'اصلاح',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: isSelected && itemStatus == 'اصلاح'
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isSelected && itemStatus == 'اصلاح'
                                  ? Colors.red
                                  : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
