class MaintenanceTranslations {
  static final Map<String, Map<String, String>> translations = {
    'en': {
      // Maintenance Checklist
      'maintenance_checklist': 'Maintenance Checklist',
      'select_all': 'Select All',
      'clear_selection': 'Clear Selection',
      'grid_view': 'Grid View',
      'items_selected': 'items selected',
      'save': 'Save',
      'check': 'Check',
      'repair': 'Repair',
      'item_number': 'No.',
      'item': 'Item',
      
      // Maintenance Items (from the image)
      'check_chassis': 'Check Chassis',
      'engine_oil': 'Engine Oil',
      'oil_filter': 'Oil Filter',
      'transmission_oil': 'Transmission Oil',
      'brake_fluid': 'Brake Fluid',
      'differential_oil': 'Differential Oil',
      'radiator_water': 'Radiator Water',
      'radiator_water_tank': 'Radiator Water Tank',
      'radiator_tank': 'Radiator Tank',
      'engine_heat_coil': 'Engine Heat Coil',
      'battery_condition': 'Battery Condition',
      'alternator_voltage': 'Alternator Voltage',
      'front_rear_rotation_check': 'Front & Rear Rotation Check',
      'tire_check_air_pressure': 'Tire Check & Air Pressure',
      'spare_tire': 'Spare Tire',
      'belts_engine_pulleys': 'Belts & Engine Pulleys',
      'warning_lights_side_lights': 'Warning Lights & Side Lights',
      'warning_sweeps_public_address': 'Warning Sweeps & Public Address',
      'passports': 'Passports',
      'spark_plugs': 'Spark Plugs',
      'windshield_wipers': 'Windshield Wipers',
      'wiper_fluid_tank': 'Wiper Fluid Tank',
      'scissor_leather': 'Scissor Leather',
      'scissors': 'Scissors',
      'arms': 'Arms',
      'brakes': 'Brakes',
      'fabric_checks': 'Fabric Checks',
      'hubs': 'Hubs',
      'brake_checks': 'Brake Checks',
      'cooling_fan_with_brake': 'Cooling Fan with Brake',
      'side_mirrors': 'Side Mirrors',
      'windshield': 'Windshield',
      'vehicle_body': 'Vehicle Body',
      'fuel_chip_vehicle_accessories': 'Fuel Chip & Vehicle Accessories',
    },
    'ar': {
      // Maintenance Checklist
      'maintenance_checklist': 'قائمة الصيانة',
      'select_all': 'تحديد الكل',
      'clear_selection': 'مسح التحديد',
      'grid_view': 'عرض شبكي',
      'items_selected': 'عنصر محدد',
      'save': 'حفظ',
      'check': 'فحص',
      'repair': 'إصلاح',
      'item_number': 'رقم',
      'item': 'البند',
      
      // Maintenance Items (from the image)
      'check_chassis': 'فحص سليم الشبك',
      'engine_oil': 'زيت الماكينة',
      'oil_filter': 'فلتر الزيت',
      'transmission_oil': 'زيت ناقل الحركة',
      'brake_fluid': 'زيت الفرامل',
      'differential_oil': 'زيت الرؤوس (الديفرنس)',
      'radiator_water': 'ماء الرديتر',
      'radiator_water_tank': 'قربة ماء الرديتر',
      'radiator_tank': 'خزان الرديتر',
      'engine_heat_coil': 'ملف حرارة المحرك',
      'battery_condition': 'حالة البطارية',
      'alternator_voltage': 'جهد مولد الشحن (الدينمو)',
      'front_rear_rotation_check': 'فحص الدوار الامامي والخلفي',
      'tire_check_air_pressure': 'فحص الإطارات وضبط الهواء',
      'spare_tire': 'الإطار الاحتياطي',
      'belts_engine_pulleys': 'السيور وبكرات المحرك',
      'warning_lights_side_lights': 'الإضاءات التحذيرية والكشافات الجانبية',
      'warning_sweeps_public_address': 'الكسحات التحذيرية ومخاطبة الجمهور',
      'passports': 'الجوازات',
      'spark_plugs': 'بواجي المحرك',
      'windshield_wipers': 'مساحات الزجاج',
      'wiper_fluid_tank': 'قربة سائل المساحات',
      'scissor_leather': 'جلد المقصات',
      'scissors': 'المقصات',
      'arms': 'الاذرعة',
      'brakes': 'الفرامل',
      'fabric_checks': 'فحصات (الأقمشة)',
      'hubs': 'الهوبات',
      'brake_checks': 'فحصات البريك (الجلنط)',
      'cooling_fan_with_brake': 'تشغيل مزراج التبريد مع البريك',
      'side_mirrors': 'المرايات الجانبية',
      'windshield': 'الزجاج الامامي',
      'vehicle_body': 'جسم المركبة (البودي)',
      'fuel_chip_vehicle_accessories': 'شريحة الوقود وملحقات المركبة',
    },
  };
}
