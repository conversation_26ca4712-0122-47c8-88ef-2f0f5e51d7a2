import 'package:cars_app/app/controllers/auth_controller.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/sector_model.dart';
import '../../../data/models/user_model.dart';
import '../../../services/firestore_service.dart';
import '../../../services/user_storage_service.dart';
import '../../../widgets/custom_button.dart';

class LogisticsController extends GetxController {
  final cars = <Car>[].obs;
  final isLoading = false.obs;
  final selectedStatus = Rxn<CarStatus>();
  final AuthController authController = Get.find();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final LocalStorageService localStorageService = Get.find<LocalStorageService>();
  final RxString uid = ''.obs;
  final RxString userName = ''.obs;
  final FirestoreService _service = Get.find();

  @override
  void onInit() async{
    super.onInit();

    uid.value = (await localStorageService.getUserId()!)!;
    userName.value = (await localStorageService.getUsername()!)!;
  }


  String getCarStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'Active';
      case CarStatus.destroyed:
        return 'Destroyed';
      case CarStatus.maintenance:
        return 'Under Maintenance';
      case CarStatus.inWorkshop:
        return 'In Workshop';
      case CarStatus.outOfService:
        return 'Out of Service';
      default:
        return 'Unknown';
    }
  }

  //اضافه تسليم للفطاع

  /*
  * ياخذ هل موافق او لا اذا موافق باكد
  * */
  Stream<List<Car>> getCarsStream() {
    return FirebaseFirestore.instance
        .collection('cars')
    //استدعاء وتسليم
        .where('status', whereIn: ['sendGroup', 'sendToLogisticsSupport'])

    // .where(
    //     (Filter.or(Filter('status', isEqualTo: 'sendToLogisticsSupport'),
    //         Filter('status', isEqualTo: 'sendGroup'))))
        .snapshots()
        .map((snapshot) {
      final allCars = snapshot.docs.map((doc) {
        return Car.fromJson({...doc.data(), 'id': doc.id});
      }).toList();
      
      return selectedStatus.value == null 
          ? allCars 
          : allCars.where((car) => car.status == selectedStatus.value).toList();
    });
  }

  // Alias for getCarsStream to match the view's expectation
  Stream<List<Car>> getFilteredCarsStream() => getCarsStream();

  void setSelectedStatus(CarStatus? status) {
    selectedStatus.value = status;
  }

  Widget getCurrentActions(String carId, Car car) {
    if (car.status == CarStatus.receipt) {
      return CustomButton(
        isSwipe: true,
        text: 'تحويل للفني',
        onPressed: () {
          updateCarStatus(car,carId, CarStatus.pending, '','');
          _service.createNoticiation(UserRole.technician.name, [], car, 'newRequest');

        },
      );
    }else if (car.status == CarStatus.sendToLogisticsSupport) {
      return CustomButton(
        isSwipe: true,
        text: 'تسليم للقطاع',
        onPressed: () {
          updateCarStatus(car,carId, CarStatus.deliveryToSector, '','');
          _service.createNoticiation('sector_'+car.sectorId, [], car, 'newRequest');

        },
      );
    } else if (car.status == CarStatus.callToWorkshop ||
        car.status == CarStatus.sendGroup) {
      return CustomButton(
        isSwipe: true,
        text: 'أستلام المركبة', onPressed: () {
        updateCarStatus(car,carId, CarStatus.receipt, '','');
        _service.createNoticiation(UserRole.supervisor.name, [], car, 'newRequest');

      },);
    }else if (car.status == CarStatus.done) {
      return CustomButton(
        isSwipe: true,
        text: 'أرجاع المركبة للدعم اللوجستي', onPressed: () {
        updateCarStatus(car,carId, CarStatus.sendToLogisticsSupport, '','');

      },);
    }else if (car.status == CarStatus.pending) {
      return SizedBox();
    }else if (car.status == CarStatus.sentRequest) {
      return Column(
        children: [
          CustomButton(
            isSwipe: true,
            text: 'أرسال فريق', onPressed: () {
            updateCarStatus(car,carId, CarStatus.sendGroup, '','');
          },),
          SizedBox(height: 16,),
          CustomButton(
            isSwipe: true,

            text: 'أستدعاء المركبة', onPressed: () {
            updateCarStatus(car,carId, CarStatus.callToWorkshop, '','');

          },),
        ],
      );
    } else {

    }
    return SizedBox();
  }

  Future<void> updateCarStatus(Car car,String carId, CarStatus newStatus,
      String supervisorId,String? notes) async {
    try {


// Create a copy of the current workshop history
      List<WorkshopEntry> updatedWorkshopHistory = List.from(car.workshopHistory);

// Get the last workshop entry
      WorkshopEntry lastEntry = updatedWorkshopHistory.last;

// Create the new status
      WorkshopEntryStatus newWorkshopStatus = WorkshopEntryStatus(
        status: newStatus,
        createAt: DateTime.now(),
        senderId: uid.value,
        senderName: userName.value,
        notes: notes,
      );

// Add the new status to the last entry’s statuses list
      lastEntry.statuses = [
        ...lastEntry.statuses,
        newWorkshopStatus,
      ];

// Update the last entry in the list
      updatedWorkshopHistory[updatedWorkshopHistory.length - 1] = lastEntry;

      await _firestore.collection('cars').doc(carId).update({
        'status': newStatus
            .toString()
            .split('.')
            .last,
        // 'inspection': inspection?.toJson(),
        'workshopEntries': updatedWorkshopHistory.map((entry) => entry.toJson()).toList(),
        'lastUpdatedBy': supervisorId,
        'lastUpdateDate': FieldValue.serverTimestamp(),
      });


      // Get.find<FirestoreService>().sendNotificationToUsersByRole(roles: [UserRole.supervisor],
      //     title: 'طلب صيانة', body: '');
      Get.snackbar(
        'تم بنجاح',
        'تم تحديث حالة المركبة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة المركبة',
        snackPosition: SnackPosition.BOTTOM,
      );
      print('Error updating car status: $e');
    }
  }
}
