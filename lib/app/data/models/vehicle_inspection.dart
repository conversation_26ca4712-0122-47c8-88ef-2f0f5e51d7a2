import 'package:get/get.dart';
import 'inspection_status.dart';

class VehicleInspection {
  String? id;
  String? note;
  int? meterReading;
  Map<String, InspectionStatus> items;
  List<Map<String, dynamic>>? damagedParts;

  VehicleInspection({
    this.id,
    this.note,
    this.meterReading,
    this.damagedParts,
    Map<String, InspectionStatus>? items,
  }) : items = items ??
            {
              'engineOil': InspectionStatus.checkOk,
              'transmissionOil': InspectionStatus.checkOk,
              'powerSteeringOil': InspectionStatus.checkOk,
              'brakeFluid': InspectionStatus.checkOk,
              'differentialOil': InspectionStatus.checkOk,
              'radiatorWater': InspectionStatus.checkOk,
              'radiatorCap': InspectionStatus.checkOk,
              'radiatorTank': InspectionStatus.checkOk,
              'engineTemp': InspectionStatus.checkOk,
              'batteryCondition': InspectionStatus.checkOk,
              'alternatorVoltage': InspectionStatus.checkOk,
              'frontRearLights': InspectionStatus.checkOk,
              'tiresAndPressure': InspectionStatus.checkOk,
              'spareTire': InspectionStatus.checkOk,
              'engineBelts': InspectionStatus.checkOk,
              'warningLightsAndIndicators': InspectionStatus.checkOk,
              'warningHornAndSounds': InspectionStatus.checkOk,
              'sensors': InspectionStatus.checkOk,
              'engineMounts': InspectionStatus.checkOk,
              'windshield': InspectionStatus.checkOk,
              'otherWindows': InspectionStatus.checkOk,
              'wiperBlades': InspectionStatus.checkOk,
              'wipers': InspectionStatus.checkOk,
              'horn': InspectionStatus.checkOk,
              'brakes': InspectionStatus.checkOk,
              'upholstery': InspectionStatus.checkOk,
              'seatBelts': InspectionStatus.checkOk,
              'brakeWithAC': InspectionStatus.checkOk,
              'sideMirrors': InspectionStatus.checkOk,
              'frontGlass': InspectionStatus.checkOk,
              'bodyWork': InspectionStatus.checkOk,
              'fuelSystemAndFilter': InspectionStatus.checkOk,
            };

  Map<String, dynamic> toJson() => {
        'id': id,
        'note': note,
        'meterReading': meterReading,
        'items': items.map((key, value) => MapEntry(key, value.toJson())),
        'damagedParts': damagedParts,
      };

  factory VehicleInspection.fromJson(Map<String, dynamic> json) =>
      VehicleInspection(
        id: json['id'],
        note: json['note'],
        meterReading: json['meterReading'],
        items: (json['items'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, InspectionStatus.fromJson(value)),
        ),
        damagedParts: json['damagedParts'] != null
            ? List<Map<String, dynamic>>.from(json['damagedParts'])
            : null,
      );
}
