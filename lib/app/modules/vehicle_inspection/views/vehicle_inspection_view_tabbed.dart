import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/vehicle_inspection_controller.dart';
import '../controllers/car_parts_selector_controller.dart';
import '../../../translations/vehicle_inspection_translations.dart';
import '../../../data/models/inspection_status.dart';
import '../../../themes/app_colors.dart';
import '../widgets/improved_maintenance_checklist.dart';
import '../widgets/interactive_svg_selector.dart';
import '../../maintenance/controllers/maintenance_checklist_controller.dart';

class VehicleInspectionViewTabbed extends GetView<VehicleInspectionController> {
  const VehicleInspectionViewTabbed({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize controllers
    final carPartsController = Get.put(CarPartsSelectorController());
    final maintenanceController = Get.put(MaintenanceChecklistController());

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.primary,
          title: Text(
            'vehicleInspection'.tr,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Cairo',
            ),
          ),
          centerTitle: true,
          elevation: 0,
          actions: [
            GetX<VehicleInspectionController>(
              builder: (_) => controller.isLoading.value
                  ? const Center(
                      child: CircularProgressIndicator(color: Colors.white))
                  : IconButton(
                      icon: const Icon(Icons.save, color: Colors.white),
                      onPressed: controller.saveInspection,
                    ),
            ),
          ],
          bottom: TabBar(
            indicatorColor: Colors.white,
            indicatorWeight: 3,
            tabs: [
              Tab(
                icon: const Icon(Icons.checklist),
                text: 'maintenance_checklist'.tr,
              ),
              Tab(
                icon: const Icon(Icons.car_repair),
                text: 'car_parts'.tr,
              ),
            ],
          ),
        ),
        body: Directionality(
          textDirection: TextDirection.rtl,
          child: Column(
            children: [
              // Header with curved bottom
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(40),
                    bottomRight: Radius.circular(40),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const CircleAvatar(
                          radius: 40,
                          backgroundColor: AppColors.primary,
                          child: Icon(
                            Icons.directions_car,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        DateTime.now().toString().split('.')[0],
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),

              // Basic info cards
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildInfoField(
                        controller.noteController,
                        'notes'.tr,
                        Icons.note_add,
                        'enter_notes'.tr,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildInfoField(
                        controller.meterReadingController,
                        'meter_reading'.tr,
                        Icons.speed,
                        'enter_meter_reading'.tr,
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
              ),

              // Tab content
              Expanded(
                child: TabBarView(
                  children: [
                    // Maintenance Checklist Tab
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child:
                          _buildMaintenanceChecklistTab(maintenanceController),
                    ),

                    // Car Parts Selector Tab
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: _buildCarPartsSelectorTab(carPartsController),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar:
            _buildBottomBar(maintenanceController, carPartsController),
      ),
    );
  }

  Widget _buildInfoField(
    TextEditingController controller,
    String label,
    IconData icon,
    String hint, {
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 18),
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            maxLines: maxLines,
            keyboardType: keyboardType,
            textDirection: TextDirection.rtl,
            style: const TextStyle(fontFamily: 'Cairo', fontSize: 14),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: const TextStyle(
                color: AppColors.textSecondary,
                fontFamily: 'Cairo',
                fontSize: 12,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceChecklistTab(
      MaintenanceChecklistController maintenanceController) {
    // Use a simpler approach with Obx at the right level
    return Obx(() {
      // This will rebuild when either selectedItems or itemStatus changes
      final selectedItems = maintenanceController.selectedItems;
      // Also observe the itemStatus map to trigger rebuilds when statuses change
      final _ = maintenanceController.itemStatus;

      return ImprovedMaintenanceChecklist(
        // Pass a copy of the list to avoid direct reference
        selectedItems: List<int>.from(selectedItems),
        onItemToggle: (id) => maintenanceController.toggleItem(id),
      );
    });
  }

  Widget _buildCarPartsSelectorTab(
      CarPartsSelectorController carPartsController) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'select_damaged_parts'.tr,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 500, // Make it taller for better visibility
              child: InteractiveSvgSelector(
                controller: carPartsController,
                onPartSelected: (part) {
                  // Handle part selection
                  controller.addSelectedPart(part);
                },
                svgAssetPath: 'assets/mbulance.svg',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar(
    MaintenanceChecklistController maintenanceController,
    CarPartsSelectorController carPartsController,
  ) {
    // Use multiple Obx widgets at the right level
    return Obx(() {
      // Get the counts from the controllers
      final selectedItems = maintenanceController.selectedItems.length;
      final selectedParts = carPartsController.selectedPartIds.length;

      if (selectedItems == 0 && selectedParts == 0) {
        return const SizedBox.shrink();
      }

      return BottomAppBar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (selectedItems > 0)
                    Text(
                      '${'selected_items'.tr}: $selectedItems',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  if (selectedParts > 0)
                    Text(
                      '${'selected_parts'.tr}: $selectedParts',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
              const Spacer(),
              // Use a separate Obx for the loading state
              Obx(
                () => controller.isLoading.value
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : ElevatedButton.icon(
                        onPressed: controller.saveInspection,
                        icon: const Icon(Icons.save),
                        label: Text('save_inspection'.tr),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
