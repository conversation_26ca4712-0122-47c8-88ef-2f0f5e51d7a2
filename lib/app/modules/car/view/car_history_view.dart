import 'package:cars_app/app/widgets/workshop_history_timeline.dart';
import 'package:flutter/material.dart';
import '../../../data/models/car_model.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:intl/intl.dart';

class CarHistoryView extends StatelessWidget {
  final Car car;
  
  const CarHistoryView({Key? key, required this.car}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Combine and sort all history items

    return Scaffold(
      appBar: AppBar(
        title: Text('${car.plateCharacters}-${car.plateNumber} History'),
        centerTitle: true,
      ),
      // body: Column(
      //   children: [
      //     _buildSummaryCard(),
      //     Expanded(
      //       child: allHistory.isEmpty
      //           ? _buildEmptyState()
      //           : ListView.builder(
      //               itemCount: allHistory.length,
      //               itemBuilder: (context, index) {
      //                 final item = allHistory[index];
      //                 final isFirst = index == 0;
      //                 final isLast = index == allHistory.length - 1;
      //                
      //                 return TimelineTile(
      //                   alignment: TimelineAlign.start,
      //                   isFirst: isFirst,
      //                   isLast: isLast,
      //                   indicatorStyle: IndicatorStyle(
      //                     width: 20,
      //                     color: _getIndicatorColor(item.type),
      //                     iconStyle: IconStyle(
      //                       color: Colors.white,
      //                       iconData: _getIndicatorIcon(item.type),
      //                     ),
      //                   ),
      //                   beforeLineStyle: LineStyle(
      //                     color: Colors.grey.shade300,
      //                   ),
      //                   endChild: _buildTimelineCard(item),
      //                 );
      //               },
      //             ),
      //     ),
      //   ],
      // ),
      body: SingleChildScrollView(child: WorkshopHistoryTimeline(entries: car.workshopHistory,showDriverInfo: false,)),
    );
  }


  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.blue),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineCard(HistoryItem item) {
    return Card(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  DateFormat('MMM d, y').format(item.date),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            if (item.subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                item.subtitle!,
                style: TextStyle(
                  color: Colors.grey[800],
                ),
              ),
            ],
            if (item.details != null) ...[
              const SizedBox(height: 8),
              ...item.details!.map((detail) => Padding(
                padding: const EdgeInsets.only(left: 8, top: 4),
                child: Row(
                  children: [
                    Icon(Icons.arrow_right, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        detail,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No history yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'All car transfers and workshop visits will appear here',
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }


  List<HistoryItem> _getWorkshopHistory() {
    return car.workshopHistory.map((entry) {
      final details = <String>[];
      
      if (entry.statuses?.isNotEmpty == true) {
        details.add('Maintenance Notes: ${entry.statuses.last.notes}');
      }
      // if (entry.inWorkshopNotes?.isNotEmpty == true) {
      //   details.add('Workshop Notes: ${entry.inWorkshopNotes}');
      // }
      // if (entry.completedNotes?.isNotEmpty == true) {
      //   details.add('Completion Notes: ${entry.completedNotes}');
      // }
      // if (entry.services.isNotEmpty) {
      //   details.add('Services:');
      //   details.addAll(entry.services.map((service) =>
      //       '${service.name} - ${service.status} (\$${service.cost.toStringAsFixed(2)})'));
      // }

      return HistoryItem(
        type: 'workshop',
        date:DateTime.now(),
        title: _getWorkshopTitle(entry.status),
        subtitle: 'sss',
        details: details,
      );
    }).toList();
  }

  String _getWorkshopTitle(WorkshopStatus status) {
    switch (status) {
      case WorkshopStatus.maintenance:
        return 'Maintenance Started';
      case WorkshopStatus.inWorkshop:
        return 'Sent to Workshop';
      case WorkshopStatus.completed:
        return 'Workshop Service Completed';
      case WorkshopStatus.pending:
        return 'Workshop Service Pending';
        // TODO: Handle this case.
      case WorkshopStatus.inProgress:
        // TODO: Handle this case.
        return 'Workshop Service In Progress';
    }
  }

  Color _getIndicatorColor(String type) {
    switch (type) {
      case 'transfer':
        return Colors.blue;
      case 'workshop':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getIndicatorIcon(String type) {
    switch (type) {
      case 'transfer':
        return Icons.swap_horiz;
      case 'workshop':
        return Icons.build;
      default:
        return Icons.info;
    }
  }

  String _getDaysSinceLastMaintenance() {
    final lastMaintenance = car.workshopHistory
        .where((entry) => entry.status == WorkshopStatus.completed)
        .map((entry) => entry.statuses.last.createAt )
        .reduce((a, b) => a.isAfter(b) ? a : b);

    final days = DateTime.now().difference(lastMaintenance).inDays;
    return days.toString();
  }
}

class HistoryItem {
  final String type;
  final DateTime date;
  final String title;
  final String? subtitle;
  final List<String>? details;

  HistoryItem({
    required this.type,
    required this.date,
    required this.title,
    this.subtitle,
    this.details,
  });
}
