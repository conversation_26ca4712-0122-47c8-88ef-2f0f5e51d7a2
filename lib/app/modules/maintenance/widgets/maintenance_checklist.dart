import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/maintenance_types.dart';
import '../controllers/maintenance_checklist_controller.dart';

class MaintenanceChecklist extends StatelessWidget {
  final List<int>? selectedItems;
  final Function(int)? onItemToggle;
  final bool readOnly;

  const MaintenanceChecklist({
    Key? key,
    this.selectedItems,
    this.onItemToggle,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                'قائمة الصيانة'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            _buildChecklistGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 4,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: standardMaintenanceTypes.length,
      itemBuilder: (context, index) {
        final item = standardMaintenanceTypes[index];
        final isSelected = selectedItems?.contains(item.id) ?? false;

        return _buildChecklistItem(item, isSelected);
      },
    );
  }

  Widget _buildChecklistItem(MaintenanceType item, bool isSelected) {
    return InkWell(
      onTap: readOnly ? null : () => onItemToggle?.call(item.id),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.green : Colors.grey.shade300,
            width: 1.5,
          ),
          color: isSelected ? Colors.green.withOpacity(0.1) : null,
        ),
        child: Row(
          children: [
            if (!readOnly)
              Checkbox(
                value: isSelected,
                onChanged: (value) => onItemToggle?.call(item.id),
                activeColor: Colors.green,
              )
            else
              Icon(
                isSelected ? Icons.check_circle : Icons.circle_outlined,
                color: isSelected ? Colors.green : Colors.grey,
                size: 20,
              ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                item.name,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Colors.green.shade800 : Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MaintenanceChecklistTable extends StatelessWidget {
  final List<int>? selectedItems;
  final Function(int)? onItemToggle;
  final bool readOnly;

  const MaintenanceChecklistTable({
    Key? key,
    this.selectedItems,
    this.onItemToggle,
    this.readOnly = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                'قائمة الصيانة'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            _buildChecklistTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistTable() {
    return Table(
      border: TableBorder.all(
        color: Colors.grey.shade300,
        width: 1,
      ),
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(4),
        2: FlexColumnWidth(2),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
          ),
          children: [
            _buildTableHeader('رقم'),
            _buildTableHeader('البند'),
            _buildTableHeader('الحاddddddلة'),
          ],
        ),
        ...standardMaintenanceTypes.map((item) {
          final isSelected = selectedItems?.contains(item.id) ?? false;
          return _buildTableRow(item, isSelected);
        }).toList(),
      ],
    );
  }

  TableRow _buildTableRow(MaintenanceType item, bool isSelected) {
    return TableRow(
      decoration: BoxDecoration(
        color: isSelected ? Colors.green.withOpacity(0.05) : null,
      ),
      children: [
        _buildTableCell(
          '${item.id}',
          textAlign: TextAlign.center,
        ),
        _buildTableCell(
          item.name,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        _buildSwitchCell(item.id, isSelected),
      ],
    );
  }

  // New method to build a switch-like cell
  Widget _buildSwitchCell(int itemId, bool isSelected) {
    // Get the controller to check the status
    final controller = Get.find<MaintenanceChecklistController>();
    final itemStatus = controller.getItemStatus(itemId);

    // If the item is selected but no status is set, default to "سليم" (ok)
    if (isSelected && itemStatus == null) {
      controller.setItemStatus(itemId, 'سليم');
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: Container(
        height: 36,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            // OK/سليم option
            Expanded(
              child: InkWell(
                onTap: readOnly
                    ? null
                    : () {
                        controller.setItemStatus(itemId, 'سليم');
                      },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected && itemStatus == 'سليم'
                        ? Colors.green.withOpacity(0.2)
                        : Colors.transparent,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(17),
                      bottomLeft: Radius.circular(17),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      'سليم',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: isSelected && itemStatus == 'سليم'
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected && itemStatus == 'سليم'
                            ? Colors.green
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Fix/إصلاح option
            Expanded(
              child: InkWell(
                onTap: readOnly
                    ? null
                    : () {
                        controller.setItemStatus(itemId, 'إصلاح');
                      },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected && itemStatus == 'إصلاح'
                        ? Colors.red.withOpacity(0.2)
                        : Colors.transparent,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(17),
                      bottomRight: Radius.circular(17),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      'إصلاح',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: isSelected && itemStatus == 'إصلاح'
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected && itemStatus == 'إصلاح'
                            ? Colors.red
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(
    String text, {
    TextAlign textAlign = TextAlign.start,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: fontWeight,
        ),
        textAlign: textAlign,
      ),
    );
  }
}
