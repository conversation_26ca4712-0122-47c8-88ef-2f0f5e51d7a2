import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../themes/app_colors.dart';
import '../../../inventory/models/product_model.dart';

/// Widget for displaying a single product item in the product selection dialog list.
class ProductSelectionItem extends StatelessWidget {
  final ProductModel product;
  final bool isSelected; // Whether this item is currently selected in the parent list
  final VoidCallback onTap; // Callback when the item is tapped/button is pressed

  const ProductSelectionItem({
    Key? key,
    required this.product,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
      leading: Container(
        width: 48.r,
        height: 48.r,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: isSelected
              ? const Icon(Icons.check, color: Colors.white)
              : (product.name.isNotEmpty
              ? Text(
            product.name.substring(0, 1).toUpperCase(),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: isSelected ? Colors.white : AppColors.textPrimary,
            ),
          )
              : null), // Handle empty name case
        ),
      ),
      title: Text(
        product.name,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (product.sku != null)
            Text(
              product.sku!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          SizedBox(height: 4.h),
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  '${'qty'.tr}: ${product.currentQuantity}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (product.sellingPrice != null) ...[
                SizedBox(width: 8.w),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '${product.sellingPrice!.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
      trailing: ElevatedButton(
        onPressed: onTap, // Use the provided callback
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? AppColors.error : AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r)),
          padding: EdgeInsets.symmetric(horizontal: 12.w),
        ),
        child: Text(isSelected ? 'remove'.tr : 'add'.tr),
      ),
      // Optional: Make the whole ListTile tappable
      // onTap: onTap,
    );
  }
}