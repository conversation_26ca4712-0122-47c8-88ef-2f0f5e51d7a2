import 'package:cloud_firestore/cloud_firestore.dart';
import 'car_model.dart';
import 'user_model.dart';

enum TripStatus {
  pending,
  inProgress,
  completed,
  cancelled
}

class Trip {
  final String id;
  final Car car;
  final String driverId;
  final String driverName;
  final DateTime startTime;
  final DateTime? endTime;
  final String startLocation;
  final String endLocation;
  final TripStatus status;
  final String? purpose;
  final String? notes;
  final double? distance;
  final double? fuelConsumption;
  final DateTime createdAt;
  final String createdBy;
  final String createdByName;
  final DateTime? lastUpdated;
  final String? lastUpdatedBy;
  final String? lastUpdatedByName;

  Trip({
    required this.id,
    required this.car,
    required this.driverId,
    required this.driverName,
    required this.startTime,
    this.endTime,
    required this.startLocation,
    required this.endLocation,
    required this.status,
    this.purpose,
    this.notes,
    this.distance,
    this.fuelConsumption,
    required this.createdAt,
    required this.createdBy,
    required this.createdByName,
    this.lastUpdated,
    this.lastUpdatedBy,
    this.lastUpdatedByName,
  });

  factory Trip.fromJson(Map<String, dynamic> json) {
    return Trip(
      id: json['id'] as String,
      car: Car.fromJson(json['car'] as Map<String, dynamic>),
      driverId: json['driverId'] as String,
      driverName: json['driverName'] as String,
      startTime: (json['startTime'] as Timestamp).toDate(),
      endTime: json['endTime'] != null ? (json['endTime'] as Timestamp).toDate() : null,
      startLocation: json['startLocation'] as String,
      endLocation: json['endLocation'] as String,
      status: TripStatus.values.firstWhere(
        (e) => e.toString() == 'TripStatus.${json['status']}',
        orElse: () => TripStatus.pending,
      ),
      purpose: json['purpose'] as String?,
      notes: json['notes'] as String?,
      distance: json['distance']?.toDouble(),
      fuelConsumption: json['fuelConsumption']?.toDouble(),
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      createdBy: json['createdBy'] as String,
      createdByName: json['createdByName'] as String,
      lastUpdated: json['lastUpdated'] != null ? (json['lastUpdated'] as Timestamp).toDate() : null,
      lastUpdatedBy: json['lastUpdatedBy'] as String?,
      lastUpdatedByName: json['lastUpdatedByName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'car': car.toJson(),
      'driverId': driverId,
      'driverName': driverName,
      'startTime': Timestamp.fromDate(startTime),
      if (endTime != null) 'endTime': Timestamp.fromDate(endTime!),
      'startLocation': startLocation,
      'endLocation': endLocation,
      'status': status.toString().split('.').last,
      if (purpose != null) 'purpose': purpose,
      if (notes != null) 'notes': notes,
      if (distance != null) 'distance': distance,
      if (fuelConsumption != null) 'fuelConsumption': fuelConsumption,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'createdByName': createdByName,
      if (lastUpdated != null) 'lastUpdated': Timestamp.fromDate(lastUpdated!),
      if (lastUpdatedBy != null) 'lastUpdatedBy': lastUpdatedBy,
      if (lastUpdatedByName != null) 'lastUpdatedByName': lastUpdatedByName,
    };
  }

  Trip copyWith({
    String? id,
    Car? car,
    String? driverId,
    String? driverName,
    DateTime? startTime,
    DateTime? endTime,
    String? startLocation,
    String? endLocation,
    TripStatus? status,
    String? purpose,
    String? notes,
    double? distance,
    double? fuelConsumption,
    DateTime? createdAt,
    String? createdBy,
    String? createdByName,
    DateTime? lastUpdated,
    String? lastUpdatedBy,
    String? lastUpdatedByName,
  }) {
    return Trip(
      id: id ?? this.id,
      car: car ?? this.car,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      startLocation: startLocation ?? this.startLocation,
      endLocation: endLocation ?? this.endLocation,
      status: status ?? this.status,
      purpose: purpose ?? this.purpose,
      notes: notes ?? this.notes,
      distance: distance ?? this.distance,
      fuelConsumption: fuelConsumption ?? this.fuelConsumption,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      createdByName: createdByName ?? this.createdByName,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      lastUpdatedBy: lastUpdatedBy ?? this.lastUpdatedBy,
      lastUpdatedByName: lastUpdatedByName ?? this.lastUpdatedByName,
    );
  }
} 