import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swipe_button/flutter_swipe_button.dart';
import 'package:get/get.dart';

enum ButtonVariant {
  primary,
  secondary,
  outlined,
  text,
  gradient,
  danger,
  success,
  warning,
}

enum ButtonSize {
  small,
  medium,
  large,
}

class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isSwipe;
  final ButtonVariant variant;
  final ButtonSize size;
  final Color? backgroundColor;
  final Color? textColor;
  final List<Color>? gradientColors;
  final bool? isActive;
  final IconData? icon;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final double? width;
  final double? height;
  final double? elevation;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool fullWidth;
  final bool enabled;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isSwipe = false,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.backgroundColor,
    this.textColor,
    this.gradientColors,
    this.icon,
    this.leadingIcon,
    this.trailingIcon,
    this.isActive,
    this.width,
    this.height,
    this.elevation,
    this.borderRadius,
    this.padding,
    this.fullWidth = true,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Get button dimensions based on size
  double get _getHeight {
    if (widget.height != null) return widget.height!;
    switch (widget.size) {
      case ButtonSize.small:
        return 40.h;
      case ButtonSize.medium:
        return 48.h;
      case ButtonSize.large:
        return 56.h;
    }
  }

  EdgeInsetsGeometry get _getPadding {
    if (widget.padding != null) return widget.padding!;
    switch (widget.size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h);
      case ButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h);
      case ButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 32.w, vertical: 16.h);
    }
  }

  double get _getFontSize {
    switch (widget.size) {
      case ButtonSize.small:
        return 14.sp;
      case ButtonSize.medium:
        return 16.sp;
      case ButtonSize.large:
        return 18.sp;
    }
  }

  double get _getIconSize {
    switch (widget.size) {
      case ButtonSize.small:
        return 16.r;
      case ButtonSize.medium:
        return 20.r;
      case ButtonSize.large:
        return 24.r;
    }
  }

  // Get button colors based on variant
  ButtonStyle get _getButtonStyle {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Color backgroundColor;
    Color foregroundColor;
    Color? borderColor;
    double elevation = widget.elevation ?? 2;

    switch (widget.variant) {
      case ButtonVariant.primary:
        backgroundColor = widget.backgroundColor ?? AppColors.primary;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
      case ButtonVariant.secondary:
        backgroundColor = widget.backgroundColor ?? AppColors.secondary;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
      case ButtonVariant.outlined:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.textColor ?? AppColors.primary;
        borderColor = AppColors.primary;
        elevation = 0;
        break;
      case ButtonVariant.text:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.textColor ?? AppColors.primary;
        elevation = 0;
        break;
      case ButtonVariant.danger:
        backgroundColor = widget.backgroundColor ?? AppColors.error;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
      case ButtonVariant.success:
        backgroundColor = widget.backgroundColor ?? AppColors.success;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
      case ButtonVariant.warning:
        backgroundColor = widget.backgroundColor ?? AppColors.warning;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
      case ButtonVariant.gradient:
        backgroundColor = Colors.transparent;
        foregroundColor = widget.textColor ?? Colors.white;
        break;
    }

    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      shadowColor: backgroundColor.withOpacity(0.3),
      surfaceTintColor: Colors.transparent,
      side: borderColor != null ? BorderSide(color: borderColor, width: 1.5) : null,
      shape: RoundedRectangleBorder(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12.r),
      ),
      padding: _getPadding,
      minimumSize: Size(
        widget.width ?? (widget.fullWidth ? double.infinity : 0),
        _getHeight,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.enabled && widget.onPressed != null && !widget.isLoading;

    if (widget.isSwipe) {
      return _buildSwipeButton();
    }

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: _buildButton(isEnabled),
    );

    return SizedBox(
      width: widget.width ?? (widget.fullWidth ? double.infinity : null),
      height: _getHeight,
      child: button,
    );
  }

  Widget _buildButton(bool isEnabled) {
    if (widget.variant == ButtonVariant.gradient) {
      return _buildGradientButton(isEnabled);
    }

    return ElevatedButton(
      onPressed: isEnabled ? _handleTap : null,
      style: _getButtonStyle,
      child: _buildButtonContent(),
    );
  }

  Widget _buildGradientButton(bool isEnabled) {
    final gradientColors = widget.gradientColors ?? AppColors.primaryGradient;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isEnabled ? gradientColors : [Colors.grey, Colors.grey.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12.r),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: gradientColors.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? _handleTap : null,
          onTapDown: isEnabled ? (_) => _animationController.forward() : null,
          onTapUp: isEnabled ? (_) => _animationController.reverse() : null,
          onTapCancel: isEnabled ? () => _animationController.reverse() : null,
          borderRadius: widget.borderRadius ?? BorderRadius.circular(12.r),
          child: Container(
            height: _getHeight,
            padding: _getPadding,
            child: _buildButtonContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildSwipeButton() {
    final buttonColor = widget.backgroundColor ?? AppColors.primary;

    return SwipeButton.expand(
      enabled: widget.isActive ?? true,
      thumb: Icon(
        Icons.double_arrow_rounded,
        color: Colors.white,
        size: _getIconSize,
      ),
      child: Text(
        widget.text,
        style: TextStyle(
          fontSize: _getFontSize,
          fontWeight: FontWeight.w600,
          color: widget.textColor ?? AppColors.getTextPrimary(Get.isDarkMode),
        ),
      ),
      activeThumbColor: buttonColor,
      activeTrackColor: AppColors.getSurface(Get.isDarkMode),
      onSwipe: widget.isLoading ? null : widget.onPressed,
    );
  }

  Widget _buildButtonContent() {
    if (widget.isLoading) {
      return SizedBox(
        height: _getIconSize,
        width: _getIconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.variant == ButtonVariant.outlined || widget.variant == ButtonVariant.text
                ? AppColors.primary
                : Colors.white,
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.leadingIcon != null) ...[
          widget.leadingIcon!,
          SizedBox(width: 8.w),
        ],
        if (widget.icon != null) ...[
          Icon(widget.icon, size: _getIconSize),
          SizedBox(width: 8.w),
        ],
        Flexible(
          child: Text(
            widget.text,
            style: TextStyle(
              fontSize: _getFontSize,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (widget.trailingIcon != null) ...[
          SizedBox(width: 8.w),
          widget.trailingIcon!,
        ],
      ],
    );
  }

  void _handleTap() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    widget.onPressed?.call();
  }
}
