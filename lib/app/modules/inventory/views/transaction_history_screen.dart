
// transaction_history_screen.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart'; // For date formatting
import '../controllers/transaction_history_controller.dart';
import '../models/transaction_model.dart';
import '../widgets/error_display.dart';
import '../widgets/loading_indicator.dart';


class TransactionHistoryScreen extends GetView<TransactionHistoryController> {
  const TransactionHistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final DateFormat dateFormat = DateFormat('MMM dd, yyyy hh:mm a'); // Example format

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction History'),
        // TODO: Add filter button/options here
        // actions: [ IconButton(onPressed: _showFilterDialog, icon: Icon(Icons.filter_list)) ]
      ),
      body: RefreshIndicator(
        onRefresh: () async => controller.refreshHistory(), // Add refresh method to controller
        child: Obx(() {
          if (controller.isLoading.value && controller.transactions.isEmpty) {
            return const LoadingIndicator();
          } else if (controller.error.value.isNotEmpty) {
            return ErrorDisplay(
              errorMessage: controller.error.value,
              onRetry: () => controller.refreshHistory(),
            );
          } else if (controller.transactions.isEmpty) {
            return const Center(child: Text('No transactions found.'));
          } else {
            return ListView.separated(
              itemCount: controller.transactions.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final transaction = controller.transactions[index];
                final isIncrease = transaction.quantityChange > 0;
                final Color quantityColor = isIncrease ? Colors.green : Colors.redAccent;
                final IconData typeIcon = _getTransactionTypeIcon(transaction.type);

                return ListTile(
                  leading: CircleAvatar( // Icon indicating transaction type
                    child: Icon(typeIcon, size: 20),
                    backgroundColor: _getTransactionTypeColor(transaction.type).withOpacity(0.2),
                    foregroundColor: _getTransactionTypeColor(transaction.type),
                  ),
                  title: Text(
                    transaction.productName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    '${transaction.type.name.capitalizeFirst}: ${dateFormat.format(transaction.timestamp.toDate())}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  trailing: Text(
                    '${isIncrease ? '+' : ''}${transaction.quantityChange}', // Show + sign for increase
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: quantityColor,
                      fontSize: 16,
                    ),
                  ),
                  // Optional: onTap to view more details if needed
                );
              },
            );
          }
        }),
      ),
    );
  }

  // Helper methods for styling transaction list items
  IconData _getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.purchase: return Icons.add_shopping_cart;
      case TransactionType.usage: return Icons.construction;
      case TransactionType.sale: return Icons.point_of_sale;
      case TransactionType.adjustment: return Icons.tune;
    }
  }
  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.purchase: return Colors.green;
      case TransactionType.usage: return Colors.orange;
      case TransactionType.sale: return Colors.blue;
      case TransactionType.adjustment: return Colors.purple;
    }
  }

  // Placeholder for filter dialog/bottom sheet
  void _showFilterDialog() {
    // Get.dialog(...) or Get.bottomSheet(...)
    // Allow filtering by product (dropdown/search), date range, type
    // Update controller's filter state (e.g., controller.setFilter(...))
    print("TODO: Implement filter dialog");
  }

}