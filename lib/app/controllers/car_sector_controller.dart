import 'package:flutter/material.dart';
import '../providers/car_provider.dart';
import '../providers/sector_provider.dart';
import '../services/car_sector_provider.dart';

class CarSectorController {
  final CarProvider _carProvider;
  final SectorProvider _sectorProvider;

  CarSectorController({
    CarProvider? carProvider,
    SectorProvider? sectorProvider,
  })  : _carProvider = carProvider ?? CarProvider(),
        _sectorProvider = sectorProvider ?? SectorProvider();

  /// Handle the creation of a car and show success or error message
  Future<void> createCar(BuildContext context, Map<String, dynamic> data) async {
    try {
      await _carProvider.createCar(data);
      _showSnackBar(context, 'Car created successfully!', Colors.green);
    } catch (e) {
      _showSnackBar(context, 'Failed to create car: $e', Colors.red);
    }
  }

  /// Handle the creation of a sector and show success or error message
  Future<void> createSector(BuildContext context, Map<String, dynamic> data) async {
    try {
      await _sectorProvider.createSector(data);
      _showSnackBar(context, 'Sector created successfully!', Colors.green);
    } catch (e) {
      _showSnackBar(context, 'Failed to create sector: $e', Colors.red);
    }
  }

  /// Show a snackbar with the given message and color
  void _showSnackBar(BuildContext context, String message, Color color) {
    final snackBar = SnackBar(
      content: Text(message),
      backgroundColor: color,
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
