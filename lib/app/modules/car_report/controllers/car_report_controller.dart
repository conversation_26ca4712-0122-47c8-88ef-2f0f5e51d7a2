import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import '../../../data/models/car_model.dart';
import '../../../services/car_service.dart';
import '../../../services/export/export.dart';
import '../../../modules/reports/controllers/reports_controller.dart'
    as reports;

class CarReportController extends GetxController {
  final CarService carService = Get.find();
  final isLoading = false.obs;
  final searchQuery = ''.obs;
  final searchController = TextEditingController();

  // Car data
  final allCars = <Car>[].obs;
  final filteredCars = <Car>[].obs;
  final selectedCar = Rxn<Car>();

  // Workshop entries for selected car
  final workshopEntries = <WorkshopEntry>[].obs;

  // Status counts for the selected car
  final statusDistribution = <reports.StatusCount>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadCars();

    // Listen to search query changes
    ever(searchQuery, (_) => filterCars());
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadCars() async {
    try {
      isLoading.value = true;
      final cars = await carService.getCars();
      allCars.assignAll(cars);
      filterCars();
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to load cars'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void filterCars() {
    if (searchQuery.value.isEmpty) {
      filteredCars.assignAll(allCars);
      return;
    }

    final query = searchQuery.value.toLowerCase();
    final filtered = allCars.where((car) {
      return car.plateNumber.toLowerCase().contains(query) ||
          car.plateCharacters.toLowerCase().contains(query) ||
          car.carModel.toLowerCase().contains(query) ||
          car.sectorName.toLowerCase().contains(query);
    }).toList();

    filteredCars.assignAll(filtered);
  }

  void selectCar(Car car) {
    selectedCar.value = car;
    workshopEntries.assignAll(car.workshopHistory);
    generateStatusDistribution();
  }

  void clearSelection() {
    selectedCar.value = null;
    workshopEntries.clear();
    statusDistribution.clear();
  }

  void generateStatusDistribution() {
    if (selectedCar.value == null) return;

    final counts = <CarStatus, int>{};

    for (final entry in workshopEntries) {
      for (final status in entry.statuses) {
        counts.update(
          status.status,
          (value) => value + 1,
          ifAbsent: () => 1,
        );
      }
    }

    statusDistribution.assignAll(counts.entries
        .map((e) => reports.StatusCount(e.key, e.value))
        .toList());
  }

  Future<void> exportToPDF() async {
    if (selectedCar.value == null) return;

    try {
      isLoading.value = true;

      final fileExportService = Get.find<FileExportService>();
      final car = selectedCar.value!;
      final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());

      // Create workshop report entries for the selected car
      final entries = <reports.WorkshopReportEntry>[];

      for (final entry in workshopEntries) {
        for (final status in entry.statuses) {
          entries.add(reports.WorkshopReportEntry(
            car: car,
            workshopEntry: entry,
            status: status,
          ));
        }
      }

      // Export the file
      await fileExportService.exportCarReportToPdf(
        car: car,
        entries: entries,
        statusDistribution: statusDistribution.toList(),
        fileName:
            'car_report_${car.plateNumber}_${car.plateCharacters}_$dateStr.pdf',
      );
    } catch (e) {
      Logger().e(e);
      Get.snackbar(
        'Error'.tr,
        'Failed to generate PDF report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> exportToExcel() async {
    if (selectedCar.value == null) return;

    try {
      isLoading.value = true;

      final fileExportService = Get.find<FileExportService>();
      final car = selectedCar.value!;
      final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());

      // Create workshop report entries for the selected car
      final entries = <reports.WorkshopReportEntry>[];

      for (final entry in workshopEntries) {
        for (final status in entry.statuses) {
          entries.add(reports.WorkshopReportEntry(
            car: car,
            workshopEntry: entry,
            status: status,
          ));
        }
      }

      // Export the file
      await fileExportService.exportCarReportToExcel(
        car: car,
        entries: entries,
        statusDistribution: statusDistribution.toList(),
        fileName:
            'car_report_${car.plateNumber}_${car.plateCharacters}_$dateStr.xlsx',
      );
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
