import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../data/models/car_model.dart';
import '../data/models/trip_model.dart';
import '../controllers/auth_controller.dart';

class TripService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthController _auth = Get.find<AuthController>();

  Future<TripService> init() async {
    return this;
  }

  // Create a new trip
  Future<void> createTrip({
    required Car car,
    required String driverId,
    required String driverName,
    required DateTime startTime,
    required String startLocation,
    required String endLocation,
    String? purpose,
    String? notes,
  }) async {
    try {
      final tripData = {
        'car': car.toJson(),
        'driverId': driverId,
        'driverName': driverName,
        'startTime': Timestamp.fromDate(startTime),
        'startLocation': startLocation,
        'endLocation': endLocation,
        'status': TripStatus.pending.toString().split('.').last,
        if (purpose != null) 'purpose': purpose,
        if (notes != null) 'notes': notes,
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': _auth.uid,
        'createdByName': _auth.userName,
      };

      await _firestore.collection('trips').add(tripData);
    } catch (e) {
      print('Error creating trip: $e');
      rethrow;
    }
  }

  // Get all trips
  Stream<List<Trip>> getTrips() {
    return _firestore
        .collection('trips')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Trip.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    });
  }

  // Get trips by status
  Stream<List<Trip>> getTripsByStatus(TripStatus status) {
    return _firestore
        .collection('trips')
        .where('status', isEqualTo: status.toString().split('.').last)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Trip.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    });
  }

  // Get trips by driver
  Stream<List<Trip>> getTripsByDriver(String driverId) {
    return _firestore
        .collection('trips')
        .where('driverId', isEqualTo: driverId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => Trip.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    });
  }

  // Get a single trip by ID
  Future<Trip?> getTrip(String tripId) async {
    try {
      final doc = await _firestore.collection('trips').doc(tripId).get();
      if (doc.exists) {
        return Trip.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      print('Error getting trip: $e');
      return null;
    }
  }

  // Update trip status
  Future<void> updateTripStatus(String tripId, TripStatus status) async {
    try {
      await _firestore.collection('trips').doc(tripId).update({
        'status': status.toString().split('.').last,
        'lastUpdated': FieldValue.serverTimestamp(),
        'lastUpdatedBy': _auth.uid,
        'lastUpdatedByName': _auth.userName,
      });
    } catch (e) {
      print('Error updating trip status: $e');
      rethrow;
    }
  }

  // Complete trip
  Future<void> completeTrip({
    required String tripId,
    required DateTime endTime,
    double? distance,
    double? fuelConsumption,
    String? notes,
  }) async {
    try {
      await _firestore.collection('trips').doc(tripId).update({
        'status': TripStatus.completed.toString().split('.').last,
        'endTime': Timestamp.fromDate(endTime),
        if (distance != null) 'distance': distance,
        if (fuelConsumption != null) 'fuelConsumption': fuelConsumption,
        if (notes != null) 'notes': notes,
        'lastUpdated': FieldValue.serverTimestamp(),
        'lastUpdatedBy': _auth.uid,
        'lastUpdatedByName': _auth.userName,
      });
    } catch (e) {
      print('Error completing trip: $e');
      rethrow;
    }
  }

  // Delete trip
  Future<void> deleteTrip(String tripId) async {
    try {
      await _firestore.collection('trips').doc(tripId).delete();
    } catch (e) {
      print('Error deleting trip: $e');
      rethrow;
    }
  }
} 