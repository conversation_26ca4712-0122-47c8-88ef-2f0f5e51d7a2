import 'dart:typed_data';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import '../../../data/models/car_model.dart';
import '../../../services/car_service.dart';
import '../../../services/export/export.dart';

// Add date extensions if you don't have them (e.g., startOfDay, endOfDay)
// Example:
// extension DateTimeExtensions on DateTime {
//   DateTime get startOfDay => DateTime(year, month, day);
//   DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999);
// }

class ReportsController extends GetxController {
  final CarService carService = Get.find();
  final isLoading = false.obs;

  // Use startOfDay and endOfDay for clear date range definition
  final startDate =
      DateTime.now().subtract(const Duration(days: 7)).startOfDay.obs;
  final endDate = DateTime.now().endOfDay.obs;

  final cars = <Car>[].obs; // All cars loaded initially
  final filteredEntries =
      <WorkshopReportEntry>[].obs; // Entries matching filters

  // Filter options
  final selectedStatus = Rxn<CarStatus>();
  final selectedSector = Rxn<String>();
  final selectedCarType = Rxn<String>();
  final searchQuery = ''.obs;
  final dateRangeController = TextEditingController();

  // UI state
  final selectedTabIndex = 0.obs;
  final chartType = ChartType.column.obs;
  final activityChartType = ActivityChartType.line.obs;
  final sectorChartType = SectorChartType.pie.obs;
  final sortOption = SortOption.dateDesc.obs;

  // Analytics data
  final statusDistribution = <StatusCount>[].obs;
  final dailyActivity = <DailyActivity>[].obs;
  final sectorDistribution = <SectorCount>[].obs;
  final carTypeDistribution = <CarTypeCount>[].obs;
  final timeInWorkshopAnalysis = <TimeAnalysis>[].obs;
  final commonIssues = <CommonIssue>[].obs;
  final maintenanceFrequency = <MaintenanceFrequency>[].obs;
  final costAnalysis = <CostAnalysis>[].obs; // Placeholder

  // Available filter options
  final availableSectors = <String>[].obs;
  final availableCarTypes = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    updateDateRangeControllerText();
    loadCars(); // loadCars will call filter and analytics initially

    // Listen to filter changes -> Trigger filterEntries -> Trigger generateAnalytics
    ever(startDate, (_) {
      updateDateRangeControllerText(); // Update text field when dates change
      filterEntries();
      // generateAnalytics() is now called inside filterEntries
    });
    ever(endDate, (_) {
      updateDateRangeControllerText(); // Update text field when dates change
      filterEntries();
      // generateAnalytics() is now called inside filterEntries
    });
    ever(selectedStatus, (_) => filterEntries());
    ever(selectedSector, (_) => filterEntries());
    ever(selectedCarType, (_) => filterEntries());
    ever(searchQuery, (_) => filterEntries());

    // Initial analytics generation after first loadCars completes
    // Handled by loadCars calling filterEntries, which now calls generateAnalytics
  }

  @override
  void onClose() {
    dateRangeController.dispose();
    super.onClose();
  }

  void updateDateRangeControllerText() {
    dateRangeController.text =
    '${DateFormat('yyyy-MM-dd').format(startDate.value)} - ${DateFormat('yyyy-MM-dd').format(endDate.value)}';
  }

  Future<void> loadCars() async {
    try {
      isLoading.value = true;
      final allCars = await carService.getCars();
      cars.assignAll(allCars);

      // Extract available sectors and car types
      availableSectors
          .assignAll(allCars.map((car) => car.sectorName).toSet().toList());
      availableCarTypes
          .assignAll(allCars.map((car) => car.carType).toSet().toList());

      filterEntries(); // This will now also trigger analytics
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to load cars'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
      Logger().e('Failed to load cars: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void filterEntries() {
    final List<WorkshopReportEntry> entries = [];

    // Ensure valid date range if possible, though picker should handle this
    if (startDate.value.isAfter(endDate.value)) {
      // Handle error or swap dates if needed
      Logger().w('Start date is after end date');
      return; // Or swap dates
    }

    final start =
        startDate.value.startOfDay; // Ensure start is beginning of day
    final end = endDate.value.endOfDay; // Ensure end is end of day

    final currentSearchQuery =
    searchQuery.value.trim().toLowerCase(); // Trim search query

    for (final car in cars) {
      for (final entry in car.workshopHistory) {
        for (final status in entry.statuses) {
          final statusDate = status.createAt;

          // Check if status date is within selected range (inclusive of end day)
          // statusDate must be >= start AND <= end
          if (statusDate.isBefore(start) || statusDate.isAfter(end)) {
            continue;
          }

          // Apply status filter if selected
          if (selectedStatus.value != null &&
              status.status != selectedStatus.value) {
            continue;
          }

          // Apply sector filter if selected
          if (selectedSector.value != null &&
              car.sectorName != selectedSector.value) {
            continue;
          }

          // Apply car type filter if selected
          if (selectedCarType.value != null &&
              car.carType != selectedCarType.value) {
            continue;
          }

          // Apply search filter if any
          if (currentSearchQuery.isNotEmpty) {
            final hasNotes = status.notes != null && status.notes!.isNotEmpty;
            if (!car.plateNumber.toLowerCase().contains(currentSearchQuery) &&
                !car.carModel.toLowerCase().contains(currentSearchQuery) &&
                !(hasNotes &&
                    status.notes!.toLowerCase().contains(currentSearchQuery))) {
              continue;
            }
          }

          entries.add(WorkshopReportEntry(
            car: car,
            workshopEntry: entry,
            status: status,
          ));
        }
      }
    }

    // Sort entries based on current sort option
    sortEntries(entries);
    filteredEntries.assignAll(entries);

    // Generate analytics based on the newly filtered entries
    generateAnalytics();
  }

  void setDateRange(DateTime start, DateTime end) {
    startDate.value = start.startOfDay; // Ensure start is beginning of day
    endDate.value = end.endOfDay; // Ensure end is end of day
    // filterEntries and generateAnalytics are triggered by the ever listeners
  }

  void applyDateRange() {
    // This method is primarily for when the date picker is closed/applied
    // The ever listeners for startDate/endDate already trigger filterEntries
    // which now includes updating the text field and analytics.
    // This method can essentially just call filterEntries() for clarity,
    // but the ever listener handles the actual reactive update flow.
    // updateDateRangeControllerText(); // Already done by ever listener
    // filterEntries(); // Already done by ever listener
    // generateAnalytics(); // Already done by filterEntries
  }

  void clearFilters() {
    selectedStatus.value = null;
    selectedSector.value = null;
    selectedCarType.value = null;
    searchQuery.value = '';
    setDateRange(
      DateTime.now().subtract(const Duration(days: 7)),
      DateTime.now(),
    );
    // setDateRange triggers filterEntries -> generateAnalytics
  }

  // Helper method to sort entries
  void sortEntries([List<WorkshopReportEntry>? entriesToSort]) {
    final entries = entriesToSort ?? filteredEntries;

    // Create a copy if sorting the live filteredEntries list to avoid modifying it while observing
    final List<WorkshopReportEntry> sortedList = List.from(entries);

    switch (sortOption.value) {
      case SortOption.dateDesc:
        sortedList
            .sort((a, b) => b.status.createAt.compareTo(a.status.createAt));
        break;
      case SortOption.dateAsc:
        sortedList
            .sort((a, b) => a.status.createAt.compareTo(b.status.createAt));
        break;
      case SortOption.statusAsc:
        sortedList.sort(
                (a, b) => a.status.status.index.compareTo(b.status.status.index));
        break;
      case SortOption.statusDesc:
        sortedList.sort(
                (a, b) => b.status.status.index.compareTo(a.status.status.index));
        break;
    }

    if (entriesToSort == null) {
      // Only update the observable list if sorting the main filteredEntries
      filteredEntries.assignAll(sortedList);
    } else {
      // If sorting a temporary list (like inside filterEntries), return it or use it
      // The current filterEntries logic assigns the sorted temp list, which is fine.
    }
  }

  void generateAnalytics() {
    // Ensure this is only called after filteredEntries is updated
    if (filteredEntries.isEmpty) {
      Logger().i('No filtered entries, clearing analytics.');
      statusDistribution.clear();
      dailyActivity.clear();
      sectorDistribution.clear();
      carTypeDistribution.clear();
      timeInWorkshopAnalysis
          .clear(); // Clear these too if they are based on filtered data
      commonIssues.clear();
      maintenanceFrequency
          .clear(); // Clear this too if it is based on filtered data
      costAnalysis.clear(); // Clear this too
      return;
    }

    _generateStatusDistribution();
    _generateDailyActivity(); // Fixed logic
    _generateSectorDistribution();
    _generateCarTypeDistribution();
    _generateTimeInWorkshopAnalysis(); // Modified to use filtered context
    _generateCommonIssues();
    _generateMaintenanceFrequency(); // Modified to use filtered context
    _generateCostAnalysis(); // Placeholder still
  }

  void _generateStatusDistribution() {
    final counts = <CarStatus, int>{};

    for (final entry in filteredEntries) {
      counts.update(
        entry.status.status,
            (value) => value + 1,
        ifAbsent: () => 1,
      );
    }

    statusDistribution.assignAll(
        counts.entries.map((e) => StatusCount(e.key, e.value)).toList());
  }

  void _generateDailyActivity() {
    final counts = <DateTime, int>{};

    // Initialize counts for each day in the range
    // Using startOfDay to ensure we count occurrences per day
    var currentDate = startDate.value.startOfDay;
    final end = endDate.value.startOfDay; // Use startOfDay for comparison loop

    while (currentDate.isAtSameMomentAs(end) || currentDate.isBefore(end)) {
      counts[currentDate] = 0;
      currentDate = currentDate.add(const Duration(days: 1));
    }

    // Count entries for each date based on filteredEntries
    for (final entry in filteredEntries) {
      final entryDate = entry.status.createAt.startOfDay;
      // Only count if the entryDate is within the initialised range (should be due to filter)
      if (counts.containsKey(entryDate)) {
        counts.update(entryDate, (value) => value + 1);
      } else {
        // This case shouldn't happen if filtering is correct, but as a fallback
        // it means an entry outside the range got through or range init is wrong.
        // Add it if necessary, though it violates range expectation.
        counts[entryDate] = 1;
        Logger().w(
            'Entry date ${entryDate} outside expected range for DailyActivity');
      }
    }

    // Sort by date before assigning
    final sortedDates = counts.keys.toList()..sort();

    dailyActivity.assignAll(
        sortedDates.map((date) => DailyActivity(date, counts[date]!)).toList());
  }

  void _generateSectorDistribution() {
    final counts = <String, int>{};

    for (final entry in filteredEntries) {
      // Ensure sectorName is not null or empty if it could be
      final sectorName = entry.car.sectorName ?? 'Unknown Sector'.tr;
      counts.update(
        sectorName,
            (value) => value + 1,
        ifAbsent: () => 1,
      );
    }

    sectorDistribution.assignAll(
        counts.entries.map((e) => SectorCount(e.key, e.value)).toList());
  }

  void _generateCarTypeDistribution() {
    final counts = <String, int>{};

    for (final entry in filteredEntries) {
      // Ensure carType is not null or empty if it could be
      final carType = entry.car.carType ?? 'Unknown Type'.tr;
      counts.update(
        carType,
            (value) => value + 1,
        ifAbsent: () => 1,
      );
    }

    carTypeDistribution.assignAll(
        counts.entries.map((e) => CarTypeCount(e.key, e.value)).toList());
  }

  // Modified to analyze time in workshop for visits *with statuses* in the filtered range
  void _generateTimeInWorkshopAnalysis() {
    final carModelDurations = <String, List<double>>{};
    final processedHistoryEntries =
    <String>{}; // To avoid double counting history entries

    for (final entry in filteredEntries) {
      final car = entry.car;
      final workshopEntry = entry.workshopEntry;
      final historyEntryId =
          '${car.plateNumber}_${workshopEntry.id}'; // Unique ID for a workshop visit

      // If we've already processed this specific workshop history entry for this car
      if (processedHistoryEntries.contains(historyEntryId)) {
        continue;
      }

      // Check if this workshop entry has at least a start and end status defined *anywhere*
      // (not just within the filtered range, but we only consider history entries
      // that *have* a status within the filtered range, due to iterating filteredEntries)
      final validStatuses = workshopEntry.statuses.toList()
        ..sort((a, b) => a.createAt.compareTo(b.createAt));

      if (validStatuses.length < 2) {
        Logger().w(
            'Workshop entry ${historyEntryId} has less than 2 statuses, cannot calculate duration.');
        continue;
      }

      final firstStatus = validStatuses.first;
      final lastStatus = validStatuses.last;
      final duration = lastStatus.createAt
          .difference(firstStatus.createAt)
          .inDays
          .toDouble();

      // Group by car model and add the duration of this specific workshop visit
      final carModel = car.carModel ?? 'Unknown Model'.tr;
      carModelDurations.update(
        carModel,
            (list) => [...list, duration],
        ifAbsent: () => [duration],
      );

      // Mark this workshop entry as processed for this car
      processedHistoryEntries.add(historyEntryId);
    }

    // Calculate average duration per car model based on the relevant history entries found
    timeInWorkshopAnalysis.assignAll(carModelDurations.entries
        .map((e) => TimeAnalysis(
      e.key,
      e.value.reduce((a, b) => a + b) / e.value.length,
    ))
        .toList());
  }

  // Modified to count unique workshop history entries within the filtered range per car model
  void _generateMaintenanceFrequency() {
    final carModelFrequencies =
    <String, Set<String>>{}; // Use a Set to count unique history entries

    for (final entry in filteredEntries) {
      final car = entry.car;
      final workshopEntry = entry.workshopEntry;
      final historyEntryId =
          '${car.plateNumber}_${workshopEntry.id}'; // Unique ID for a workshop visit

      final carModel = car.carModel ?? 'Unknown Model'.tr;

      carModelFrequencies.update(
        carModel,
            (set) {
          set.add(historyEntryId);
          return set;
        },
        ifAbsent: () => {historyEntryId},
      );
    }

    // Convert counts of unique history entry IDs per model to the final list
    maintenanceFrequency.assignAll(carModelFrequencies.entries
        .map((e) => MaintenanceFrequency(
        e.key, e.value.length)) // Count of unique entries
        .toList());
  }

  void _generateCommonIssues() {
    final issueCounts = <String, int>{};
    // Calculate total *relevant* entries for percentage, maybe those with notes?
    final entriesWithNotes = filteredEntries
        .where((e) => e.status.notes != null && e.status.notes!.isNotEmpty)
        .toList();
    final totalRelevantEntries = entriesWithNotes.length;

    if (totalRelevantEntries == 0) {
      commonIssues.clear();
      return;
    }

    for (final entry in entriesWithNotes) {
      final notes = entry.status.notes!.toLowerCase();
      String? issue;

      // --- Basic Keyword Matching (as in original, but consider limitations) ---
      if (notes.contains('engine')) {
        issue = 'Engine Problem';
      } else if (notes.contains('brake')) {
        issue = 'Brake Issue';
      } else if (notes.contains('حرارة')) {
        // Handles Arabic
        issue = 'حرارة (Overheating)';
      } else if (notes.contains('tire') || notes.contains('tyre')) {
        issue = 'Tire Issue';
      } else if (notes.contains('battery')) {
        issue = 'Battery Problem';
      } else if (notes.contains('electr')) {
        issue = 'Electrical Issue';
      } else if (notes.contains('oil')) {
        issue = 'Oil Change/Leak';
      } else if (notes.contains('maintenance') || notes.contains('service')) {
        issue = 'Routine Maintenance/Service';
      } else {
        // Default to the note itself if no keywords found, or a generic "Other"
        // Using the note might create too many categories.
        // Using "Other" is better for charts, but loses detail.
        // Let's stick to the note for now, but acknowledge this could be refined.
        issue = notes.isNotEmpty
            ? notes
            : 'No Specific Issue Noted'
            .tr; // Fallback for empty notes that passed filter?
      }

      issueCounts.update(issue, (value) => value + 1, ifAbsent: () => 1);
    }

    commonIssues.assignAll(issueCounts.entries
        .map((e) => CommonIssue(
      e.key,
      e.value,
      (e.value /
          totalRelevantEntries *
          100), // Percentage of entries with notes
    ))
        .toList());
  }

  void _generateCostAnalysis() {
    // This is a placeholder - in a real app you'd have actual cost data
    // If costs were associated with WorkshopEntry or WorkshopEntryStatus,
    // you would sum them up here for the filteredEntries.
    costAnalysis.clear(); // Clear placeholder data if any
    // Example:
    // double totalCost = 0;
    // for (final entry in filteredEntries) {
    //    // Assuming WorkshopEntry has a cost property
    //    totalCost += entry.workshopEntry.cost ?? 0;
    // }
    // costAnalysis.assignAll([CostAnalysis('Total Cost (Filtered)', totalCost)]);
  }

  Future<void> exportToPDF() async {
    try {
      isLoading.value = true;

      final fileExportService = Get.find<FileExportService>();
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(startDate.value.startOfDay)}_to_${DateFormat('yyyy-MM-dd').format(endDate.value.startOfDay)}'; // Format dates consistently

      // Export the file
      await fileExportService.exportWorkshopReportToPdf(
        entries: filteredEntries,
        startDate: startDate.value.startOfDay, // Pass start of day
        endDate: endDate.value.endOfDay, // Pass end of day
        statusDistribution: statusDistribution,
        sectorDistribution: sectorDistribution,
        fileName: 'workshop_report_$dateStr.pdf',
      );

      Get.snackbar(
        'Success'.tr,
        'PDF report generated successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[400],
        colorText: Colors.white,
      );
    } catch (e, s) {
      // Catch stack trace for better debugging
      Logger().e('Failed to generate PDF report', error: e, stackTrace: s);
      Get.snackbar(
        'Error'.tr,
        'Failed to generate PDF : ${e.toString()}'
            .tr, // Use toString for cleaner message
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> exportToExcel() async {
    try {
      isLoading.value = true;
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(startDate.value.startOfDay)}_to_${DateFormat('yyyy-MM-dd').format(endDate.value.startOfDay)}'; // Format dates consistently

      final fileExportService = Get.find<FileExportService>();

      // Export the file
      await fileExportService.exportWorkshopReportToExcel(
        entries: filteredEntries,
        startDate: startDate.value.startOfDay, // Pass start of day
        endDate: endDate.value.endOfDay, // Pass end of day
        statusDistribution: statusDistribution,
        sectorDistribution: sectorDistribution,
        fileName: 'workshop_report_$dateStr.xlsx',
      );

      Get.snackbar(
        'Success'.tr,
        'Excel report generated successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[400],
        colorText: Colors.white,
      );
    } catch (e, s) {
      Logger().e('Failed to generate Excel report', error: e, stackTrace: s);
      Get.snackbar(
        'Error'.tr,
        'Failed to generate Excel report: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> exportToCSV() async {
    try {
      isLoading.value = true;
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(startDate.value.startOfDay)}_to_${DateFormat('yyyy-MM-dd').format(endDate.value.startOfDay)}'; // Format dates consistently

      final fileExportService = Get.find<FileExportService>();

      // Export the file
      await fileExportService.exportWorkshopReportToCsv(
        entries: filteredEntries,
        fileName: 'workshop_report_$dateStr.csv',
      );

      Get.snackbar(
        'Success'.tr,
        'CSV report generated successfully'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[400],
        colorText: Colors.white,
      );
    } catch (e, s) {
      Logger().e('Failed to generate CSV report', error: e, stackTrace: s);
      Get.snackbar(
        'Error'.tr,
        'Failed to generate CSV report: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Generate a status summary report for selected car statuses
  Future<void> generateStatusSummaryReport({
    required DateTime start,
    required DateTime end,
    String? sectorId,
    List<CarStatus>? selectedStatuses,
    required String exportFormat,
  }) async {
    try {
      isLoading.value = true;

      // Format date string for file name
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(start)}_to_${DateFormat('yyyy-MM-dd').format(end)}';

      // Get all cars
      final allCars = await carService.getCars();

      // Filter cars by date range and sector
      final filteredCars = allCars.where((car) {
        // Check if car is within date range
        final carDate = car.lasUpdate ?? car.createAt;
        if (carDate.isBefore(start.startOfDay) ||
            carDate.isAfter(end.endOfDay)) {
          return false;
        }

        // Check if car is in selected sector
        if (sectorId != null &&
            sectorId.isNotEmpty &&
            car.sectorName != sectorId) {
          return false;
        }

        return true;
      }).toList();

      // Group cars by status
      final Map<CarStatus, List<Car>> carsByStatus = {};

      // Use selected statuses or default to common ones if not provided
      final statuses = selectedStatuses ?? [
        CarStatus.active,
        CarStatus.sentRequest,
        CarStatus.pending,
        CarStatus.done,
      ];

      // Group cars by status
      for (final status in statuses) {
        carsByStatus[status] = filteredCars
            .where((car) => car.status == status)
            .toList();
      }

      final fileExportService = Get.find<FileExportService>();

      if (exportFormat == 'excel') {
        await fileExportService.exportStatusSummaryReportToExcel(
          carsByStatus: carsByStatus,
          startDate: start,
          endDate: end,
          sectorName: sectorId,
          fileName: 'status_summary_report_$dateStr.xlsx',
        );

        Get.snackbar(
          'Success'.tr,
          'Excel report generated successfully'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Error'.tr,
          'Unsupported export format: $exportFormat'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[400],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[400],
        colorText: Colors.white,
      );
      Logger().e('Failed to generate status summary report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Calculate duration for a workshop entry from creation until it becomes active
  Duration? calculateWorkshopEntryDuration(WorkshopEntry entry) {
    // Sort statuses by creation date
    final sortedStatuses = entry.statuses.toList()
      ..sort((a, b) => a.createAt.compareTo(b.createAt));

    if (sortedStatuses.isEmpty) return null;

    final startDate = sortedStatuses.first.createAt;
    final endDate = sortedStatuses.last.createAt;

    return endDate.difference(startDate);
  }

  /// Generate a workshop entries report
  Future<void> generateWorkshopEntriesReport({
    required DateTime start,
    required DateTime end,
    String? sectorId,
    required String exportFormat,
  }) async {
    try {
      isLoading.value = true;

      // Format date string for file name
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(start)}_to_${DateFormat('yyyy-MM-dd').format(end)}';

      // Get all cars
      final allCars = await carService.getCars();

      // Filter cars by sector if specified
      final filteredCars = sectorId != null && sectorId.isNotEmpty
          ? allCars.where((car) => car.sectorName == sectorId).toList()
          : allCars;

      // Create a list to store all workshop entries with their details
      final List<Map<String, dynamic>> workshopEntries = [];

      for (final car in filteredCars) {
        for (final entry in car.workshopHistory) {
          // Skip entries outside the date range
          if (entry.statuses.isEmpty) continue;
          
          final firstStatus = entry.statuses.first;
          if (firstStatus.createAt.isBefore(start.startOfDay) ||
              firstStatus.createAt.isAfter(end.endOfDay)) {
            continue;
          }

          // Calculate duration
          final duration = calculateWorkshopEntryDuration(entry);
          
          // Get status progression
          final statusProgression = entry.statuses
              .map((status) => status.status.toString().split('.').last)
              .join(' → ');

          // Get the last status
          final lastStatus = entry.statuses.last;

          workshopEntries.add({
            'carPlate': '${car.plateNumber}-${car.plateCharacters}',
            'carModel': car.carModel,
            'sector': car.sectorName,
            'maintenanceType': entry.maintenance_type ?? 'غير محدد',
            'startDate': firstStatus.createAt,
            'endDate': lastStatus.createAt,
            'duration': duration?.inDays ?? 0,
            'statusProgression': statusProgression,
            'finalStatus': lastStatus.status.toString().split('.').last,
            'notes': lastStatus.notes ?? '',
            'senderName': lastStatus.senderName,
          });
        }
      }

      final fileExportService = Get.find<FileExportService>();

      if (exportFormat == 'excel') {
        await fileExportService.exportWorkshopEntriesReportToExcel(
          entries: workshopEntries,
          startDate: start,
          endDate: end,
          sectorName: sectorId,
          fileName: 'workshop_entries_report_$dateStr.xlsx',
        );

        Get.snackbar(
          'Success'.tr,
          'Excel report generated successfully'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Error'.tr,
          'Unsupported export format: $exportFormat'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[400],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'Failed to generate report: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[400],
        colorText: Colors.white,
      );
      Logger().e('Failed to generate workshop entries report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Generate an advanced workshop report for all cars and their workshop entries
  Future<void> generateAdvancedWorkshopReport({
    required DateTime start,
    required DateTime end,
    String? sectorId,
    required String exportFormat,
  }) async {
    try {
      isLoading.value = true;
      final dateStr =
          '${DateFormat('yyyy-MM-dd').format(start)}_to_${DateFormat('yyyy-MM-dd').format(end)}';

      // Get all cars
      final allCars = await carService.getCars();

      // Filter cars by sector if specified
      final filteredCars = sectorId != null && sectorId.isNotEmpty
          ? allCars.where((car) => car.sectorName == sectorId).toList()
          : allCars;

      // Optionally, filter workshop entries by date range if needed (currently, all entries for each car are included)

      if (exportFormat == 'pdf') {
        final pdfExportService = Get.find<PdfExportService>();
        final pdfBytes = await pdfExportService.generateAdvancedWorkshopReport(
          cars: filteredCars,
          startDate: start,
          endDate: end,
        );
        final fileExportService = Get.find<FileExportService>();
        await fileExportService.exportFile(
          pdfBytes,
          'advanced_workshop_report_$dateStr.pdf',
        );
        Get.snackbar(
          'Success'.tr,
          'تم إنشاء تقرير PDF بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      } else if (exportFormat == 'excel') {
        final fileExportService = Get.find<FileExportService>();
        final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());
        
        // Filter cars that have workshop history
        final carsWithHistory = filteredCars.where((car) => car.workshopHistory.isNotEmpty).toList();
        
        // Create Excel workbook
        final Workbook workbook = Workbook();
        final Worksheet sheet = workbook.worksheets[0];

        // Set RTL direction for the sheet
        sheet.enableSheetCalculations();
        sheet.isRightToLeft = true;

        // Add title with styling
        final Range titleRange = sheet.getRangeByIndex(1, 1, 1, 7);
        titleRange.setText('تقرير متقدم لسجلات الورشة');
        titleRange.merge();
        titleRange.cellStyle.fontSize = 16;
        titleRange.cellStyle.bold = true;
        titleRange.cellStyle.hAlign = HAlignType.center;
        titleRange.cellStyle.backColor = '#C00000';  // Dark red
        titleRange.cellStyle.fontColor = '#FFFFFF';

        // Add date range with styling
        final Range dateRange = sheet.getRangeByIndex(2, 1, 2, 7);
        dateRange.setText('الفترة: ${DateFormat('yyyy-MM-dd').format(start)} - ${DateFormat('yyyy-MM-dd').format(end)}');
        dateRange.merge();
        dateRange.cellStyle.fontSize = 12;
        dateRange.cellStyle.hAlign = HAlignType.center;
        dateRange.cellStyle.backColor = '#FFE4E4';  // Light red
        dateRange.cellStyle.fontColor = '#000000';

        // Add summary section header
        final Range summaryHeaderRange = sheet.getRangeByIndex(4, 1, 4, 7);
        summaryHeaderRange.setText('ملخص الإحصائيات');
        summaryHeaderRange.merge();
        summaryHeaderRange.cellStyle.fontSize = 14;
        summaryHeaderRange.cellStyle.bold = true;
        summaryHeaderRange.cellStyle.hAlign = HAlignType.center;
        summaryHeaderRange.cellStyle.backColor = '#C00000';  // Dark red
        summaryHeaderRange.cellStyle.fontColor = '#FFFFFF';

        // Add summary data with styling
        sheet.getRangeByIndex(5, 1).setText('عدد المركبات مع سجلات ورشة');
        sheet.getRangeByIndex(5, 2).setText(carsWithHistory.length.toString());
        sheet.getRangeByIndex(6, 1).setText('إجمالي سجلات الورشة');
        sheet.getRangeByIndex(6, 2).setText(carsWithHistory.fold(0, (sum, car) => sum + car.workshopHistory.length).toString());

        final Range summaryDataRange = sheet.getRangeByIndex(5, 1, 6, 2);
        summaryDataRange.cellStyle.fontSize = 11;
        summaryDataRange.cellStyle.hAlign = HAlignType.center;
        summaryDataRange.cellStyle.backColor = '#FFF0F0';  // Very light red
        summaryDataRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        summaryDataRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        // Add table headers
        int currentRow = 8;
        final Range tableHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 7);
        tableHeaderRange.cellStyle.fontSize = 11;
        tableHeaderRange.cellStyle.bold = true;
        tableHeaderRange.cellStyle.hAlign = HAlignType.center;
        tableHeaderRange.cellStyle.backColor = '#FFE4E4';  // Light red
        tableHeaderRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        tableHeaderRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        sheet.getRangeByIndex(currentRow, 1).setText('القطاع');
        sheet.getRangeByIndex(currentRow, 2).setText('اللوحة');
        sheet.getRangeByIndex(currentRow, 3).setText('الموديل');
        sheet.getRangeByIndex(currentRow, 4).setText('الحالة الحالية');
        sheet.getRangeByIndex(currentRow, 5).setText('تاريخ آخر تحديث');
        sheet.getRangeByIndex(currentRow, 6).setText('عدد سجلات الورشة');
        sheet.getRangeByIndex(currentRow, 7).setText('آخر سجل ورشة');
        currentRow++;

        // Add car data
        for (var car in carsWithHistory) {
          final Range carRowRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 7);
          carRowRange.cellStyle.fontSize = 11;
          carRowRange.cellStyle.hAlign = HAlignType.center;
          carRowRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
          carRowRange.cellStyle.borders.all.color = '#C00000';  // Dark red

          sheet.getRangeByIndex(currentRow, 1).setText(car.sectorName);
          sheet.getRangeByIndex(currentRow, 2).setText('${car.plateNumber}-${car.plateCharacters}');
          sheet.getRangeByIndex(currentRow, 3).setText(car.carModel);
          sheet.getRangeByIndex(currentRow, 4).setText(car.status.name.tr);
          sheet.getRangeByIndex(currentRow, 5).setText(DateFormat('yyyy-MM-dd HH:mm').format(car.createAt));
          sheet.getRangeByIndex(currentRow, 6).setText(car.workshopHistory.length.toString());

          // Get last workshop entry details
          final lastEntry = car.workshopHistory.last;
          final lastStatus = lastEntry.statuses.last;
          final lastEntryText = '${lastEntry.maintenance_type ?? 'غير محدد'} - ${lastStatus.status.name.tr}';
          sheet.getRangeByIndex(currentRow, 7).setText(lastEntryText);

          currentRow++;
        }

        // Add workshop history section
        currentRow += 2;
        final Range historyHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 8);
        historyHeaderRange.setText('تفاصيل سجلات الورشة');
        historyHeaderRange.merge();
        historyHeaderRange.cellStyle.fontSize = 14;
        historyHeaderRange.cellStyle.bold = true;
        historyHeaderRange.cellStyle.hAlign = HAlignType.center;
        historyHeaderRange.cellStyle.backColor = '#C00000';  // Dark red
        historyHeaderRange.cellStyle.fontColor = '#FFFFFF';
        currentRow++;

        // Add workshop history headers
        final Range historyTableHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 8);
        historyTableHeaderRange.cellStyle.fontSize = 11;
        historyTableHeaderRange.cellStyle.bold = true;
        historyTableHeaderRange.cellStyle.hAlign = HAlignType.center;
        historyTableHeaderRange.cellStyle.backColor = '#FFE4E4';  // Light red
        historyTableHeaderRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        historyTableHeaderRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        sheet.getRangeByIndex(currentRow, 1).setText('القطاع');
        sheet.getRangeByIndex(currentRow, 2).setText('اللوحة');
        sheet.getRangeByIndex(currentRow, 3).setText('نوع الصيانة');
        sheet.getRangeByIndex(currentRow, 4).setText('الحالات');
        sheet.getRangeByIndex(currentRow, 5).setText('تاريخ البدء');
        sheet.getRangeByIndex(currentRow, 6).setText('المدة الكلية (أيام)');
        sheet.getRangeByIndex(currentRow, 7).setText('مدة الصيانة (أيام)');
        sheet.getRangeByIndex(currentRow, 8).setText('الملاحظات');
        currentRow++;

        // Add workshop history data
        for (var car in carsWithHistory) {
          for (var entry in car.workshopHistory) {
            // Sort statuses by date
            final sortedStatuses = entry.statuses.toList()
              ..sort((a, b) => a.createAt.compareTo(b.createAt));
            
            // Calculate total duration
            final startDate = sortedStatuses.first.createAt;
            final endDate = sortedStatuses.last.createAt;
            final duration = endDate.difference(startDate).inDays;

            // Calculate maintenance duration (from creation to active status)
            String maintenanceDuration = 'N/A';
            final activeStatus = sortedStatuses.firstWhere(
              (status) => status.status == CarStatus.active,
              orElse: () => sortedStatuses.first,  // Return first status if no active status found
            );
            if (activeStatus.status == CarStatus.active) {
              final maintenanceDays = activeStatus.createAt.difference(startDate).inDays;
              maintenanceDuration = maintenanceDays.toString();
            }

            // Create status progression text
            final hasSentRequest = sortedStatuses.any((status) => status.status == CarStatus.sentRequest) ? '1' : '0';
            final hasPending = sortedStatuses.any((status) => status.status == CarStatus.pending) ? '1' : '0';
            final hasActive = sortedStatuses.any((status) => status.status == CarStatus.active) ? '1' : '0';
            final statusProgression = 'طلب صيانة: $hasSentRequest | تحت الاجراء: $hasPending | نشط: $hasActive';

            final Range historyRowRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 8);
            historyRowRange.cellStyle.fontSize = 11;
            historyRowRange.cellStyle.hAlign = HAlignType.center;
            historyRowRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
            historyRowRange.cellStyle.borders.all.color = '#C00000';  // Dark red

            sheet.getRangeByIndex(currentRow, 1).setText(car.sectorName);
            sheet.getRangeByIndex(currentRow, 2).setText('${car.plateNumber}-${car.plateCharacters}');
            sheet.getRangeByIndex(currentRow, 3).setText(entry.maintenance_type?.tr ?? 'غير محدد');
            sheet.getRangeByIndex(currentRow, 4).setText(statusProgression);
            sheet.getRangeByIndex(currentRow, 5).setText(DateFormat('yyyy-MM-dd').format(startDate));
            sheet.getRangeByIndex(currentRow, 6).setText(duration.toString());
            sheet.getRangeByIndex(currentRow, 7).setText(maintenanceDuration);
            sheet.getRangeByIndex(currentRow, 8).setText(sortedStatuses.last.notes ?? '');

            currentRow++;
          }
        }

        // Auto-fit columns
        sheet.getRangeByIndex(1, 1, currentRow, 8).autoFitColumns();

        // Set column widths if needed
        sheet.getRangeByIndex(1, 1, 1, 1).columnWidth = 20;  // Sector
        sheet.getRangeByIndex(1, 2, 1, 2).columnWidth = 15;  // Plate
        sheet.getRangeByIndex(1, 3, 1, 3).columnWidth = 20;  // Maintenance Type
        sheet.getRangeByIndex(1, 4, 1, 4).columnWidth = 50;  // Status Progression
        sheet.getRangeByIndex(1, 5, 1, 5).columnWidth = 15;  // Start Date
        sheet.getRangeByIndex(1, 6, 1, 6).columnWidth = 15;  // Total Duration
        sheet.getRangeByIndex(1, 7, 1, 7).columnWidth = 15;  // Maintenance Duration
        sheet.getRangeByIndex(1, 8, 1, 8).columnWidth = 30;  // Notes

        // Export the Excel file
        final List<int> bytes = workbook.saveAsStream();
        workbook.dispose();

        await fileExportService.exportFile(
          Uint8List.fromList(bytes),
          'advanced_workshop_report_$dateStr.xlsx',
        );

        Get.snackbar(
          'Success'.tr,
          'تم إنشاء تقرير Excel بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Error'.tr,
          'تنسيق التصدير غير مدعوم: $exportFormat'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[400],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'فشل في إنشاء التقرير: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[400],
        colorText: Colors.white,
      );
      Logger().e('Failed to generate advanced workshop report: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Generate a daily report showing cars' status for the last 24 hours
  Future<void> generateDailyReport({String format = 'pdf'}) async {
    try {
      isLoading.value = true;

      // Get all cars
      final allCars = await carService.getCars();

      // Get today's date range (00:00 to now)
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = now;

      // Filter cars that had status changes in the last 24 hours
      final relevantCars = allCars.where((car) {
        final lastStatusChange = car.createAt;
        return lastStatusChange.isAfter(startOfDay) && lastStatusChange.isBefore(endOfDay);
      }).toList();

      if (format == 'pdf') {
        // Generate the PDF report
        final pdfExportService = Get.find<PdfExportService>();
        final pdfBytes = await pdfExportService.generateDailyReport(
          cars: allCars,
        );

        // Export the PDF file
        final fileExportService = Get.find<FileExportService>();
        final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());
        await fileExportService.exportFile(
          pdfBytes,
          'daily_report_$dateStr.pdf',
        );

        Get.snackbar(
          'Success'.tr,
          'تم إنشاء التقرير اليومي PDF بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      } else if (format == 'excel') {
        // Export to Excel
        final fileExportService = Get.find<FileExportService>();
        final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());
        
        // Group cars by sector
        final Map<String, List<Car>> carsBySector = {};
        for (var car in relevantCars) {
          if (!carsBySector.containsKey(car.sectorName)) {
            carsBySector[car.sectorName] = [];
          }
          carsBySector[car.sectorName]!.add(car);
        }

        // Create Excel workbook
        final Workbook workbook = Workbook();
        final Worksheet sheet = workbook.worksheets[0];

        // Set RTL direction for the sheet
        sheet.enableSheetCalculations();
        sheet.isRightToLeft = true;

        // Add title with styling
        final Range titleRange = sheet.getRangeByIndex(1, 1, 1, 3);
        titleRange.setText('تقرير حالة المركبات اليومي');
        titleRange.merge();
        titleRange.cellStyle.fontSize = 16;
        titleRange.cellStyle.bold = true;
        titleRange.cellStyle.hAlign = HAlignType.center;
        titleRange.cellStyle.backColor = '#C00000';  // Dark red
        titleRange.cellStyle.fontColor = '#FFFFFF';

        // Add date range with styling
        final Range dateRange = sheet.getRangeByIndex(2, 1, 2, 3);
        dateRange.setText('الفترة: ${DateFormat('yyyy-MM-dd').format(startOfDay)} - ${DateFormat('yyyy-MM-dd HH:mm').format(endOfDay)}');
        dateRange.merge();
        dateRange.cellStyle.fontSize = 12;
        dateRange.cellStyle.hAlign = HAlignType.center;
        dateRange.cellStyle.backColor = '#FFE4E4';  // Light red
        dateRange.cellStyle.fontColor = '#000000';

        // Add summary section header
        final Range summaryHeaderRange = sheet.getRangeByIndex(4, 1, 4, 3);
        summaryHeaderRange.setText('ملخص الإحصائيات');
        summaryHeaderRange.merge();
        summaryHeaderRange.cellStyle.fontSize = 14;
        summaryHeaderRange.cellStyle.bold = true;
        summaryHeaderRange.cellStyle.hAlign = HAlignType.center;
        summaryHeaderRange.cellStyle.backColor = '#C00000';  // Dark red
        summaryHeaderRange.cellStyle.fontColor = '#FFFFFF';

        // Add summary data with styling
        sheet.getRangeByIndex(5, 1).setText('إجمالي المركبات');
        sheet.getRangeByIndex(5, 2).setText(allCars.length.toString());
        sheet.getRangeByIndex(6, 1).setText('تم تحديث الحالة');
        sheet.getRangeByIndex(6, 2).setText(relevantCars.length.toString());

        final Range summaryDataRange = sheet.getRangeByIndex(5, 1, 6, 2);
        summaryDataRange.cellStyle.fontSize = 11;
        summaryDataRange.cellStyle.hAlign = HAlignType.center;
        summaryDataRange.cellStyle.backColor = '#FFF0F0';  // Very light red
        summaryDataRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        summaryDataRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        // Add sector tables
        int currentRow = 8;
        for (var sectorEntry in carsBySector.entries) {
          final sectorName = sectorEntry.key;
          final sectorCars = sectorEntry.value;

          // Add sector header with styling
          final Range sectorHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
          sectorHeaderRange.setText('$sectorName (${sectorCars.length})');
          sectorHeaderRange.merge();
          sectorHeaderRange.cellStyle.fontSize = 13;
          sectorHeaderRange.cellStyle.bold = true;
          sectorHeaderRange.cellStyle.hAlign = HAlignType.center;
          sectorHeaderRange.cellStyle.backColor = '#E06666';  // Medium red
          sectorHeaderRange.cellStyle.fontColor = '#FFFFFF';
          currentRow++;

          // Add table headers with styling
          final Range tableHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
          tableHeaderRange.cellStyle.fontSize = 11;
          tableHeaderRange.cellStyle.bold = true;
          tableHeaderRange.cellStyle.hAlign = HAlignType.center;
          tableHeaderRange.cellStyle.backColor = '#FFE4E4';  // Light red
          tableHeaderRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
          tableHeaderRange.cellStyle.borders.all.color = '#C00000';  // Dark red

          sheet.getRangeByIndex(currentRow, 1).setText('اللوحة');
          sheet.getRangeByIndex(currentRow, 2).setText('الحالة');
          sheet.getRangeByIndex(currentRow, 3).setText('آخر تحديث');
          currentRow++;

          // Add car data with styling
          for (var car in sectorCars) {
            final Range carDataRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
            carDataRange.cellStyle.fontSize = 11;
            carDataRange.cellStyle.hAlign = HAlignType.center;
            carDataRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
            carDataRange.cellStyle.borders.all.color = '#C00000';  // Dark red

            sheet.getRangeByIndex(currentRow, 1).setText('${car.plateNumber}-${car.plateCharacters}');
            sheet.getRangeByIndex(currentRow, 2).setText(car.status.name.tr);
            sheet.getRangeByIndex(currentRow, 3).setText(DateFormat('yyyy-MM-dd HH:mm').format(car.createAt));
            currentRow++;
          }
          currentRow += 2; // Add spacing between sectors
        }

        // Add status summary section
        currentRow += 2; // Add extra spacing before summary

        // Add summary header
        final Range statusSummaryHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
        statusSummaryHeaderRange.setText('ملخص الحالات');
        statusSummaryHeaderRange.merge();
        statusSummaryHeaderRange.cellStyle.fontSize = 14;
        statusSummaryHeaderRange.cellStyle.bold = true;
        statusSummaryHeaderRange.cellStyle.hAlign = HAlignType.center;
        statusSummaryHeaderRange.cellStyle.backColor = '#C00000';  // Dark red
        statusSummaryHeaderRange.cellStyle.fontColor = '#FFFFFF';
        currentRow++;

        // Add table headers
        final Range statusTableHeaderRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
        statusTableHeaderRange.cellStyle.fontSize = 11;
        statusTableHeaderRange.cellStyle.bold = true;
        statusTableHeaderRange.cellStyle.hAlign = HAlignType.center;
        statusTableHeaderRange.cellStyle.backColor = '#FFE4E4';  // Light red
        statusTableHeaderRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        statusTableHeaderRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        sheet.getRangeByIndex(currentRow, 1).setText('الحالة');
        sheet.getRangeByIndex(currentRow, 2).setText('العدد');
        sheet.getRangeByIndex(currentRow, 3).setText('النسبة المئوية');
        currentRow++;

        // Calculate status counts
        final Map<CarStatus, int> statusCounts = {};
        for (var car in relevantCars) {
          statusCounts[car.status] = (statusCounts[car.status] ?? 0) + 1;
        }

        // Add status rows
        for (var status in CarStatus.values) {
          final count = statusCounts[status] ?? 0;
          final percentage = relevantCars.isEmpty ? 0 : (count / relevantCars.length * 100);

          final Range statusRowRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
          statusRowRange.cellStyle.fontSize = 11;
          statusRowRange.cellStyle.hAlign = HAlignType.center;
          statusRowRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
          statusRowRange.cellStyle.borders.all.color = '#C00000';  // Dark red

          sheet.getRangeByIndex(currentRow, 1).setText(status.name.tr);
          sheet.getRangeByIndex(currentRow, 2).setText(count.toString());
          sheet.getRangeByIndex(currentRow, 3).setText('${percentage.toStringAsFixed(1)}%');
          currentRow++;
        }

        // Add total row
        final Range totalRowRange = sheet.getRangeByIndex(currentRow, 1, currentRow, 3);
        totalRowRange.cellStyle.fontSize = 11;
        totalRowRange.cellStyle.bold = true;
        totalRowRange.cellStyle.hAlign = HAlignType.center;
        totalRowRange.cellStyle.backColor = '#FFE4E4';  // Light red
        totalRowRange.cellStyle.borders.all.lineStyle = LineStyle.thin;
        totalRowRange.cellStyle.borders.all.color = '#C00000';  // Dark red

        sheet.getRangeByIndex(currentRow, 1).setText('الإجمالي');
        sheet.getRangeByIndex(currentRow, 2).setText(relevantCars.length.toString());
        sheet.getRangeByIndex(currentRow, 3).setText('100%');

        // Auto-fit columns
        sheet.getRangeByIndex(1, 1, currentRow, 3).autoFitColumns();

        // Set column widths if needed
        sheet.getRangeByIndex(1, 1, 1, 1).columnWidth = 20;
        sheet.getRangeByIndex(1, 2, 1, 2).columnWidth = 15;
        sheet.getRangeByIndex(1, 3, 1, 3).columnWidth = 20;

        // Export the Excel file
        final List<int> bytes = workbook.saveAsStream();
        workbook.dispose();

        await fileExportService.exportFile(
          Uint8List.fromList(bytes),
          'daily_report_$dateStr.xlsx',
        );

        Get.snackbar(
          'Success'.tr,
          'تم إنشاء التقرير اليومي Excel بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[400],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error'.tr,
        'فشل في إنشاء التقرير اليومي: $e'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[400],
        colorText: Colors.white,
      );
      Logger().e('Failed to generate daily report: $e');
    } finally {
      isLoading.value = false;
    }
  }
}

// --- Add necessary Enums and Helper Classes if not already defined elsewhere ---
// (Keeping them here for completeness as they were in the original post)

// Enums for UI state
enum ChartType { column, pie }

enum ActivityChartType { line, area }

enum SectorChartType { pie, doughnut }

enum SortOption { dateDesc, dateAsc, statusAsc, statusDesc }

// Helper classes for analytics data
class StatusCount {
  final CarStatus status;
  final int count;

  StatusCount(this.status, this.count);
}

class DailyActivity {
  final DateTime date;
  final int count;

  DailyActivity(this.date, this.count);
}

class SectorCount {
  final String sectorName;
  final int count;

  SectorCount(this.sectorName, this.count);
}

class CarTypeCount {
  final String carType;
  final int count;

  CarTypeCount(this.carType, this.count);
}

class TimeAnalysis {
  final String category; // Represents Car Model
  final double days; // Average days in workshop

  TimeAnalysis(this.category, this.days);
}

class CommonIssue {
  final String issue;
  final int count;
  final double percentage;

  CommonIssue(this.issue, this.count, this.percentage);
}

class MaintenanceFrequency {
  final String carModel;
  final int frequency; // Count of distinct visits in filtered range

  MaintenanceFrequency(this.carModel, this.frequency);
}

class CostAnalysis {
  final String category;
  final double amount;

  CostAnalysis(this.category, this.amount);
}

// Helper class to group report data
class WorkshopReportEntry {
  final Car car;
  final WorkshopEntry workshopEntry; // The specific workshop visit
  final WorkshopEntryStatus status; // The specific status within that visit

  WorkshopReportEntry({
    required this.car,
    required this.workshopEntry,
    required this.status,
  });
}

// Example Date Extensions (add this to a utils/date_extensions.dart or similar)
extension DateTimeExtensions on DateTime {
  /// Returns a new [DateTime] instance with the time set to the beginning of the day (00:00:00).
  DateTime get startOfDay => DateTime(year, month, day);

  /// Returns a new [DateTime] instance with the time set to the end of the day (23:59:59.999).
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999);
}
