// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
// import 'package:lottie/lottie.dart';
//
// import '../../../data/models/car_model.dart';
// import '../../../data/models/user_model.dart';
// import '../../../routes/app_pages.dart';
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/language_switcher.dart';
//
// // Content views
// import 'dashboard_content.dart';
// import 'cars_content.dart';
// import 'sectors_content.dart';
// import 'users_content.dart';
// import 'maintenance_content.dart';
// import 'products_content.dart';
//
// class ModernDashboardView extends GetView<ModernDashboardController> {
//   const ModernDashboardView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     // Use app colors
//     final backgroundColor = AppColors.background;
//     final cardBackground = Colors.white;
//     final primaryText = Colors.black87;
//     final secondaryText = Colors.black54;
//
//     return Scaffold(
//       backgroundColor: backgroundColor,
//       body: LayoutBuilder(
//         builder: (context, constraints) {
//           // Responsive breakpoints
//           bool isDesktop = constraints.maxWidth > 900;
//           bool isTablet = constraints.maxWidth > 600 && constraints.maxWidth <= 900;
//           bool isMobile = constraints.maxWidth <= 600;
//
//           return Obx(() {
//             // Get RTL direction
//             final isRTL = controller.isRTL.value;
//
//             // Show loading state
//             if (controller.isLoading.value) {
//               return Center(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Lottie.asset(
//                       'assets/images/loading.json',
//                       width: 200,
//                       height: 200,
//                       fit: BoxFit.contain,
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       'loading'.tr,
//                       style: GoogleFonts.cairo(
//                         fontSize: 18,
//                         fontWeight: FontWeight.bold,
//                         color: AppColors.primary,
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             }
//
//             return Directionality(
//               textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//               child: Row(
//                 children: [
//                   // Left/Right sidebar (based on RTL)
//                   if (isDesktop || isTablet)
//                     _buildSidebar(
//                       backgroundColor,
//                       primaryText,
//                       secondaryText,
//                       isDesktop ? 250.w : 80.w,
//                       isDesktop,
//                       isRTL,
//                     ),
//
//                   // Main content
//                   Expanded(
//                     child: SingleChildScrollView(
//                       padding: EdgeInsets.all(isDesktop ? 24.r : 16.r),
//                       child: Column(
//                         crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//                         children: [
//                           // Header with search and language switcher
//                           Row(
//                             children: [
//                               // Search bar
//                               Expanded(
//                                 child: _buildSearchBar(isRTL),
//                               ),
//                               SizedBox(width: 16.w),
//                               // Language switcher
//                               LanguageSwitcher(
//                                 isRTL: isRTL,
//                                 onLanguageChanged: controller.changeLanguage,
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: 24.h),
//
//                           // Content based on current view
//                           Obx(() {
//                             switch (controller.currentView.value) {
//                               case 'dashboard':
//                                 return DashboardContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               case 'cars':
//                                 return CarsContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               case 'sectors':
//                                 return SectorsContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               case 'users':
//                                 return UsersContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               case 'maintenance':
//                                 return MaintenanceContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               case 'products':
//                                 return ProductsContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                               default:
//                                 return DashboardContent(
//                                   controller: controller,
//                                   isDesktop: isDesktop,
//                                   isTablet: isTablet,
//                                   isMobile: isMobile,
//                                   isRTL: isRTL,
//                                 );
//                             }
//                           }),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//       // Bottom navigation for mobile
//       bottomNavigationBar: LayoutBuilder(
//         builder: (context, constraints) {
//           bool isMobile = constraints.maxWidth <= 600;
//           if (isMobile) {
//             return Obx(() => _buildBottomNavBar(controller.currentView.value, controller.isRTL.value));
//           } else {
//             return const SizedBox.shrink();
//           }
//         },
//       ),
//       // Floating action button for adding new items
//       floatingActionButton: Obx(() {
//         // Only show FAB in specific views
//         final currentView = controller.currentView.value;
//         if (currentView == 'dashboard') return const SizedBox.shrink();
//
//         return FloatingActionButton(
//           backgroundColor: AppColors.primary,
//           child: const Icon(Icons.add, color: Colors.white),
//           onPressed: () {
//             // Handle action based on current view
//             switch (currentView) {
//               case 'cars':
//                 // Navigate to add car page
//                 Get.toNamed(Routes.CAR_FORM);
//                 break;
//               case 'sectors':
//                 // Navigate to add sector page
//                 Get.toNamed(Routes.SECTOR_FORM);
//                 break;
//               case 'users':
//                 // Navigate to add user page
//                 Get.toNamed(Routes.USER_FORM);
//                 break;
//               case 'maintenance':
//                 // Navigate to add maintenance page
//                 Get.toNamed(Routes.MAINTENANCE_FORM);
//                 break;
//               case 'products':
//                 // Navigate to add product page
//                 Get.toNamed(Routes.PRODUCT_FORM);
//                 break;
//             }
//           },
//         );
//       }),
//     );
//   }
//
//   // Build sidebar with navigation options
//   Widget _buildSidebar(
//     Color backgroundColor,
//     Color primaryText,
//     Color secondaryText,
//     double width,
//     bool isExpanded,
//     bool isRTL,
//   ) {
//     return Container(
//       width: width,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       padding: EdgeInsets.symmetric(vertical: 24.h),
//       child: Column(
//         children: [
//           // App logo and title
//           Padding(
//             padding: EdgeInsets.symmetric(horizontal: 16.w),
//             child: Row(
//               textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//               mainAxisAlignment: isExpanded
//                   ? isRTL ? MainAxisAlignment.end : MainAxisAlignment.start
//                   : MainAxisAlignment.center,
//               children: [
//                 Container(
//                   width: 40.w,
//                   height: 40.h,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(8.r),
//                     gradient: const LinearGradient(
//                       colors: AppColors.primaryGradient,
//                       begin: Alignment.topLeft,
//                       end: Alignment.bottomRight,
//                     ),
//                   ),
//                   child: Center(
//                     child: Text(
//                       'C',
//                       style: GoogleFonts.cairo(
//                         color: Colors.white,
//                         fontSize: 24.sp,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                   ),
//                 ),
//                 if (isExpanded) ...[
//                   SizedBox(width: 8.w),
//                   Text(
//                     'car_fleet'.tr,
//                     style: GoogleFonts.cairo(
//                       color: AppColors.primary,
//                       fontSize: 18.sp,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ],
//               ],
//             ),
//           ),
//           SizedBox(height: 40.h),
//
//           // Menu items
//           Expanded(
//             child: SingleChildScrollView(
//               child: Column(
//                 children: [
//                   _buildMenuItem(
//                     Icons.dashboard_outlined,
//                     'dashboard'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'dashboard',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('dashboard'),
//                   ),
//                   _buildMenuItem(
//                     Icons.directions_car_outlined,
//                     'cars'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'cars',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('cars'),
//                   ),
//                   _buildMenuItem(
//                     Icons.location_on_outlined,
//                     'sectors'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'sectors',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('sectors'),
//                   ),
//                   _buildMenuItem(
//                     Icons.people_outline,
//                     'users'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'users',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('users'),
//                   ),
//                   _buildMenuItem(
//                     Icons.build_outlined,
//                     'maintenance'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'maintenance',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('maintenance'),
//                   ),
//                   _buildMenuItem(
//                     Icons.inventory_2_outlined,
//                     'products'.tr,
//                     primaryText,
//                     secondaryText,
//                     controller.currentView.value == 'products',
//                     isExpanded,
//                     isRTL,
//                     () => controller.changeView('products'),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//
//           // Settings and logout at bottom
//           _buildMenuItem(
//             Icons.settings_outlined,
//             'settings'.tr,
//             primaryText,
//             secondaryText,
//             false,
//             isExpanded,
//             isRTL,
//             () => Get.toNamed(Routes.SETTINGS),
//           ),
//           _buildMenuItem(
//             Icons.logout_outlined,
//             'logout'.tr,
//             primaryText,
//             secondaryText,
//             false,
//             isExpanded,
//             isRTL,
//             () => Get.toNamed(Routes.LOGOUT),
//           ),
//         ],
//       ),
//     );
//   }
//
//   // Build bottom navigation bar for mobile
//   Widget _buildBottomNavBar(String currentView, bool isRTL) {
//     return BottomNavigationBar(
//       currentIndex: _getNavIndex(currentView),
//       onTap: (index) => controller.changeView(_getViewFromIndex(index)),
//       selectedItemColor: AppColors.primary,
//       unselectedItemColor: Colors.grey,
//       type: BottomNavigationBarType.fixed,
//       selectedLabelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
//       unselectedLabelStyle: GoogleFonts.cairo(),
//       items: [
//         BottomNavigationBarItem(
//           icon: const Icon(Icons.dashboard_outlined),
//           label: 'dashboard'.tr,
//         ),
//         BottomNavigationBarItem(
//           icon: const Icon(Icons.directions_car_outlined),
//           label: 'cars'.tr,
//         ),
//         BottomNavigationBarItem(
//           icon: const Icon(Icons.location_on_outlined),
//           label: 'sectors'.tr,
//         ),
//         BottomNavigationBarItem(
//           icon: const Icon(Icons.people_outline),
//           label: 'users'.tr,
//         ),
//         BottomNavigationBarItem(
//           icon: const Icon(Icons.more_horiz),
//           label: 'more'.tr,
//         ),
//       ],
//     );
//   }
//
//   // Helper method to get nav index from view name
//   int _getNavIndex(String view) {
//     switch (view) {
//       case 'dashboard': return 0;
//       case 'cars': return 1;
//       case 'sectors': return 2;
//       case 'users': return 3;
//       case 'maintenance':
//       case 'products': return 4;
//       default: return 0;
//     }
//   }
//
//   // Helper method to get view name from nav index
//   String _getViewFromIndex(int index) {
//     switch (index) {
//       case 0: return 'dashboard';
//       case 1: return 'cars';
//       case 2: return 'sectors';
//       case 3: return 'users';
//       case 4: return 'more';
//       default: return 'dashboard';
//     }
//   }
//
//   // Build menu item for sidebar
//   Widget _buildMenuItem(
//     IconData icon,
//     String title,
//     Color primaryText,
//     Color secondaryText,
//     bool isActive,
//     bool isExpanded,
//     bool isRTL,
//     VoidCallback onTap,
//   ) {
//     return InkWell(
//       onTap: onTap,
//       child: Container(
//         height: 56.h,
//         width: double.infinity,
//         padding: EdgeInsets.symmetric(horizontal: 16.w),
//         decoration: BoxDecoration(
//           color: isActive ? AppColors.primary.withOpacity(0.1) : Colors.transparent,
//           borderRadius: BorderRadius.circular(8.r),
//         ),
//         margin: EdgeInsets.only(bottom: 8.h, left: 16.w, right: 16.w),
//         child: Row(
//           textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//           children: [
//             Icon(
//               icon,
//               color: isActive ? AppColors.primary : secondaryText,
//               size: 24.sp,
//             ),
//             if (isExpanded) ...[
//               SizedBox(width: 16.w),
//               Text(
//                 title,
//                 style: GoogleFonts.cairo(
//                   color: isActive ? AppColors.primary : secondaryText,
//                   fontSize: 16.sp,
//                   fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
//                 ),
//               ),
//             ],
//           ],
//         ),
//       ),
//     );
//   }
//
//   // Build search bar
//   Widget _buildSearchBar(bool isRTL) {
//     return Container(
//       height: 56.h,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16.r),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       padding: EdgeInsets.symmetric(horizontal: 16.w),
//       child: Row(
//         textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//         children: [
//           Icon(
//             Icons.search,
//             color: Colors.grey,
//             size: 24.sp,
//           ),
//           SizedBox(width: 16.w),
//           Expanded(
//             child: TextField(
//               textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
//               textAlign: isRTL ? TextAlign.right : TextAlign.left,
//               onChanged: controller.setSearchQuery,
//               decoration: InputDecoration(
//                 hintText: 'search'.tr,
//                 hintStyle: GoogleFonts.cairo(
//                   color: Colors.grey,
//                   fontSize: 16.sp,
//                 ),
//                 border: InputBorder.none,
//               ),
//               style: GoogleFonts.cairo(
//                 color: Colors.black87,
//                 fontSize: 16.sp,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildProfileSection(
//     Color backgroundColor,
//     Color primaryText,
//     Color secondaryText,
//     double width,
//   ) {
//     return Container(
//       width: width,
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           // Profile image
//           CircleAvatar(
//             radius: 40.r,
//             backgroundImage: const NetworkImage(
//               'https://randomuser.me/api/portraits/women/44.jpg',
//             ),
//           ),
//           SizedBox(height: 16.h),
//
//           // Name
//           Text(
//             controller.userName.value,
//             style: TextStyle(
//               color: primaryText,
//               fontSize: 20.sp,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           SizedBox(height: 4.h),
//
//           // Edit health details
//           Text(
//             'Edit health details',
//             style: TextStyle(
//               color: secondaryText,
//               fontSize: 14.sp,
//             ),
//           ),
//           SizedBox(height: 16.h),
//
//           // Health metrics
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               _buildHealthMetric('Weight', controller.userWeight.value,
//                   primaryText, secondaryText),
//               _buildHealthMetric('Height', controller.userHeight.value,
//                   primaryText, secondaryText),
//               _buildHealthMetric('Blood Type', controller.userBloodType.value,
//                   primaryText, secondaryText),
//             ],
//           ),
//           SizedBox(height: 24.h),
//
//           // Scheduled activities
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 'Scheduled',
//                 style: TextStyle(
//                   color: primaryText,
//                   fontSize: 18.sp,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               SizedBox(height: 16.h),
//
//               // Activity list
//               ...controller.scheduledActivities
//                   .map(
//                     (activity) => _buildScheduledActivity(
//                       activity['title'],
//                       activity['day'],
//                       activity['time'],
//                       backgroundColor,
//                       primaryText,
//                       secondaryText,
//                     ),
//                   )
//                   .toList(),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildHealthMetric(
//       String label, String value, Color primaryText, Color secondaryText) {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
//       decoration: BoxDecoration(
//         color: Colors.white.withOpacity(0.1),
//         borderRadius: BorderRadius.circular(8.r),
//       ),
//       child: Column(
//         children: [
//           Text(
//             label,
//             style: TextStyle(
//               color: secondaryText,
//               fontSize: 12.sp,
//             ),
//           ),
//           SizedBox(height: 4.h),
//           Text(
//             value,
//             style: TextStyle(
//               color: primaryText,
//               fontSize: 16.sp,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildScheduledActivity(
//     String title,
//     String day,
//     String time,
//     Color backgroundColor,
//     Color primaryText,
//     Color secondaryText,
//   ) {
//     return Container(
//       margin: EdgeInsets.only(bottom: 12.h),
//       padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
//       decoration: BoxDecoration(
//         color: backgroundColor.withOpacity(0.5),
//         borderRadius: BorderRadius.circular(12.r),
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 title,
//                 style: TextStyle(
//                   color: primaryText,
//                   fontSize: 16.sp,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               SizedBox(height: 4.h),
//               Text(
//                 '$day, $time',
//                 style: TextStyle(
//                   color: secondaryText,
//                   fontSize: 14.sp,
//                 ),
//               ),
//             ],
//           ),
//           Icon(
//             Icons.more_horiz,
//             color: secondaryText,
//             size: 24.sp,
//           ),
//         ],
//       ),
//     );
//   }
//
//   // Desktop responsive grid layout for stats cards
//   Widget _buildDesktopStatsGrid(
//       Color backgroundColor, Color primaryText, Color secondaryText) {
//     return Obx(() => GridView.count(
//           crossAxisCount: 4,
//           shrinkWrap: true,
//           physics: const NeverScrollableScrollPhysics(),
//           mainAxisSpacing: 16.0,
//           crossAxisSpacing: 16.0,
//           children: [
//             _buildStatCard(
//               Icons.directions_car,
//               '${controller.totalCars.value}',
//               'إجمالي المركبات',
//               Colors.blue,
//               backgroundColor,
//               primaryText,
//               secondaryText,
//             ),
//             _buildStatCard(
//               Icons.check_circle,
//               '${controller.activeCars.value}',
//               'مركبات نشطة',
//               Colors.green,
//               backgroundColor,
//               primaryText,
//               secondaryText,
//             ),
//             _buildStatCard(
//               Icons.build,
//               '${controller.maintenanceCars.value}',
//               'قيد الصيانة',
//               Colors.orange,
//               backgroundColor,
//               primaryText,
//               secondaryText,
//             ),
//             _buildStatCard(
//               Icons.error,
//               '${controller.rejectedCars.value}',
//               'مرفوضة',
//               Colors.red,
//               backgroundColor,
//               primaryText,
//               secondaryText,
//             ),
//           ],
//         ));
//   }
//
//   // Mobile/tablet layout for stats cards
//   Widget _buildStatsCards(
//       Color backgroundColor, Color primaryText, Color secondaryText) {
//     return Obx(() => Row(
//           children: [
//             Expanded(
//               child: _buildStatCard(
//                 Icons.directions_car,
//                 '${controller.totalCars.value}',
//                 'إجمالي',
//                 Colors.blue,
//                 backgroundColor,
//                 primaryText,
//                 secondaryText,
//               ),
//             ),
//             SizedBox(width: 16.w),
//             Expanded(
//               child: _buildStatCard(
//                 Icons.check_circle,
//                 '${controller.activeCars.value}',
//                 'نشطة',
//                 Colors.green,
//                 backgroundColor,
//                 primaryText,
//                 secondaryText,
//               ),
//             ),
//             SizedBox(width: 16.w),
//             Expanded(
//               child: _buildStatCard(
//                 Icons.build,
//                 '${controller.maintenanceCars.value}',
//                 'صيانة',
//                 Colors.orange,
//                 backgroundColor,
//                 primaryText,
//                 secondaryText,
//               ),
//             ),
//           ],
//         ));
//   }
//
//   Widget _buildStatCard(
//     IconData icon,
//     String value,
//     String label,
//     Color iconColor,
//     Color backgroundColor,
//     Color primaryText,
//     Color secondaryText,
//   ) {
//     return Container(
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Container(
//             padding: EdgeInsets.all(12.r),
//             decoration: BoxDecoration(
//               color: iconColor.withOpacity(0.2),
//               shape: BoxShape.circle,
//             ),
//             child: Icon(
//               icon,
//               color: iconColor,
//               size: 24.sp,
//             ),
//           ),
//           SizedBox(height: 16.h),
//           Text(
//             value,
//             style: TextStyle(
//               color: primaryText,
//               fontSize: 24.sp,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           SizedBox(height: 8.h),
//           Text(
//             label,
//             style: TextStyle(
//               color: secondaryText,
//               fontSize: 14.sp,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildBarChart(
//     String title,
//     List<double> data,
//     Color barColor,
//     Color backgroundColor,
//     Color primaryText,
//     Color secondaryText,
//   ) {
//     return Container(
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             title,
//             style: TextStyle(
//               color: primaryText,
//               fontSize: 16.sp,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           SizedBox(height: 16.h),
//           SizedBox(
//             height: 150.h,
//             child: BarChart(
//               BarChartData(
//                 alignment: BarChartAlignment.spaceAround,
//                 maxY: 100,
//                 barTouchData: BarTouchData(enabled: false),
//                 titlesData: FlTitlesData(
//                   show: true,
//                   rightTitles: AxisTitles(
//                     sideTitles: SideTitles(showTitles: false),
//                   ),
//                   topTitles: AxisTitles(
//                     sideTitles: SideTitles(showTitles: false),
//                   ),
//                   bottomTitles: AxisTitles(
//                     sideTitles: SideTitles(
//                       showTitles: true,
//                       getTitlesWidget: (value, meta) {
//                         const style = TextStyle(
//                           color: Colors.white54,
//                           fontSize: 10,
//                         );
//                         String text;
//                         switch (value.toInt()) {
//                           case 0:
//                             text = 'M';
//                             break;
//                           case 1:
//                             text = 'T';
//                             break;
//                           case 2:
//                             text = 'W';
//                             break;
//                           case 3:
//                             text = 'T';
//                             break;
//                           case 4:
//                             text = 'F';
//                             break;
//                           case 5:
//                             text = 'S';
//                             break;
//                           case 6:
//                             text = 'S';
//                             break;
//                           default:
//                             return Container();
//                         }
//                         return Text(text, style: style);
//                       },
//                     ),
//                   ),
//                   leftTitles: AxisTitles(
//                     sideTitles: SideTitles(showTitles: false),
//                   ),
//                 ),
//                 borderData: FlBorderData(show: false),
//                 gridData: FlGridData(show: false),
//                 barGroups: List.generate(
//                   data.length,
//                   (index) => BarChartGroupData(
//                     x: index,
//                     barRods: [
//                       BarChartRodData(
//                         toY: data[index],
//                         color: barColor,
//                         width: 12.w,
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(6.r),
//                           topRight: Radius.circular(6.r),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildCarStatusChart(
//       Color backgroundColor, Color primaryText, Color secondaryText) {
//     return Container(
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'توزيع حالة المركبات',
//             style: TextStyle(
//               fontSize: 18.sp,
//               fontWeight: FontWeight.bold,
//               color: primaryText,
//             ),
//           ),
//           SizedBox(height: 8.h),
//           Text(
//             'نظرة عامة على حالة جميع المركبات',
//             style: TextStyle(
//               fontSize: 14.sp,
//               color: secondaryText,
//             ),
//           ),
//           SizedBox(height: 16.h),
//           SizedBox(
//             height: 200.h,
//             child: Obx(() {
//               if (controller.carStatusData.isEmpty) {
//                 return const Center(child: CircularProgressIndicator());
//               }
//
//               // Define colors for different statuses
//               final statusColors = {
//                 'active': Colors.green,
//                 'maintenance': Colors.orange,
//                 'rejected': Colors.red,
//                 'pending': Colors.blue,
//                 'inWorkshop': Colors.purple,
//                 'sentRequest': Colors.amber,
//                 'done': Colors.teal,
//               };
//
//               // Prepare data for pie chart
//               final pieChartSections = <PieChartSectionData>[];
//
//               for (var i = 0; i < controller.carStatusData.length; i++) {
//                 final item = controller.carStatusData[i];
//                 final status = item['status'] as String;
//                 final count = item['count'] as int;
//
//                 pieChartSections.add(
//                   PieChartSectionData(
//                     color: statusColors[status] ?? Colors.grey,
//                     value: count.toDouble(),
//                     title: '$count',
//                     radius: 80.r,
//                     titleStyle: TextStyle(
//                       fontSize: 14.sp,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.white,
//                     ),
//                   ),
//                 );
//               }
//
//               return Row(
//                 children: [
//                   // Pie chart
//                   Expanded(
//                     flex: 3,
//                     child: PieChart(
//                       PieChartData(
//                         sectionsSpace: 2,
//                         centerSpaceRadius: 40.r,
//                         sections: pieChartSections,
//                       ),
//                     ),
//                   ),
//
//                   // Legend
//                   Expanded(
//                     flex: 2,
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: controller.carStatusData.map((item) {
//                         final status = item['status'] as String;
//                         final count = item['count'] as int;
//                         final color = statusColors[status] ?? Colors.grey;
//
//                         return Padding(
//                           padding: EdgeInsets.symmetric(vertical: 4.h),
//                           child: Row(
//                             children: [
//                               Container(
//                                 width: 16.w,
//                                 height: 16.h,
//                                 decoration: BoxDecoration(
//                                   color: color,
//                                   shape: BoxShape.circle,
//                                 ),
//                               ),
//                               SizedBox(width: 8.w),
//                               Expanded(
//                                 child: Text(
//                                   _getArabicStatusName(status),
//                                   style: TextStyle(
//                                     fontSize: 12.sp,
//                                     color: primaryText,
//                                   ),
//                                 ),
//                               ),
//                               Text(
//                                 count.toString(),
//                                 style: TextStyle(
//                                   fontSize: 12.sp,
//                                   fontWeight: FontWeight.bold,
//                                   color: primaryText,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         );
//                       }).toList(),
//                     ),
//                   ),
//                 ],
//               );
//             }),
//           ),
//         ],
//       ),
//     );
//   }
//
//   // Helper method to convert status to Arabic
//   String _getArabicStatusName(String status) {
//     switch (status) {
//       case 'active':
//         return 'نشط';
//       case 'maintenance':
//         return 'صيانة';
//       case 'rejected':
//         return 'مرفوض';
//       case 'pending':
//         return 'قيد الانتظار';
//       case 'inWorkshop':
//         return 'في الورشة';
//       case 'sentRequest':
//         return 'تم إرسال الطلب';
//       case 'done':
//         return 'مكتمل';
//       default:
//         return status;
//     }
//   }
//
//   Widget _buildCarModelDistributionChart(
//       Color backgroundColor, Color primaryText, Color secondaryText) {
//     return Container(
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'توزيع موديلات المركبات',
//             style: TextStyle(
//               fontSize: 18.sp,
//               fontWeight: FontWeight.bold,
//               color: primaryText,
//             ),
//           ),
//           SizedBox(height: 8.h),
//           Text(
//             'توزيع المركبات حسب الموديل',
//             style: TextStyle(
//               fontSize: 14.sp,
//               color: secondaryText,
//             ),
//           ),
//           SizedBox(height: 16.h),
//           SizedBox(
//             height: 200.h,
//             child: Obx(() {
//               if (controller.carModelData.isEmpty) {
//                 return const Center(child: CircularProgressIndicator());
//               }
//
//               // Sort models by count for better visualization
//               final sortedModels = List.from(controller.carModelData);
//               sortedModels.sort(
//                   (a, b) => (b['count'] as int).compareTo(a['count'] as int));
//
//               // Take top 7 models for better visualization
//               final topModels = sortedModels.take(7).toList();
//
//               // Define colors for different models
//               final modelColors = [
//                 Colors.blue,
//                 Colors.green,
//                 Colors.orange,
//                 Colors.purple,
//                 Colors.teal,
//                 Colors.amber,
//                 Colors.pink,
//               ];
//
//               return BarChart(
//                 BarChartData(
//                   alignment: BarChartAlignment.spaceAround,
//                   maxY: (topModels.isNotEmpty
//                           ? (topModels[0]['count'] as int) * 1.2
//                           : 10)
//                       .toDouble(),
//                   barTouchData: BarTouchData(
//                     enabled: false,
//                   ),
//                   titlesData: FlTitlesData(
//                     show: true,
//                     bottomTitles: AxisTitles(
//                       sideTitles: SideTitles(
//                         showTitles: true,
//                         getTitlesWidget: (value, meta) {
//                           final index = value.toInt();
//                           if (index >= 0 && index < topModels.length) {
//                             return Padding(
//                               padding: const EdgeInsets.only(top: 8.0),
//                               child: Text(
//                                 topModels[index]['model'] as String,
//                                 style: TextStyle(
//                                   color: secondaryText,
//                                   fontSize: 12.sp,
//                                 ),
//                               ),
//                             );
//                           }
//                           return const Text('');
//                         },
//                       ),
//                     ),
//                     leftTitles: AxisTitles(
//                       sideTitles: SideTitles(
//                         showTitles: true,
//                         reservedSize: 30,
//                         getTitlesWidget: (value, meta) {
//                           if (value % 5 == 0) {
//                             return Text(
//                               value.toInt().toString(),
//                               style: TextStyle(
//                                 color: secondaryText,
//                                 fontSize: 12.sp,
//                               ),
//                             );
//                           }
//                           return const Text('');
//                         },
//                       ),
//                     ),
//                     topTitles: AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                     rightTitles: AxisTitles(
//                       sideTitles: SideTitles(showTitles: false),
//                     ),
//                   ),
//                   gridData: FlGridData(
//                     show: true,
//                     drawHorizontalLine: true,
//                     horizontalInterval: 5,
//                     getDrawingHorizontalLine: (value) {
//                       return FlLine(
//                         color: Colors.grey.shade300,
//                         strokeWidth: 1,
//                       );
//                     },
//                     drawVerticalLine: false,
//                   ),
//                   borderData: FlBorderData(
//                     show: false,
//                   ),
//                   barGroups: List.generate(topModels.length, (index) {
//                     return BarChartGroupData(
//                       x: index,
//                       barRods: [
//                         BarChartRodData(
//                           toY: (topModels[index]['count'] as int).toDouble(),
//                           color: modelColors[index % modelColors.length],
//                           width: 20,
//                           borderRadius: BorderRadius.circular(4),
//                           backDrawRodData: BackgroundBarChartRodData(
//                             show: true,
//                             toY: (topModels.isNotEmpty
//                                     ? (topModels[0]['count'] as int) * 1.2
//                                     : 10)
//                                 .toDouble(),
//                             color: Colors.grey.shade200,
//                           ),
//                         ),
//                       ],
//                     );
//                   }),
//                 ),
//               );
//             }),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildRecentMaintenanceHistory(
//       Color backgroundColor, Color primaryText, Color secondaryText) {
//     return Container(
//       padding: EdgeInsets.all(16.r),
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(16.r),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'آخر طلبات الصيانة',
//             style: TextStyle(
//               fontSize: 18.sp,
//               fontWeight: FontWeight.bold,
//               color: primaryText,
//             ),
//           ),
//           SizedBox(height: 8.h),
//           Text(
//             'أحدث 5 مركبات تم إرسالها للصيانة',
//             style: TextStyle(
//               fontSize: 14.sp,
//               color: secondaryText,
//             ),
//           ),
//           SizedBox(height: 16.h),
//           Obx(() {
//             if (controller.recentMaintenanceHistory.isEmpty) {
//               return const Center(
//                 child: Padding(
//                   padding: EdgeInsets.all(20.0),
//                   child: CircularProgressIndicator(),
//                 ),
//               );
//             }
//
//             return ListView.builder(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               itemCount: controller.recentMaintenanceHistory.length,
//               itemBuilder: (context, index) {
//                 final item = controller.recentMaintenanceHistory[index];
//                 final plateNumber = item['plateNumber'] as String;
//                 final model = item['model'] as String;
//                 final status = item['status'] as String;
//                 final date = item['date'] as DateTime;
//
//                 // Define color based on status
//                 Color statusColor;
//                 switch (status) {
//                   case 'active':
//                     statusColor = Colors.green;
//                     break;
//                   case 'maintenance':
//                     statusColor = Colors.orange;
//                     break;
//                   case 'rejected':
//                     statusColor = Colors.red;
//                     break;
//                   default:
//                     statusColor = Colors.blue;
//                 }
//
//                 return Card(
//                   elevation: 0,
//                   color: Colors.grey.shade100,
//                   margin: EdgeInsets.only(bottom: 8.h),
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(8.r),
//                   ),
//                   child: ListTile(
//                     contentPadding:
//                         EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
//                     leading: CircleAvatar(
//                       backgroundColor: Colors.blue.shade100,
//                       child: Icon(Icons.directions_car, color: Colors.blue),
//                     ),
//                     title: Text(
//                       plateNumber,
//                       style: TextStyle(
//                         fontSize: 16.sp,
//                         fontWeight: FontWeight.bold,
//                         color: primaryText,
//                       ),
//                     ),
//                     subtitle: Text(
//                       model,
//                       style: TextStyle(
//                         fontSize: 14.sp,
//                         color: secondaryText,
//                       ),
//                     ),
//                     trailing: Column(
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Container(
//                           padding: EdgeInsets.symmetric(
//                               horizontal: 8.w, vertical: 4.h),
//                           decoration: BoxDecoration(
//                             color: statusColor.withOpacity(0.2),
//                             borderRadius: BorderRadius.circular(4.r),
//                           ),
//                           child: Text(
//                             _getArabicStatusName(status),
//                             style: TextStyle(
//                               fontSize: 12.sp,
//                               color: statusColor,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: 4.h),
//                         Text(
//                           '${date.day}/${date.month}/${date.year}',
//                           style: TextStyle(
//                             fontSize: 12.sp,
//                             color: secondaryText,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 );
//               },
//             );
//           }),
//         ],
//       ),
//     );
//   }
// }
