import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/routes/app_pages.dart';
import 'package:cars_app/app/widgets/car_rotation_dashboard.dart';
import 'package:cars_app/app/widgets/notification_icon_widget.dart';
import 'package:cars_app/app/widgets/supervistor_car_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../controllers/auth_controller.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/car_rotation_un_active_card.dart';
import '../../../widgets/process_time_line_car_status.dart';
import '../../../widgets/shared_app_bar.dart';

import '../controllers/supervisor_controller.dart';

class SupervisorView extends GetView<SupervisorController> {
  const SupervisorView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return DefaultTabController(
      length: 3,
      initialIndex: 0,
      child: Scaffold(
        backgroundColor: AppColors.background,
        floatingActionButton: FloatingActionButton.extended(
          icon: Icon(Icons.rotate_90_degrees_ccw,color: Colors.white,),
          label: Text('تدوير المركبات'.tr,style: TextStyle(color: Colors.white),),
          backgroundColor: AppColors.primary,
          onPressed: () {
            Get.toNamed(Routes.CarRotationDashboard,arguments: '');
        },),
        appBar: SharedAppBar(
          title: ('مشرف تشغيل وصيانة الأسطول'),
          showBackButton: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.person,color: Colors.white,),
              onPressed: () => Get.toNamed(Routes.PROFILE),
            ),
            NotificationIconWidget()
          ],
        ),

        body: Column(
          children: [
            TabBar(
              indicatorColor: AppColors.primary,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.grey,
              tabs: [
                Tab(text: 'الصيانة'),
                Tab(text: 'دعم المركبات'),
                Tab(text: 'الأعاده'),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  StreamBuilder<QuerySnapshot>(
                    stream: FirebaseFirestore.instance
                        .collection('cars')
                        .where('status', whereIn: ['sentRequest', 'pending','callToWorkshop','sendGroup','receipt','done','agreeDeliveryToWorkShop'])
                        .snapshots(),
                    builder: (context, snapshot) {
                      if (snapshot.hasError) {
                        return Center(child: Text('حدث خطأ: ${snapshot.error}'));
                      }

                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      final cars = snapshot.data?.docs ?? [];

                      if (cars.isEmpty) {
                        return const Center(child: Text('لا توجد طلبات حالياً'));
                      }

                      return ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: cars.length,
                        itemBuilder: (context, index) {
                          final carData = cars[index].data() as Map<String, dynamic>;
                          final car = Car.fromJson({...carData, 'id': snapshot.data?.docs[index].id});
                          final latestWorkshopEntry = car.workshopHistory?.isNotEmpty == true
                              ? car.workshopHistory!.last
                              : null;


                          return SupervistorCarCard(car: car, currentAction: controller.getCurrentActions(car.id, car), uid: controller.uid.value,);
                        },
                      );
                    },
                  ),

                  StreamBuilder<List<CarRotate>>(
                      stream: controller.rotationService.getUnActiveCarsStreamByAction(RotateAction.support),
                      builder: (context, snapshot) {
                        List<CarRotate> carRotations = snapshot.data ?? [];
                        if(carRotations.isEmpty){
                          return const Center(child: Text('لا توجد طلبات حالياً'));
                        }else{
                          return ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: carRotations.length,
                            itemBuilder: (context, index) {
                              return CarRotationUnActiveCard(
                                rotation: carRotations[index], onStatusUpdate: (CarRotate , agree ) {
                                  controller.activeRotateCar(CarRotate, agree);
                              },

                              );
                            },
                          );
                        }
                      },),
                  StreamBuilder<List<CarRotate>>(
                      stream: controller.rotationService.getUnActiveCarsStreamByAction(RotateAction.restore),
                      builder: (context, snapshot) {
                        List<CarRotate> carRotations = snapshot.data ?? [];
                        if(carRotations.isEmpty){
                          return const Center(child: Text('لا توجد طلبات حالياً'));
                        }else{
                          return ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: carRotations.length,
                            itemBuilder: (context, index) {
                              return CarRotationUnActiveCard(
                                rotation: carRotations[index], onStatusUpdate: (CarRotate , agree ) {
                                  controller.activeRotateCar(CarRotate, agree);
                              },

                              );
                            },
                          );
                        }
                      },),

                ],
              ),
            ),
          ],
        ),
      ),
    );
  }


}
