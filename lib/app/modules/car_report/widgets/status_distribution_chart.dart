import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';
import '../../../modules/reports/controllers/reports_controller.dart'
    as reports;

class StatusDistribution<PERSON>hart extends StatelessWidget {
  final List<reports.StatusCount> statusDistribution;

  const StatusDistributionChart({
    Key? key,
    required this.statusDistribution,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  sections: _getSections(),
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {
                      // Handle touch events if needed
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              alignment: WrapAlignment.center,
              children: statusDistribution
                  .map((status) => _buildIndicator(
                        status.status.toString().split('.').last.tr,
                        _getStatusColor(status.status),
                        status.count,
                      ))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _getSections() {
    final total =
        statusDistribution.fold<int>(0, (sum, status) => sum + status.count);

    return statusDistribution.asMap().entries.map((entry) {
      final status = entry.value;
      final percent = status.count / total * 100;
      final color = _getStatusColor(status.status);

      return PieChartSectionData(
        color: color,
        value: status.count.toDouble(),
        title: '${percent.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildIndicator(String label, Color color, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ($count)',
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return AppColors.success;
      case CarStatus.maintenance:
      case CarStatus.inWorkshop:
        return AppColors.warning;
      case CarStatus.rejected:
      case CarStatus.destroyed:
      case CarStatus.outOfService:
        return AppColors.error;
      case CarStatus.pending:
        return AppColors.info;
      case CarStatus.done:
        return AppColors.done;
      case CarStatus.sendToLogisticsSupport:
        return AppColors.logistics;
      case CarStatus.deliveryToSector:
        return AppColors.delivery;
      case CarStatus.callToWorkshop:
        return AppColors.workshop;
      case CarStatus.agreeDeliveryToWorkShop:
        return AppColors.approved;
      default:
        return AppColors.accent;
    }
  }
}
