import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../data/models/sector_model.dart';

class SectorService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<List<Sector>> getSectors() async {
    try {
      final snapshot = await _firestore.collection('sectors').get();
      return snapshot.docs
          .map((doc) => Sector.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to load sectors');
    }
  }

  Future<Sector?> getSectorById(String sectorId) async {
    try {
      final doc = await _firestore.collection('sectors').doc(sectorId).get();
      if (doc.exists) {
        return Sector.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Failed to load sector');
    }
  }
} 