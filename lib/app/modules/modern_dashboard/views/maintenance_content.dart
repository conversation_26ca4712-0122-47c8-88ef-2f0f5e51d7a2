// import 'package:cars_app/app/modules/modern_dashboard/widgets/filter_bar.dart';
// import 'package:cars_app/app/modules/modern_dashboard/widgets/filter_bar.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:shimmer/shimmer.dart';
//
// import '../../../data/models/car_model.dart';
// import '../../../themes/app_colors.dart';
// import '../controllers/modern_dashboard_controller.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/filter_bar.dart';
// import '../widgets/section_header.dart';
// import '../widgets/maintenance_card.dart';
//
// class MaintenanceContent extends StatelessWidget {
//   final ModernDashboardController controller;
//   final bool isDesktop;
//   final bool isTablet;
//   final bool isMobile;
//   final bool isRTL;
//
//   const MaintenanceContent({
//     Key? key,
//     required this.controller,
//     required this.isDesktop,
//     required this.isTablet,
//     required this.isMobile,
//     required this.isRTL,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
//       children: [
//         // Maintenance section header
//         SectionHeader(
//           title: 'maintenance'.tr,
//           subtitle: 'maintenance_subtitle'.tr,
//           icon: Icons.build_outlined,
//           isRTL: isRTL,
//         ),
//         const SizedBox(height: 16),
//
//         // Status filter
//         Obx(() => FilterBar<CarStatus>(
//           title: 'filter_by_status'.tr,
//           options: [
//             ModernDashboardController.FilterOption(
//               label: 'completed'.tr,
//               value: CarStatus.active,
//               icon: Icons.check_circle_outline,
//             ),
//             ModernDashboardController.FilterOption(
//               label: 'in_progress'.tr,
//               value: CarStatus.maintenance,
//               icon: Icons.pending_outlined,
//             ),
//           ],
//           selectedValue: controller.carStatusFilter.value,
//           onChanged: controller.setCarStatusFilter,
//           isRTL: isRTL,
//         )),
//         const SizedBox(height: 16),
//
//         // Maintenance list with loading state
//         Obx(() {
//           if (controller.isMaintenanceLoading.value) {
//             return _buildLoadingList();
//           }
//
//           if (controller.filteredMaintenances.isEmpty) {
//             return _buildEmptyState();
//           }
//
//           return ListView.builder(
//             shrinkWrap: true,
//             physics: const NeverScrollableScrollPhysics(),
//             itemCount: controller.filteredMaintenances.length,
//             itemBuilder: (context, index) {
//               final item = controller.filteredMaintenances[index];
//               return Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: MaintenanceCard(
//                   car: item.car,
//                   maintenance: item.maintenance,
//                   isRTL: isRTL,
//                   onTap: () => Get.toNamed('/maintenance/${item.id}'),
//                 ),
//               );
//             },
//           );
//         }),
//       ],
//     );
//   }
//
//   // Build loading state
//   Widget _buildLoadingList() {
//     return ListView.builder(
//       shrinkWrap: true,
//       physics: const NeverScrollableScrollPhysics(),
//       itemCount: 3,
//       itemBuilder: (context, index) {
//         return Padding(
//           padding: const EdgeInsets.only(bottom: 8),
//           child: Container(
//             height: 150,
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withOpacity(0.05),
//                   blurRadius: 10,
//                   offset: const Offset(0, 4),
//                 ),
//               ],
//             ),
//             child: Shimmer.fromColors(
//               baseColor: Colors.grey[300]!,
//               highlightColor: Colors.grey[100]!,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     children: [
//                       Container(
//                         width: 48,
//                         height: 48,
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(10),
//                         ),
//                       ),
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Container(
//                               width: 120,
//                               height: 20,
//                               color: Colors.white,
//                             ),
//                             const SizedBox(height: 8),
//                             Container(
//                               width: 180,
//                               height: 16,
//                               color: Colors.white,
//                             ),
//                           ],
//                         ),
//                       ),
//                       Container(
//                         width: 80,
//                         height: 24,
//                         decoration: BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),
//                   Container(
//                     width: double.infinity,
//                     height: 1,
//                     color: Colors.white,
//                   ),
//                   const SizedBox(height: 16),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                       Container(
//                         width: 80,
//                         height: 16,
//                         color: Colors.white,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   // Build empty state
//   Widget _buildEmptyState() {
//     return Container(
//       height: 200,
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             offset: const Offset(0, 4),
//           ),
//         ],
//       ),
//       child: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               Icons.build_circle_outlined,
//               size: 48,
//               color: Colors.grey[400],
//             ),
//             const SizedBox(height: 16),
//             Text(
//               'no_maintenance_found'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'no_maintenance_found_description'.tr,
//               style: GoogleFonts.cairo(
//                 fontSize: 14,
//                 color: Colors.grey[600],
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
