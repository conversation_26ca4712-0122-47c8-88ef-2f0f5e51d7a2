import 'dart:async';

import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../data/models/sector_model.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/user_model.dart';
import '../../../services/user_storage_service.dart';

class DashboardController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LocalStorageService _storageService = LocalStorageService();

  final RxMap<String, int> carStats = <String, int>{}.obs;
  final RxList<Sector> sectors = <Sector>[].obs;
  final RxMap<String, int> userStats = <String, int>{}.obs;
  final RxList<Map<String, dynamic>> recentActivities =
      <Map<String, dynamic>>[].obs;

  final RxString selectedSector = ''.obs;
  final RxString selectedStatus = ''.obs;
  final RxString selectedDateRange = 'all'.obs;
  final RxBool isLoading = false.obs;

  // User information
  final RxString userEmail = ''.obs;
  final RxString userName = ''.obs;
  final RxString userRole = ''.obs;

  // For pie chart data
  final RxList<double> carStatusData = <double>[].obs;
  final RxList<String> carStatusLabels = <String>[].obs;
  final RxList<double> sectorCarData = <double>[0, 0, 0].obs;
  final RxList<String> sectorLabels = <String>[
    'Sector 1',
    'Sector 2',
    'Sector 3',
  ].obs;

  // Stream subscriptions
  StreamSubscription<QuerySnapshot>? _carsSubscription;
  StreamSubscription<QuerySnapshot>? _activitiesSubscription;

  @override
  void onInit() {
    super.onInit();
    setupStreams();
    loadInitialData();
    loadUserInfo();
  }

  Future<void> loadUserInfo() async {
    try {
      final userId = await _storageService.getUserId();
      if (userId != null && userId.isNotEmpty) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          final userData = userDoc.data();
          if (userData != null) {
            userEmail.value = userData['email'] ?? '';
            userName.value = userData['username'] ?? '';
            userRole.value = userData['role'] ?? '';
          }
        }
      }
    } catch (e) {
      print('Error loading user info: $e');
    }
  }

  @override
  void onClose() {
    _carsSubscription?.cancel();
    _activitiesSubscription?.cancel();
    super.onClose();
  }

  void setupStreams() {
    // Listen to cars collection changes
    _carsSubscription =
        _firestore.collection('cars').snapshots().listen((snapshot) {
      loadCarStats();
      updateChartData();
    });

    // Listen to recent activities
    _activitiesSubscription = _firestore
        .collection('activities')
        .orderBy('timestamp', descending: true)
        .limit(10)
        .snapshots()
        .listen((snapshot) {
      recentActivities.value = snapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
    });
  }

  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadSectors(),
        loadUserStats(),
      ]);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadAllStats() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadCarStats(),
        loadSectors(),
        loadUserStats(),
      ]);
      updateChartData();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadSectors() async {
    try {
      QuerySnapshot sectorsSnapshot =
          await _firestore.collection('sectors').get();
      sectors.value = [
        Sector(
            id: '',
            name: 'all'.tr,
            managerId: '',
            createAt: DateTime.now(),
            managerHistory: []),
        ...sectorsSnapshot.docs.map((doc) => Sector.fromJson({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }))
      ];
    } catch (e) {
      print('Error loading sectors: $e');
    }
  }

  Future<void> loadCarStats() async {
    try {
      Query carsQuery = _firestore.collection('cars');

      if (selectedSector.value.isNotEmpty && selectedSector.value != 'all') {
        carsQuery =
            carsQuery.where('sectorId', isEqualTo: selectedSector.value);
      }
      if (selectedStatus.value.isNotEmpty && selectedStatus.value != 'all') {
        carsQuery = carsQuery.where('status', isEqualTo: selectedStatus.value);
      }

      QuerySnapshot carsSnapshot = await carsQuery.get();

      Map<String, int> statusCount = {
        'active': 0,
        'sendRequest': 0,
        'workshop': 0,
        'destroyed': 0,
        'sentRequest': 0,
        'pending': 0,
      };

      for (var doc in carsSnapshot.docs) {
        final car = Car.fromJson(doc.data() as Map<String, dynamic>);
        String status = car.status.toString().split('.').last;
        statusCount[status] = (statusCount[status] ?? 0) + 1;
      }

      carStats.value = {
        'total': carsSnapshot.size,
        ...statusCount,
      };
    } catch (e) {
      print('Error loading car stats: $e');
    }
  }

  Future<void> loadUserStats() async {
    try {
      QuerySnapshot usersSnapshot = await _firestore.collection('users').get();

      Map<String, int> roleCount = {
        'admin': 0,
        'supervisor': 0,
        'technician': 0,
        'user': 0,
      };

      for (var doc in usersSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        String role = data['role']?.toString().toLowerCase() ?? 'user';
        roleCount[role] = (roleCount[role] ?? 0) + 1;
      }

      userStats.value = {
        'total': usersSnapshot.size,
        ...roleCount,
      };
    } catch (e) {
      print('Error loading user stats: $e');
    }
  }

  void updateChartData() {
    carStatusData.clear();
    carStatusLabels.clear();
    carStats.forEach((key, value) {
      if (key != 'total') {
        carStatusData.add(value.toDouble());
        carStatusLabels.add(key.tr);
      }
    });

    sectorCarData.clear();
    sectorLabels.clear();
    for (var sector in sectors) {
      if (sector.id.isEmpty) continue;
      sectorCarData.add((carStats[sector.id] ?? 0).toDouble());
      sectorLabels.add(sector.name);
    }
  }

  Future<void> applyFilters(
      {String? sector, String? status, String? dateRange}) async {
    if (sector != null) selectedSector.value = sector;
    if (status != null) selectedStatus.value = status;
    if (dateRange != null) selectedDateRange.value = dateRange;
    await loadAllStats();
  }

  void resetFilters() {
    selectedSector.value = '';
    selectedStatus.value = '';
    selectedDateRange.value = 'all';
    loadAllStats();
  }

  Future<List<Car>> getCarsByStatus(String status) async {
    try {
      print('Fetching cars with status: $status');
      Query carsQuery = _firestore.collection('cars');
      
      // Apply sector filter if selected
      if (selectedSector.value.isNotEmpty && selectedSector.value != 'all') {
        carsQuery = carsQuery.where('sectorId', isEqualTo: selectedSector.value);
      }
      
      // Filter by status only if a specific status is provided
      if (status.isNotEmpty) {
        carsQuery = carsQuery.where('status', isEqualTo: status);
      }
      
      print('Executing Firestore query for cars');
      QuerySnapshot carsSnapshot = await carsQuery.get();
      print('Found ${carsSnapshot.docs.length} cars in Firestore');
      
      List<Car> carsList = [];
      for (var doc in carsSnapshot.docs) {
        try {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          carsList.add(Car.fromJson(data));
        } catch (e) {
          print('Error parsing car document ${doc.id}: $e');
        }
      }
      
      print('Successfully parsed ${carsList.length} cars');
      return carsList;
    } catch (e) {
      print('Error fetching cars by status: $e');
      return [];
    }
  }
}
