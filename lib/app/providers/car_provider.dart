import 'package:cloud_firestore/cloud_firestore.dart';

import '../services/firestore_service.dart';

class CarProvider {
  final FirestoreService _firestoreService;

  CarProvider({FirestoreService? firestoreService})
      : _firestoreService = firestoreService ?? FirestoreService();

  /// Create a new car document in the 'cars' collection
  Future<void> createCar(Map<String, dynamic> data) async {
    await _firestoreService.create('cars', data);
  }

  /// Read all car documents from the 'cars' collection
  Future<List<Map<String, dynamic>>> readCars() async {
    QuerySnapshot querySnapshot = (await _firestoreService.readAll('cars')) as QuerySnapshot<Object?>;
    return querySnapshot.docs.map((doc) => doc.data() as Map<String, dynamic>).toList();
  }

  /// Update a car document identified by [documentId] in the 'cars' collection
  Future<void> updateCar(String documentId, Map<String, dynamic> data) async {
    await _firestoreService.update('cars', documentId, data);
  }

  /// Delete a car document identified by [documentId] from the 'cars' collection
  Future<void> deleteCar(String documentId) async {
    await _firestoreService.delete('cars', documentId);
  }
}
