import 'package:cars_app/app/modules/car/view/car_history_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/car_model.dart';
import '../themes/app_colors.dart';

class ProcessTimelineCarStatus extends StatelessWidget {
  final Car car;

  const ProcessTimelineCarStatus({
    Key? key,
    required this.car,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<_TimelineStatus> timelineStatuses = [
      _TimelineStatus(
        status: CarStatus.sentRequest,
        label: 'sent_request'.tr,
        icon: Icons.send,
      ),
      _TimelineStatus(
        status: CarStatus.receipt,
        label: 'receipt'.tr,
        icon: Icons.receipt_long,
      ),
      _TimelineStatus(
        status: CarStatus.callToWorkshop,
        label: 'call_workshop'.tr,
        icon: Icons.phone,
      ),
      _TimelineStatus(
        status: CarStatus.pending,
        label: 'pending'.tr,
        icon: Icons.pending_actions,
      ),
      _TimelineStatus(
        status: CarStatus.done,
        label: 'done'.tr,
        icon: Icons.check_circle,
      ),
    ];

    // Find the current status index
    // final currentStatusIndex = timelineStatuses.indexWhere(
    //   (s) => statuses.any((status) => status.status == s.status),
    // );

    return Card(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Row(
            //   children: List.generate(timelineStatuses.length * 2 - 1, (index) {
            //     // If even index, show status circle
            //     if (index % 2 == 0) {
            //       final statusIndex = index ~/ 2;
            //       final status = timelineStatuses[statusIndex];
            //       final isCompleted = statusIndex <= currentStatusIndex;
            //       final isCurrent = statusIndex == currentStatusIndex;
            //
            //       return Expanded(
            //         child: Column(
            //           children: [
            //             Container(
            //               width: 40,
            //               height: 40,
            //               decoration: BoxDecoration(
            //                 color: isCompleted ? AppColors.success : Colors.grey[300],
            //                 shape: BoxShape.circle,
            //               ),
            //               child: Icon(
            //                 status.icon,
            //                 color: isCompleted ? Colors.white : Colors.grey[600],
            //                 size: 20,
            //               ),
            //             ),
            //             const SizedBox(height: 8),
            //             Text(
            //               status.label,
            //               style: TextStyle(
            //                 color: isCurrent ? AppColors.primary : AppColors.textSecondary,
            //                 fontSize: 12,
            //                 fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
            //               ),
            //               textAlign: TextAlign.center,
            //             ),
            //           ],
            //         ),
            //       );
            //     }
            //
            //     // If odd index, show connecting line
            //     final lineIndex = index ~/ 2;
            //     final isCompleted = lineIndex < currentStatusIndex;
            //
            //     return Expanded(
            //       child: Container(
            //         height: 2,
            //         color: isCompleted ? AppColors.success : Colors.grey[300],
            //       ),
            //     );
            //   }),
            // ),
            if (car.workshopHistory.last.statuses.isNotEmpty) ...[
              // const SizedBox(height: 24),
              Row(
                children: [
                  IconButton(onPressed: () {
                    Get.to(CarHistoryView(car: car));
                                }, icon:Icon( Icons.history)),
                  _buildStatusDetails(),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDetails() {
    final latestStatus = car.workshopHistory.last.statuses.last;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getStatusIcon(latestStatus.status),
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _getStatusLabel(latestStatus.status),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          if (latestStatus.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Text(
              latestStatus.notes!,
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            '${'by'.tr}: ${latestStatus.senderName}',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusLabel(CarStatus status) {
    switch (status) {
      case CarStatus.sentRequest:
        return 'sent_request'.tr;
      case CarStatus.receipt:
        return 'receipt'.tr;
      case CarStatus.callToWorkshop:
        return 'call_workshop'.tr;
      case CarStatus.pending:
        return 'عند الفني'.tr;
      case CarStatus.done:
        return 'done'.tr;
      default:
        return status.toString().split('.').last.tr;
    }
  }

  IconData _getStatusIcon(CarStatus status) {
    switch (status) {
      case CarStatus.sentRequest:
        return Icons.send;
      case CarStatus.receipt:
        return Icons.receipt_long;
      case CarStatus.callToWorkshop:
        return Icons.phone;
      case CarStatus.pending:
        return Icons.pending_actions;
      case CarStatus.done:
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }
}

class _TimelineStatus {
  final CarStatus status;
  final String label;
  final IconData icon;

  const _TimelineStatus({
    required this.status,
    required this.label,
    required this.icon,
  });
}
