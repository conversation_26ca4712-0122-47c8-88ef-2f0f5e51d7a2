import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/theme_controller.dart';
import '../data/language_controller.dart';
import '../themes/app_colors.dart';
import 'custom_card.dart';
import 'custom_switch.dart';

class ThemeSettingsWidget extends StatelessWidget {
  final bool showLanguageSwitch;
  final bool showThemePreview;

  const ThemeSettingsWidget({
    Key? key,
    this.showLanguageSwitch = true,
    this.showThemePreview = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (themeController) => GetBuilder<LanguageController>(
        builder: (languageController) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Mode Selection
            _buildThemeModeSection(themeController),
            
            SizedBox(height: 16.h),
            
            // Language Selection
            if (showLanguageSwitch) ...[
              _buildLanguageSection(languageController),
              SizedBox(height: 16.h),
            ],
            
            // Theme Preview
            if (showThemePreview) ...[
              _buildThemePreview(),
              SizedBox(height: 16.h),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeSection(ThemeController themeController) {
    return CustomCard(
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'appearance'.tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.getTextPrimary(Get.isDarkMode),
            ),
          ),
          SizedBox(height: 16.h),
          
          // Theme Mode Options
          _buildThemeOption(
            title: 'light_theme'.tr,
            subtitle: 'light_theme_desc'.tr,
            icon: Icons.light_mode,
            isSelected: themeController.themeMode == ThemeMode.light,
            onTap: () => themeController.setLightTheme(),
          ),
          
          SizedBox(height: 12.h),
          
          _buildThemeOption(
            title: 'dark_theme'.tr,
            subtitle: 'dark_theme_desc'.tr,
            icon: Icons.dark_mode,
            isSelected: themeController.themeMode == ThemeMode.dark,
            onTap: () => themeController.setDarkTheme(),
          ),
          
          SizedBox(height: 12.h),
          
          _buildThemeOption(
            title: 'system_theme'.tr,
            subtitle: 'system_theme_desc'.tr,
            icon: Icons.brightness_auto,
            isSelected: themeController.themeMode == ThemeMode.system,
            onTap: () => themeController.setSystemTheme(),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? AppColors.primary 
                : AppColors.getBorder(Get.isDarkMode),
            width: isSelected ? 2 : 1,
          ),
          color: isSelected 
              ? AppColors.primary.withOpacity(0.1) 
              : Colors.transparent,
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppColors.primary 
                    : AppColors.getSurface(Get.isDarkMode),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: isSelected 
                    ? Colors.white 
                    : AppColors.getTextSecondary(Get.isDarkMode),
                size: 20.r,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.getTextPrimary(Get.isDarkMode),
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.getTextSecondary(Get.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20.r,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSection(LanguageController languageController) {
    return CustomCard(
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'language'.tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.getTextPrimary(Get.isDarkMode),
            ),
          ),
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: _buildLanguageOption(
                  title: 'English',
                  subtitle: 'English',
                  flag: '🇺🇸',
                  isSelected: !languageController.isArabic,
                  onTap: () => languageController.setLanguage('en'),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildLanguageOption(
                  title: 'العربية',
                  subtitle: 'Arabic',
                  flag: '🇸🇦',
                  isSelected: languageController.isArabic,
                  onTap: () => languageController.setLanguage('ar'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption({
    required String title,
    required String subtitle,
    required String flag,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? AppColors.primary 
                : AppColors.getBorder(Get.isDarkMode),
            width: isSelected ? 2 : 1,
          ),
          color: isSelected 
              ? AppColors.primary.withOpacity(0.1) 
              : Colors.transparent,
        ),
        child: Column(
          children: [
            Text(
              flag,
              style: TextStyle(fontSize: 24.sp),
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.getTextSecondary(Get.isDarkMode),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemePreview() {
    return CustomCard(
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'preview'.tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.getTextPrimary(Get.isDarkMode),
            ),
          ),
          SizedBox(height: 16.h),
          
          // Preview Cards
          Row(
            children: [
              Expanded(
                child: _buildPreviewCard(
                  title: 'Primary',
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildPreviewCard(
                  title: 'Secondary',
                  color: AppColors.secondary,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildPreviewCard(
                  title: 'Success',
                  color: AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewCard({
    required String title,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Container(
            width: 24.w,
            height: 24.h,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
