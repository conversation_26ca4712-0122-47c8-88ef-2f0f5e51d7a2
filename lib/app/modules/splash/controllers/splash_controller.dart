import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import '../../../controllers/auth_controller.dart';
import '../../../routes/app_pages.dart';

class SplashController extends GetxController with GetTickerProviderStateMixin {
  final _auth = Get.find<AuthController>();
  
  late AnimationController mainLogoController;
  late Animation<double> mainLogoAnimation;
  
  late AnimationController poweredByLogoController;
  late Animation<double> poweredByLogoAnimation;
  
  late AnimationController textsController;
  late Animation<double> textsAnimation;
  
  @override
  void onInit() {
    super.onInit();
    
    // Main Logo Animation
    mainLogoController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    mainLogoAnimation = CurvedAnimation(
      parent: mainLogoController,
      curve: Curves.easeOut,
    );
    
    // Powered By Logo Animation
    poweredByLogoController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    poweredByLogoAnimation = CurvedAnimation(
      parent: poweredByLogoController,
      curve: Curves.easeOut,
    );
    
    // Texts Animation
    textsController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    textsAnimation = CurvedAnimation(
      parent: textsController,
      curve: Curves.easeOut,
    );
    
    _startAnimationSequence();
  }
  
  void _startAnimationSequence() async {
    // Start main logo animation
    await mainLogoController.forward();
    
    // Start powered by logo animation after main logo
    await poweredByLogoController.forward();
    
    // Start texts animation after powered by logo
    await textsController.forward();
    
    // Wait for animations to complete
    await Future.delayed(const Duration(seconds: 5));
    
    // Check auth and navigate
    if (FirebaseAuth.instance.currentUser != null) {
      switch (_auth.userRole) {
        case 'admin':
          Get.offAllNamed(Routes.HOME);
          break;
        case 'manager':
          Get.offAllNamed(Routes.SECTOR_MANAGER);
          break;
        case 'supervisor':
          Get.offAllNamed(Routes.SECTORS);
          break;
        case 'user':
          Get.offAllNamed(Routes.CARS);
          break;
        default:
          Get.offAllNamed(Routes.LOGIN);
      }
    } else {
      Get.offAllNamed(Routes.LOGIN);
    }
  }
  
  @override
  void onClose() {
    mainLogoController.dispose();
    poweredByLogoController.dispose();
    textsController.dispose();
    super.onClose();
  }
}
