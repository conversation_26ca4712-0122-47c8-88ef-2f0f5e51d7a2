import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cars_app/app/widgets/process_time_line_car_status.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../routes/app_pages.dart';
import '../themes/app_colors.dart';
import 'chat_button.dart';

class SupervistorCarCard extends StatelessWidget {
  const SupervistorCarCard({
    super.key,
    required this.car,
    required this.currentAction,
    required this.uid,
  });
  final Widget currentAction;
  final Car car;
  final String uid;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.withOpacity(0.1)),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.primaryGradient,
          ).scale(0.02),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Car status icon with hero animation
                  Hero(
                    tag: 'car_status_${car.plateNumber}',
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getStatusColor(car.status).withOpacity(0.15),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColor(car.status).withOpacity(0.2),
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: _buildStatusIcon(car.status),
                    ),
                  ),
                  // Plate number
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        '${car.plateCharacters}-${car.plateNumber}',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          letterSpacing: 0.5,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ),
                  // Status chip
                  _buildStatusChip(car.status),
                ],
              ),
              const SizedBox(height: 8),
              // Car details in horizontal chips
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildInfoChip(
                      context,
                      Icons.directions_car,
                      car.carType,
                      Colors.teal,
                    ),
                    _buildInfoChip(
                      context,
                      Icons.directions_car,
                      car.sectorName,
                      Colors.pinkAccent,
                    ),
                    const SizedBox(width: 8),
                    _buildInfoChip(
                      context,
                      Icons.model_training,
                      car.carModel,
                      AppColors.primary,
                    ),
                  ],
                ),
              ),
              // Chat button
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: ChatButton(car: car, receiverId: '', senderId: uid),
              ),
              // Process timeline if applicable
              if (car.status != CarStatus.active &&
                  car.workshopHistory.isNotEmpty &&
                  car.workshopHistory.last.statuses.isNotEmpty) ...[
                const Divider(height: 16),
                ProcessTimelineCarStatus(car: car),
              ],
              // Action buttons
              currentAction,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Cairo',
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildStatusChip(CarStatus status) {
    Color color;
    String text;

    switch (status) {
      case CarStatus.sentRequest:
        color = AppColors.warning;
        text = 'طلب جديد'.tr;
        break;
      case CarStatus.done:
        color = AppColors.success;
        text = 'جاهزة'.tr;
        break;
      case CarStatus.receipt:
        color = AppColors.success;
        text = 'تم الأستلام'.tr;
        break;
      case CarStatus.sendGroup:
        color = AppColors.primary;
        text = 'أرسال فريق'.tr;
        break;
      case CarStatus.callToWorkshop:
        color = AppColors.secondary;
        text = 'أستدعاء للورشة'.tr;
        break;
      case CarStatus.sendToLogisticsSupport:
        color = AppColors.secondary;
        text = 'ارسال لدعم اللوجستي'.tr;
        break;
      case CarStatus.pending:
        color = AppColors.accent;
        text = 'قيد المعالجة'.tr;
        break;
      default:
        color = AppColors.textSecondary;
        text = 'غير معروف'.tr;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Cairo',
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  // Get status color based on car status
  Color _getStatusColor(CarStatus status) {
    switch (status) {
      case CarStatus.sentRequest:
        return AppColors.warning;
      case CarStatus.done:
        return AppColors.success;
      case CarStatus.receipt:
        return AppColors.success;
      case CarStatus.sendGroup:
        return AppColors.primary;
      case CarStatus.callToWorkshop:
        return AppColors.secondary;
      case CarStatus.sendToLogisticsSupport:
        return AppColors.secondary;
      case CarStatus.pending:
        return AppColors.accent;
      default:
        return AppColors.textSecondary;
    }
  }

  // Build status icon based on car status
  Widget _buildStatusIcon(CarStatus status) {
    IconData iconData;
    
    switch (status) {
      case CarStatus.sentRequest:
        iconData = Icons.new_releases_outlined;
        break;
      case CarStatus.done:
        iconData = Icons.check_circle_outline;
        break;
      case CarStatus.receipt:
        iconData = Icons.inventory_2_outlined;
        break;
      case CarStatus.sendGroup:
        iconData = Icons.group_outlined;
        break;
      case CarStatus.callToWorkshop:
        iconData = Icons.build_outlined;
        break;
      case CarStatus.sendToLogisticsSupport:
        iconData = Icons.local_shipping_outlined;
        break;
      case CarStatus.pending:
        iconData = Icons.pending_outlined;
        break;
      default:
        iconData = Icons.help_outline;
    }
    
    return Icon(
      iconData,
      size: 16,
      color: _getStatusColor(status),
    );
  }
}
