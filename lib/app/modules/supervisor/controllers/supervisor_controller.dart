import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:cars_app/app/controllers/app_users_controller.dart';
import 'package:cars_app/app/controllers/auth_controller.dart';
import 'package:cars_app/app/data/models/car_rotate.dart';
import 'package:cars_app/app/services/car_rotation_service.dart';
import 'package:cars_app/app/services/car_service.dart';
import 'package:cars_app/app/services/user_service.dart';
import 'package:cars_app/app/widgets/awesome_snackbar_content.dart';
import 'package:cars_app/app/widgets/custom_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';
import '../../../data/models/car_model.dart';
import '../../../data/models/user_model.dart';
import '../../../data/models/vehicle_inspection.dart';
import '../../../routes/app_pages.dart';
import '../../../services/firestore_service.dart';
import '../../../services/user_storage_service.dart';

class SupervisorController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LocalStorageService localStorageService =
      Get.find<LocalStorageService>();
  final RxString uid = ''.obs;
  final RxString userName = ''.obs;
  final CarService service = Get.find<CarService>();
  final CarRotationService rotationService = Get.find<CarRotationService>();
  final FirestoreService _service = Get.find();

  Rx<UserModel?>? selectedTechnician;
  final AppUsersController usersController = Get.find<AppUsersController>();

  @override
  void onInit() async {
    super.onInit();
    uid.value = (await localStorageService.getUserId()!)!;
    userName.value = (await localStorageService.getUsername()!)!;
  }

  Future<void> updateCarStatus(Car car, String carId, CarStatus newStatus,
      String supervisorId, String? notes,
      {String? technicianId}) async {
    if (newStatus == CarStatus.pending) {
      // Pass the car to the vehicle inspection view
      final result =
          await Get.toNamed(Routes.VEHICLE_INSPECTION, arguments: car);

      // Check if we got a result back
      if (result != null && result is Map<String, dynamic>) {
        Logger().d("Got result from vehicle inspection");

        // Extract the inspection and updated car
        final inspection = result['inspection'] as VehicleInspection?;
        final updatedCar = result['car'] as Car?;

        if (inspection != null) {
          Logger().d("Inspection meter reading: ${inspection.meterReading}");

          // If we have an updated car with meter reading, use it
          if (updatedCar != null) {
            Logger().d("Using updated car from inspection");

            // The car has already been updated in Firestore by the VehicleInspectionController
            // Just update the status now
            await service.updateCarStatusWithEntry(
                updatedCar, carId, newStatus, notes,
                technicianId: technicianId, inspection: inspection);
            return;
          }
        }
      }

      // Fallback if we didn't get a proper result
      await service.updateCarStatusWithEntry(car, carId, newStatus, notes,
          technicianId: technicianId);
    } else {
      await service.updateCarStatusWithEntry(car, carId, newStatus, notes,
          technicianId: technicianId);
    }
  }

  Widget getCurrentActions(String carId, Car car) {
    if (car.status == CarStatus.receipt) {
      return Column(
        children: [
          Obx(() {
            // Initialize selectedTechnician if not already set
            if (selectedTechnician == null) {
              selectedTechnician = Rx<UserModel?>(null);
            }

            // Get updated technicians list
            final techList = usersController.technicians;

            return Container(
              margin: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                gradient: LinearGradient(
                  colors: [Colors.white, Colors.grey.shade50],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: DropdownButtonFormField<UserModel>(
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Theme.of(Get.context!).primaryColor),
                  ),
                  prefixIcon: Icon(Icons.engineering,
                      color: Theme.of(Get.context!).primaryColor),
                  hintText: 'اختر فني',
                  hintStyle: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                  ),
                ),
                isExpanded: true,
                icon: Icon(Icons.arrow_drop_down,
                    color: Theme.of(Get.context!).primaryColor),
                value: selectedTechnician!.value,
                items: techList.map((UserModel technician) {
                  return DropdownMenuItem<UserModel>(
                    value: technician,
                    child: Text(
                      technician.name,
                      style: const TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 14,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (UserModel? newValue) {
                  selectedTechnician!.value = newValue;
                },
              ),
            );
          }),
          Obx(() {
            return CustomButton(
              isSwipe: true,
              isActive: selectedTechnician?.value != null,
              text: 'تحويل للفني',
              onPressed: () {
                if (selectedTechnician?.value != null) {
                  updateCarStatus(
                      technicianId:
                          selectedTechnician!.value!.username + '@hilal.com',
                      car,
                      carId,
                      CarStatus.pending,
                      selectedTechnician!.value!.id,
                      'تم تحويل المركبة للفني ${selectedTechnician!.value!.name}');
                } else {
                  Get.snackbar(
                    'تنبيه',
                    'يرجى اختيار فني أولاً',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.amber,
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(8),
                    borderRadius: 10,
                    duration: const Duration(seconds: 3),
                  );
                }
              },
            );
          }),
        ],
      );
    } else if (car.status == CarStatus.callToWorkshop ||
        car.status == CarStatus.sendGroup) {
      return SizedBox();
    } else if (car.status == CarStatus.agreeDeliveryToWorkShop ||
        car.status == CarStatus.receipt) {
      return CustomButton(
        isSwipe: true,
        text: 'أستلام المركبة',
        onPressed: () {
          updateCarStatus(car, carId, CarStatus.receipt, '', '');
          _service.createNoticiation(
              UserRole.technician.name, [], car, 'newRequest');
        },
      );
    } else if (car.status == CarStatus.done) {
      return CustomButton(
        isSwipe: true,
        text: 'أرجاع المركبة للدعم اللوجستي',
        onPressed: () {
          _service.createNoticiation(
              UserRole.logisticsSupport.name, [], car, 'newRequest');

          updateCarStatus(car, carId, CarStatus.sendToLogisticsSupport, '', '');
        },
      );
    } else if (car.status == CarStatus.pending) {
      return SizedBox();
    } else if (car.status == CarStatus.sentRequest) {
      return Column(
        children: [
          CustomButton(
            isSwipe: true,
            text: 'أرسال فريق',
            onPressed: () {
              updateCarStatus(car, carId, CarStatus.sendGroup, '', '');
              _service.createNoticiation(
                  'logistic_support', [], car, 'newRequest');
            },
          ),
          SizedBox(
            height: 16,
          ),
          CustomButton(
            isSwipe: true,
            text: 'أستدعاء المركبة',
            onPressed: () {
              _service.createNoticiation(
                  'sector_${car.sectorId}', [], car, 'newRequest');

              updateCarStatus(car, carId, CarStatus.callToWorkshop, '', '');
            },
          ),
        ],
      );
    } else {}
    return SizedBox();
  }

  void activeRotateCar(CarRotate carRotate, bool agree) {
    print('ddddddddddddd${carRotate.fromSectorId} $agree');
    if (agree) {
      if (carRotate.fromSectorId.isEmpty) {
        Get.toNamed(Routes.CAR_ROTATION, arguments: carRotate);
      } else {
        rotationService.activeRotateCar(carRotate, agree);
      }
    } else {
      rotationService.updateRotation(carRotate, false);
    }
  }
}
