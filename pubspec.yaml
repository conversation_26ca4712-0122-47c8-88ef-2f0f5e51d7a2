name: cars_app
description: A new Flutter project.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.1.5 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_login: ^5.0.0
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.9 # use latest
  xml: ^6.5.0
  get: ^4.6.6
  timeline_tile: ^2.0.0
  intl: ^0.19.0
  logger:
  fl_chart: ^0.65.0
  get_storage: ^2.1.1
  cloud_firestore: ^5.6.4
  shared_preferences: ^2.5.2
  firebase_core: ^3.12.0
  firebase_auth: ^5.5.2
  firebase_storage: ^12.4.3
  google_fonts: ^6.1.0
  timeago:
  flutter_screenutil: ^5.9.3
  flutter_form_builder: ^9.5.0
  loading_animation_widget: ^1.3.0
  form_builder_validators: ^11.0.0
  timelines_plus: ^1.0.6
  sidebarx: ^0.17.1
  flutter_zoom_drawer: ^3.2.0
  shimmer: ^3.0.0
  firebase_messaging: ^15.2.4
  awesome_snackbar_content: ^0.1.5
  responsive_grid_list: ^1.4.0
  animated_text_kit: ^4.2.3
  flutter_animated_icons: ^1.0.1
  uuid: ^4.5.1
  flutter_swipe_button: ^2.1.3
  googleapis_auth:
  flutter_local_notifications: ^19.0.0
  pdf: ^3.10.7
  excel: ^4.0.6
  path_provider: ^2.1.2
  open_file: ^3.3.2
  permission_handler: ^11.4.0
  flutter_spinkit: ^5.1.0 # Example loading indicator
  flutter_speed_dial: ^6.0.0 # Example animated FAB
  syncfusion_flutter_core: ^25.1.40 # or latest
  syncfusion_flutter_charts: ^25.1.40
  syncfusion_flutter_datepicker: ^25.1.40
  flutter_typeahead: ^5.0.0
  flutter_chips_input: ^1.10.0
  flutter_staggered_grid_view: ^0.7.0 # For responsive grid layout
  lottie: ^1.4.3 # For animated illustrations (compatible with flutter_animated_icons)
  flutter_slidable: ^3.0.1 # For swipe actions on cards
  connectivity_plus: ^5.0.2 # For internet connection checking
  universal_html: ^2.2.4
  syncfusion_flutter_xlsio: ^25.2.7
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/fonts/
    - assets/images/
    - assets/fonts/NotoSansArabic.ttf
    - assets/assets/animations/
    - assets/mbulance.svg
  fonts:
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic.ttf
  generate: true