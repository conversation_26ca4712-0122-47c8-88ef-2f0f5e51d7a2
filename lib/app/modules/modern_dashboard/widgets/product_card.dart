import 'package:cars_app/app/modules/inventory/models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../themes/app_colors.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final Function()? onTap;
  final Function()? onEdit;
  final Function()? onDelete;
  final Function()? onRestock;
  final bool isRTL;

  const ProductCard({
    Key? key,
    required this.product,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onRestock,
    this.isRTL = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isLowStock = product.currentQuantity <= 0;

    return Slidable(
      key: ValueKey(product.id),
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        dismissible: DismissiblePane(onDismissed: () {}),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppColors.info,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'edit'.tr,
            ),
          if (onRestock != null)
            SlidableAction(
              onPressed: (_) => onRestock!(),
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              icon: Icons.add_shopping_cart,
              label: 'restock'.tr,
            ),
        ],
      ),
      endActionPane: onDelete != null
          ? ActionPane(
              motion: const DrawerMotion(),
              children: [
                SlidableAction(
                  onPressed: (_) => onDelete!(),
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                  icon: Icons.delete,
                  label: 'delete'.tr,
                ),
              ],
            )
          : null,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: isLowStock
                ? Border.all(color: AppColors.warning.withOpacity(0.5))
                : null,
          ),
          child: Column(
            crossAxisAlignment: isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:  const Icon(
                            Icons.inventory_2,
                            color: AppColors.primary,
                            size: 24,
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: isRTL
                          ? CrossAxisAlignment.end
                          : CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          product.description ?? '',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  _buildStockIndicator(isLowStock),
                ],
              ),
              const SizedBox(height: 12),
              Divider(color: Colors.grey[200]),
              const SizedBox(height: 12),
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildInfoItem(
                    Icons.inventory,
                    '${product.currentQuantity} ${'in_stock'.tr}',
                    isLowStock ? AppColors.warning : AppColors.info,
                  ),
                  _buildInfoItem(
                    Icons.category,
                    product.name ?? 'uncategorized'.tr,
                    AppColors.primary,
                  ),
                  _buildInfoItem(
                    Icons.attach_money,
                    '${product.name} SAR',
                    AppColors.success,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStockIndicator(bool isLowStock) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isLowStock
            ? AppColors.warning.withOpacity(0.1)
            : AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isLowStock
              ? AppColors.warning.withOpacity(0.3)
              : AppColors.success.withOpacity(0.3),
        ),
      ),
      child: Text(
        isLowStock ? 'low_stock'.tr : 'in_stock'.tr,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: isLowStock ? AppColors.warning : AppColors.success,
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}
