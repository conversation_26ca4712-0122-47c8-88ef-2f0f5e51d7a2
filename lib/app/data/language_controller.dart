import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../themes/app_theme.dart';
import '../controllers/theme_controller.dart';

class LanguageController extends GetxController {
  final _box = GetStorage();
  final _key = 'isArabic';

  bool get isArabic => _box.read(_key) ?? false;

  /// Get theme with proper language support
  ThemeData get getTheme {
    final themeController = Get.find<ThemeController>();
    final isDark = themeController.isDarkMode;

    if (isDark) {
      return AppTheme.darkTheme.copyWith(
        textTheme: AppTheme.getTextTheme(isDark, isArabic),
      );
    } else {
      return AppTheme.lightTheme.copyWith(
        textTheme: AppTheme.getTextTheme(isDark, isArabic),
      );
    }
  }

  void changeLanguage() {
    _box.write(_key, !isArabic);
    if (!isArabic) {
      Get.updateLocale(const Locale('ar', 'SA'));
    } else {
      Get.updateLocale(const Locale('en', 'US'));
    }
    update();

    // Update theme controller to refresh theme with new language
    if (Get.isRegistered<ThemeController>()) {
      Get.find<ThemeController>().update();
    }
  }

  void loadSelectedLanguage() {
    if (isArabic) {
      Get.updateLocale(const Locale('ar', 'SA'));
    } else {
      Get.updateLocale(const Locale('en', 'US'));
    }
    update();
  }

  /// Get current language code
  String get currentLanguageCode => isArabic ? 'ar' : 'en';

  /// Get current language name
  String get currentLanguageName => isArabic ? 'العربية' : 'English';

  /// Set language by code
  void setLanguage(String languageCode) {
    final wasArabic = isArabic;
    _box.write(_key, languageCode == 'ar');

    if (wasArabic != isArabic) {
      Get.updateLocale(Locale(languageCode, languageCode == 'ar' ? 'SA' : 'US'));
      update();

      // Update theme controller to refresh theme with new language
      if (Get.isRegistered<ThemeController>()) {
        Get.find<ThemeController>().update();
      }
    }
  }
}
