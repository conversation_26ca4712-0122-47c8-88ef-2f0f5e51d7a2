import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../data/models/notification_model.dart';
import '../data/models/user_model.dart';
import 'user_storage_service.dart';

// This is a simplified version of the notification service for web deployment
// It doesn't use FCM or local notifications, which are causing build issues
class NotificationService extends GetxService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final RxString fcmToken = ''.obs;
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxInt unreadCount = 0.obs;
  final RxBool isLoading = false.obs;
  User? user = FirebaseAuth.instance.currentUser;
  Future<NotificationService> init() async {
    // Web implementation doesn't need FCM initialization
    return this;
  }

  Future<void> updateUserFcmToken(String token) async {
    // No-op for web
    return;
  }

  Stream<List<NotificationModel>> getNotificationsForUser(String userId) {
    return _firestore
        .collection('notifications')
        .where('userIds', arrayContains: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    });
  }

  Future<void> markAsRead(String notificationId, String userId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'readBy.$userId': true,
      });
    } catch (e) {
      print('Error marking notification as read: $e');
      rethrow;
    }
  }

  Future<void> markAllAsRead(String userId) async {
    try {
      final batch = _firestore.batch();
      final notifications = await _firestore
          .collection('notifications')
          .where('userIds', arrayContains: userId)
          .get();

      for (var doc in notifications.docs) {
        batch.update(doc.reference, {
          'readBy.$userId': true,
        });
      }

      await batch.commit();
    } catch (e) {
      print('Error marking all notifications as read: $e');
      rethrow;
    }
  }

  Future<void> createNotification({
    required String title,
    required String content,
    required NotificationType type,
    required UserRole role,
    required List<String> userIds,
  }) async {
    try {
      await _firestore.collection('notifications').add({
        'title': title,
        'content': content,
        'type': type.toString(),
        'createdAt': FieldValue.serverTimestamp(),
        'userIds': userIds,
        'role': role.toString(),
        'readBy': {},
      });
    } catch (e) {
      print('Error creating notification: $e');
      rethrow;
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      print('Error deleting notification: $e');
      rethrow;
    }
  }

  void updateUnreadCount(List<NotificationModel> notifications, String userId) {
    unreadCount.value = notifications
        .where((notification) => !(notification.readBy[userId] ?? false))
        .length;
  }

  // Stub methods to maintain API compatibility
  Future<void> sendNotificationToTokens({
    required List<String> tokens,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    // No-op for web
    return;
  }
  
  // Get unread notifications count by role
  Stream<int> getUnreadNotificationCountByRole(String role) {
    Logger().e(role);
    return _firestore
        .collection('notifications')
        .where('role', isEqualTo: role)
        .snapshots()
        .map((snapshot) {
          final notifications = snapshot.docs
              .map((doc) => NotificationModel.fromFirestore(doc))
              .toList();
          
          // Count notifications that haven't been read by anyone
          return notifications
              .where((notification) => notification.readBy.isEmpty)
              .length;
        });
  }
  
  // Mark all notifications for a role as read
  Future<void> markAllAsReadForRole(String role) async {
    try {
      final batch = _firestore.batch();

      // احصل على جميع الإشعارات المرتبطة بهذا الدور
      final notifications = await _firestore
          .collection('notifications')
          .where('role', isEqualTo: role)
          .get();

      // مثلاً userId هو معرف المستخدم الحالي
      final userId = user!.uid;

      for (var doc in notifications.docs) {
        // أنشئ تحديث لإضافة userId إلى readBy
        final readByUpdates = {
          'readBy.$userId': true,
        };

        batch.update(doc.reference, readByUpdates);
      }

      // نفذ كل التحديثات دفعة واحدة
      await batch.commit();
    } catch (e) {
      print('حدث خطأ: $e');
    }

  }
  
  // Helper method to get user IDs by role
  Future<List<String>> _getUserIdsByRole(String role) async {
    try {
      final usersSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: role)
          .get();
          
      return usersSnapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      print('Error getting users by role: $e');
      return [];
    }
  }
}
