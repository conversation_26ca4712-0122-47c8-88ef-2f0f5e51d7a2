import 'package:cars_app/app/services/user_storage_service.dart';
import 'package:get/get.dart';
import '../../../data/models/notification_model.dart';
import '../../../services/notification_service_selector.dart';
import '../../../controllers/auth_controller.dart';

class NotificationsController extends GetxController {
  final NotificationService notificationService =
      Get.find<NotificationService>();
  final LocalStorageService _storageService = LocalStorageService();
  String uid = '';
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxInt unreadCount = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    uid = (await _storageService.getUserId())!;
    loadNotifications();
  }

  void loadNotifications() {
    final userId = uid;

    notificationService.getNotificationsForUser(userId).listen(
      (notificationList) {
        notifications.value = notificationList;
        notificationService.updateUnreadCount(notificationList, userId);
        unreadCount.value = notificationList
            .where((notification) => !(notification.readBy[userId] ?? false))
            .length;
      },
      onError: (error) {
        print('Error loading notifications: $error');
        Get.snackbar(
          'error'.tr,
          'error_loading_notifications'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  Future<void> markAsRead(String notificationId) async {
    final userId = uid;
    if (userId == null) return;

    try {
      await notificationService.markAsRead(notificationId, userId);
    } catch (e) {
      print('Error marking notification as read: $e');
      Get.snackbar(
        'error'.tr,
        'error_marking_read'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> markAllAsRead() async {
    final userId = uid;
    if (userId == null) return;

    try {
      await notificationService.markAllAsRead(userId);
    } catch (e) {
      print('Error marking all notifications as read: $e');
      Get.snackbar(
        'error'.tr,
        'error_marking_all_read'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await notificationService.deleteNotification(notificationId);
      Get.snackbar(
        'success'.tr,
        'notification_deleted'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error deleting notification: $e');
      Get.snackbar(
        'error'.tr,
        'error_deleting_notification'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
