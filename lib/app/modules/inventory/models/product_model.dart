// product_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  final String? id; // Nullable for creation before Firestore assigns ID
  final String name;
  final String? sku;
  final String? description;
  final int currentQuantity;
  final double? purchasePrice;
  final double? sellingPrice;
  final int? lowStockThreshold;
  final Timestamp? createdAt;
  final Timestamp? updatedAt;

  ProductModel({
    this.id,
    required this.name,
    this.sku,
    this.description,
    required this.currentQuantity,
    this.purchasePrice,
    this.sellingPrice,
    this.lowStockThreshold,
    this.createdAt,
    this.updatedAt,
  });

  factory ProductModel.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map<String, dynamic>;
    return ProductModel(
      id: doc.id,
      name: data['name'] ?? '',
      sku: data['sku'],
      description: data['description'],
      currentQuantity: data['currentQuantity'] ?? 0,
      purchasePrice: (data['purchasePrice'] as num?)?.toDouble(),
      sellingPrice: (data['sellingPrice'] as num?)?.toDouble(),
      lowStockThreshold: data['lowStockThreshold'],
      createdAt: data['createdAt'] as Timestamp?,
      updatedAt: data['updatedAt'] as Timestamp?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'sku': sku,
      'description': description,
      'currentQuantity': currentQuantity,
      'purchasePrice': purchasePrice,
      'sellingPrice': sellingPrice,
      'lowStockThreshold': lowStockThreshold,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(), // Set on creation
      'updatedAt': FieldValue.serverTimestamp(), // Always update on write
    };
  }

// Optional: copyWith for easier updates
}

// transaction_model.dart
