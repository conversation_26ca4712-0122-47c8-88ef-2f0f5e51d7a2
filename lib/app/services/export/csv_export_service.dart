import 'dart:convert';
import 'dart:typed_data';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../modules/reports/controllers/reports_controller.dart';

/// Service for generating CSV reports
class CsvExportService {
  /// Generate a workshop report CSV
  Uint8List generateWorkshopReport(List<WorkshopReportEntry> entries) {
    // Create a simple CSV
    final csv = StringBuffer();
    
    // Add header
    csv.writeln(
        'رقم اللوحة,موديل السيارة,القطاع,الحالة,التاريخ,الملاحظات,نوع الصيانة,تم التحديث بواسطة');
    
    // Add data rows
    for (final entry in entries) {
      csv.writeln([
        '"${entry.car.plateNumber}-${entry.car.plateCharacters}"',
        '"${entry.car.carModel}"',
        '"${entry.car.sectorName}"',
        '"${entry.status.status.toString().split('.').last.tr}"',
        '"${DateFormat('yyyy-MM-dd HH:mm').format(entry.status.createAt)}"',
        '"${entry.status.notes ?? ''}"',
        '"${entry.workshopEntry.maintenance_type ?? ''}"',
        '"${entry.status.senderName}"',
      ].join(','));
    }
    
    return Uint8List.fromList(utf8.encode(csv.toString()));
  }
  
  /// Generate a car report CSV
  Uint8List generateCarReport(List<WorkshopReportEntry> entries) {
    // Create a simple CSV
    final csv = StringBuffer();
    
    // Add header
    csv.writeln(
        'رقم,نوع الصيانة,الحالة,التاريخ,الملاحظات,تم التحديث بواسطة');
    
    // Add data rows
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      csv.writeln([
        '"${i + 1}"',
        '"${entry.workshopEntry.maintenance_type ?? 'غير محدد'}"',
        '"${entry.status.status.toString().split('.').last.tr}"',
        '"${DateFormat('yyyy-MM-dd HH:mm').format(entry.status.createAt)}"',
        '"${entry.status.notes ?? ''}"',
        '"${entry.status.senderName}"',
      ].join(','));
    }
    
    return Uint8List.fromList(utf8.encode(csv.toString()));
  }
}
