import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../../../../themes/app_colors.dart';
import '../../controllers/reports_controller.dart';

class DailyActivityChart extends StatelessWidget {
  final ReportsController controller;

  const DailyActivityChart({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              AppColors.secondary.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppColors.secondary),
                const SizedBox(width: 8),
                Text(
                  'النشاط اليومي'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildChartTypeToggle(),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() => SizedBox(
              height: 300,
              child: controller.activityChartType.value == ActivityChartType.line
                  ? _buildLineChart()
                  : _buildAreaChart(),
            )),
            const SizedBox(height: 8),
            Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Obx(() {
                  final totalActivity = controller.dailyActivity.fold<int>(
                    0, (sum, item) => sum + item.count);
                  
                  return Text(
                    'إجمالي النشاط: $totalActivity',
                    style: TextStyle(
                      color: AppColors.secondary,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartTypeToggle() {
    return Obx(() => SegmentedButton<ActivityChartType>(
      segments: const [
        ButtonSegment<ActivityChartType>(
          value: ActivityChartType.line,
          icon: Icon(Icons.show_chart),
          label: Text('خط'),
        ),
        ButtonSegment<ActivityChartType>(
          value: ActivityChartType.area,
          icon: Icon(Icons.area_chart),
          label: Text('مساحة'),
        ),
      ],
      selected: {controller.activityChartType.value},
      onSelectionChanged: (Set<ActivityChartType> newSelection) {
        controller.activityChartType.value = newSelection.first;
      },
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.resolveWith<Color>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.selected)) {
              return AppColors.secondary;
            }
            return Colors.white;
          },
        ),
        foregroundColor: MaterialStateProperty.resolveWith<Color>(
          (Set<MaterialState> states) {
            if (states.contains(MaterialState.selected)) {
              return Colors.white;
            }
            return AppColors.secondary;
          },
        ),
      ),
    ));
  }

  Widget _buildLineChart() {
    return SfCartesianChart(
      primaryXAxis: DateTimeAxis(
        dateFormat: DateFormat('dd/MM'),
        intervalType: DateTimeIntervalType.days,
        majorGridLines: const MajorGridLines(width: 0),
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
      ),
      primaryYAxis: NumericAxis(
        labelFormat: '{value}',
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        zoomMode: ZoomMode.x,
      ),
      crosshairBehavior: CrosshairBehavior(
        enable: true,
        lineType: CrosshairLineType.both,
      ),
      trackballBehavior: TrackballBehavior(
        enable: true,
        tooltipSettings: const InteractiveTooltip(
          format: 'point.x : point.y',
        ),
      ),
      series: [
        LineSeries<DailyActivity, DateTime>(
          dataSource: controller.dailyActivity,
          xValueMapper: (DailyActivity data, _) => data.date,
          yValueMapper: (DailyActivity data, _) => data.count,
          name: 'عدد السجلات',
          markerSettings: const MarkerSettings(
            isVisible: true,
            shape: DataMarkerType.circle,
            height: 8,
            width: 8,
          ),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelAlignment: ChartDataLabelAlignment.top,
          ),
          color: AppColors.secondary,
          width: 3,
        ),
      ],
    );
  }

  Widget _buildAreaChart() {
    return SfCartesianChart(
      primaryXAxis: DateTimeAxis(
        dateFormat: DateFormat('dd/MM'),
        intervalType: DateTimeIntervalType.days,
        majorGridLines: const MajorGridLines(width: 0),
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
      ),
      primaryYAxis: NumericAxis(
        labelFormat: '{value}',
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        zoomMode: ZoomMode.x,
      ),
      crosshairBehavior: CrosshairBehavior(
        enable: true,
        lineType: CrosshairLineType.both,
      ),
      trackballBehavior: TrackballBehavior(
        enable: true,
        tooltipSettings: const InteractiveTooltip(
          format: 'point.x : point.y',
        ),
      ),
      series: [
        AreaSeries<DailyActivity, DateTime>(
          dataSource: controller.dailyActivity,
          xValueMapper: (DailyActivity data, _) => data.date,
          yValueMapper: (DailyActivity data, _) => data.count,
          name: 'عدد السجلات',
          markerSettings: const MarkerSettings(
            isVisible: true,
            shape: DataMarkerType.circle,
            height: 8,
            width: 8,
          ),
          dataLabelSettings: const DataLabelSettings(
            isVisible: true,
            labelAlignment: ChartDataLabelAlignment.top,
          ),
          color: AppColors.secondary,
          borderColor: AppColors.secondary,
          borderWidth: 3,
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.secondary.withOpacity(0.7),
              AppColors.secondary.withOpacity(0.1),
            ],
          ),
        ),
      ],
    );
  }
}
