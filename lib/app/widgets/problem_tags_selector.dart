import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/problem_tag_model.dart';
import '../services/problem_tag_service.dart';
import '../themes/app_colors.dart';

class ProblemTagsSelector extends StatefulWidget {
  final List<String> initialSelectedTags;
  final Function(List<String>) onTagsChanged;
  final bool showAddButton;
  final String hintText;

  const ProblemTagsSelector({
    Key? key,
    this.initialSelectedTags = const [],
    required this.onTagsChanged,
    this.showAddButton = true,
    this.hintText = 'اختر المشاكل أو أضف مشكلة جديدة',
  }) : super(key: key);

  @override
  State<ProblemTagsSelector> createState() => _ProblemTagsSelectorState();
}

class _ProblemTagsSelectorState extends State<ProblemTagsSelector> {
  final ProblemTagService _tagService = Get.find<ProblemTagService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<String> _selectedTagIds = <String>[].obs;
  final RxString _searchQuery = ''.obs;
  final RxBool _isAddingNewTag = false.obs;
  final TextEditingController _newTagController = TextEditingController();
  final TextEditingController _newCategoryController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _selectedTagIds.value = List<String>.from(widget.initialSelectedTags);
    
    // Load tags if not already loaded
    if (_tagService.tags.isEmpty) {
      _tagService.loadTags();
    }
    
    // Listen for search changes
    _searchController.addListener(() {
      _searchQuery.value = _searchController.text;
    });
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _newTagController.dispose();
    _newCategoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search and Add button row
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
              ),
            ),
            if (widget.showAddButton) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  _isAddingNewTag.value = true;
                  _showAddTagDialog();
                },
                icon: const Icon(Icons.add_circle),
                color: AppColors.primary,
                tooltip: 'إضافة مشكلة جديدة',
              ),
            ],
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Selected Tags
        Obx(() => _selectedTagIds.isNotEmpty
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'المشاكل المحددة:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _selectedTagIds.map((tagId) {
                    final tag = _tagService.tags.firstWhereOrNull(
                      (t) => t.id == tagId
                    ) ?? ProblemTag(
                      id: tagId,
                      name: tagId, // Use ID as name if tag not found
                      category: 'custom',
                      createdAt: DateTime.now(),
                    );
                    
                    return Chip(
                      label: Text(tag.name),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        _selectedTagIds.remove(tagId);
                        widget.onTagsChanged(_selectedTagIds);
                      },
                      backgroundColor: AppColors.primary.withOpacity(0.1),
                      labelStyle: const TextStyle(color: AppColors.primary),
                      deleteIconColor: AppColors.primary,
                    );
                  }).toList(),
                ),
                const Divider(height: 24),
              ],
            )
          : const SizedBox.shrink()
        ),
        
        // Available Tags
        Obx(() {
          if (_tagService.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }
          
          final filteredTags = _searchQuery.value.isEmpty
              ? _tagService.tags
              : _tagService.searchTags(_searchQuery.value);
          
          if (filteredTags.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  _searchQuery.value.isEmpty
                      ? 'لا توجد مشاكل متاحة'
                      : 'لا توجد نتائج لـ "${_searchQuery.value}"',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),
            );
          }
          
          // Group tags by category
          final Map<String, List<ProblemTag>> tagsByCategory = {};
          for (var tag in filteredTags) {
            if (!tagsByCategory.containsKey(tag.category)) {
              tagsByCategory[tag.category] = [];
            }
            tagsByCategory[tag.category]!.add(tag);
          }
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: tagsByCategory.entries.map((entry) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Text(
                      entry.key,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: entry.value.map((tag) {
                      final isSelected = _selectedTagIds.contains(tag.id);
                      
                      return FilterChip(
                        label: Text(tag.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            _selectedTagIds.add(tag.id);
                            // Increment usage count in the background
                            _tagService.incrementUsageCount(tag.id);
                          } else {
                            _selectedTagIds.remove(tag.id);
                          }
                          widget.onTagsChanged(_selectedTagIds);
                        },
                        backgroundColor: Colors.grey[200],
                        selectedColor: AppColors.primary.withOpacity(0.2),
                        checkmarkColor: AppColors.primary,
                        labelStyle: TextStyle(
                          color: isSelected ? AppColors.primary : Colors.black87,
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                ],
              );
            }).toList(),
          );
        }),
      ],
    );
  }
  
  void _showAddTagDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مشكلة جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _newTagController,
              decoration: const InputDecoration(
                labelText: 'اسم المشكلة',
                hintText: 'أدخل اسم المشكلة',
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            Obx(() {
              final categories = _tagService.getAllCategories();
              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  hintText: 'اختر الفئة',
                ),
                value: categories.contains(_newCategoryController.text) 
                    ? _newCategoryController.text 
                    : (categories.isNotEmpty ? categories.first : null),
                items: [
                  ...categories.map((category) => DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  )),
                  const DropdownMenuItem(
                    value: 'custom',
                    child: Text('فئة جديدة'),
                  ),
                ],
                onChanged: (value) {
                  if (value == 'custom') {
                    _showAddCategoryDialog();
                  } else if (value != null) {
                    _newCategoryController.text = value;
                  }
                },
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _newTagController.clear();
              _isAddingNewTag.value = false;
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_newTagController.text.trim().isNotEmpty) {
                final category = _newCategoryController.text.isEmpty 
                    ? 'general' 
                    : _newCategoryController.text;
                
                final newTag = ProblemTag(
                  id: '', // Will be set by Firestore
                  name: _newTagController.text.trim(),
                  category: category,
                  usageCount: 1, // Start with 1 since we're using it
                  createdAt: DateTime.now(),
                );
                
                final tagId = await _tagService.addTag(newTag);
                
                if (tagId.isNotEmpty) {
                  _selectedTagIds.add(tagId);
                  widget.onTagsChanged(_selectedTagIds);
                }
                
                _newTagController.clear();
                _isAddingNewTag.value = false;
                Navigator.of(context).pop();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }
  
  void _showAddCategoryDialog() {
    final TextEditingController categoryController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة فئة جديدة'),
        content: TextField(
          controller: categoryController,
          decoration: const InputDecoration(
            labelText: 'اسم الفئة',
            hintText: 'أدخل اسم الفئة الجديدة',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (categoryController.text.trim().isNotEmpty) {
                _newCategoryController.text = categoryController.text.trim();
                Navigator.of(context).pop();
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    ).then((_) {
      categoryController.dispose();
    });
  }
}
