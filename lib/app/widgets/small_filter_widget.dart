import 'package:cars_app/app/data/models/car_model.dart';
import 'package:cars_app/app/modules/sector/controller/sector_manager_controller.dart';
import 'package:cars_app/app/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SmallFilterWidget extends GetView<SectorManagerController> {
  const SmallFilterWidget({super.key});

  final Map<CarStatus, Color> statusColors = const {
    CarStatus.active: Colors.green,
    CarStatus.maintenance: Colors.orange,
    CarStatus.inWorkshop: Colors.blue,
    CarStatus.destroyed: Colors.red,
    CarStatus.outOfService: Colors.grey,
    CarStatus.sentRequest: Colors.purple,
    CarStatus.sendGroup: Colors.teal,
    CarStatus.receipt: Colors.indigo,
    CarStatus.callToWorkshop: Colors.amber,
    CarStatus.pending: Colors.brown,
    CarStatus.done: Color(0xFF2E7D32), // Colors.green.shade800
  };

  String getStatusText(CarStatus status) {
    switch (status) {
      case CarStatus.active:
        return 'نشط';
      case CarStatus.destroyed:
        return 'معطل';
      case CarStatus.maintenance:
        return 'صيانة';
      case CarStatus.inWorkshop:
        return 'في الورشة';
      case CarStatus.outOfService:
        return 'خارج الخدمة';
      case CarStatus.sentRequest:
        return 'تم الارسال';
      case CarStatus.sendGroup:
        return 'مجموعة';
      case CarStatus.receipt:
        return 'استلام';
      case CarStatus.callToWorkshop:
        return 'طلب ورشة';
      case CarStatus.pending:
        return 'معلق';
      case CarStatus.done:
        return 'مكتمل';
      default:
        return 'غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Obx(() {
        final cars = controller.cars;
        final statusCounts = <CarStatus, int>{};
        
        // Count cars for each status
        for (var car in cars) {
          statusCounts[car.status] = (statusCounts[car.status] ?? 0) + 1;
        }

        final statuses = CarStatus.values.where((status) => 
          statusCounts.containsKey(status) || status == CarStatus.active
        ).toList();

        return ListView.builder(
          itemCount: statuses.length + 1, // +1 for "All" filter
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            if (index == 0) {
              // "All" filter
              return Obx(() => GestureDetector(
                onTap: () => controller.setSelectedStatus(null),
                child: SizedBox(
                  width: 100,
                  height: 100,
                  child: Card(
                    color: controller.selectedStatus.value == null
                        ? AppColors.primary
                        : Colors.grey.shade200,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'الكل',
                            style: TextStyle(
                              color: controller.selectedStatus.value == null
                                  ? Colors.white
                                  : Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${cars.length}',
                            style: TextStyle(
                              color: controller.selectedStatus.value == null
                                  ? Colors.white
                                  : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ));
            }

            final status = statuses[index - 1];
            final count = statusCounts[status] ?? 0;

            return Obx(() => GestureDetector(
              onTap: () => controller.setSelectedStatus(
                controller.selectedStatus.value == status ? null : status
              ),
              child: Card(
                color: controller.selectedStatus.value == status 
                    ? statusColors[status] 
                    : Colors.grey.shade200,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        getStatusText(status),
                        style: TextStyle(
                          color: controller.selectedStatus.value == status 
                              ? Colors.white 
                              : Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$count',
                        style: TextStyle(
                          color: controller.selectedStatus.value == status 
                              ? Colors.white 
                              : Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
          },
        );
      }),
    );
  }
}
