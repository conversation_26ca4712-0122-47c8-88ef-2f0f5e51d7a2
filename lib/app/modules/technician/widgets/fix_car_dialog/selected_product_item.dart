import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../themes/app_colors.dart';
import '../../../inventory/models/product_model.dart';
import '../../../inventory/widgets/quantity_selector.dart';


/// Widget for displaying a single selected product item in the FixCarDialog list.
class SelectedProductItem extends StatelessWidget {
  final ProductModel product;
  final RxInt quantity; // Pass the RxInt for direct observation/modification
  final VoidCallback onRemove;

  const SelectedProductItem({
    Key? key,
    required this.product,
    required this.quantity,
    required this.onRemove,
  }) : super(key: key);

  /// Builds a remove button for the item
  Widget _buildRemoveButton() {
    return IconButton(
      icon: Container(
        padding: EdgeInsets.all(4.r),
        decoration: BoxDecoration(
          color: AppColors.error.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Icon(
          Icons.close,
          color: AppColors.error,
          size: 16.r,
        ),
      ),
      onPressed: onRemove,
      padding: EdgeInsets.zero,
      constraints: BoxConstraints(
        minWidth: 24.w,
        minHeight: 24.h,
      ),
      splashRadius: 20.r,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Use Obx to react to changes in the individual item's quantity
    return Obx(() {
      final currentQuantityValue = quantity.value; // Get the current value

      return LayoutBuilder(
        builder: (context, constraints) {
          // For very small screens, use a stacked layout
          if (constraints.maxWidth < 280) {
            return Card(
              margin: EdgeInsets.symmetric(vertical: 4.h),
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
                side: BorderSide(color: AppColors.border.withOpacity(0.5)),
              ),
              child: Padding(
                padding: EdgeInsets.all(8.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name and remove button
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            product.name,
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildRemoveButton(),
                      ],
                    ),
                    if (product.sku != null)
                      Padding(
                        padding: EdgeInsets.only(top: 2.h, bottom: 6.h),
                        child: Text(
                          product.sku!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    // Quantity selector centered
                    Center(
                      child: QuantitySelectorWidget(
                        initialQuantity: currentQuantityValue,
                        minValue: product.currentQuantity, // Limit quantity to stock
                        onQuantityChanged: (newQty) {
                          quantity.value = newQty; // Update the RxInt
                        },
                        incrementStep: 1,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          // For normal screens, use a row layout
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
            child: Row(
              children: [
                // Product info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (product.sku != null)
                        Text(
                          product.sku!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),

                SizedBox(width: 4.w),

                // Quantity selector with fixed width
                SizedBox(
                  width: 100.w,
                  child: QuantitySelectorWidget(
                    initialQuantity: currentQuantityValue,
                    minValue: product.currentQuantity, // Limit quantity to stock
                    onQuantityChanged: (newQty) {
                      quantity.value = newQty; // Update the RxInt
                    },
                    incrementStep: 1,
                  ),
                ),

                // Remove button
                _buildRemoveButton(),
              ],
            ),
          );
        },
      );
    });
  }
}