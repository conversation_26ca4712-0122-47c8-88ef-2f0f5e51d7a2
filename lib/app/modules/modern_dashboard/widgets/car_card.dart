import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../data/models/car_model.dart';
import '../../../themes/app_colors.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class CarCard extends StatelessWidget {
  final Car car;
  final Function()? onTap;
  final Function()? onEdit;
  final Function()? onDelete;
  final Function()? onMaintenance;
  final Function()? onActivate;
  final bool isRTL;

  const CarCard({
    Key? key,
    required this.car,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onMaintenance,
    this.onActivate,
    this.isRTL = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(car.id),
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        dismissible: DismissiblePane(onDismissed: () {}),
        children: [
          if (onEdit != null)
            SlidableAction(
              onPressed: (_) => onEdit!(),
              backgroundColor: AppColors.info,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: 'edit'.tr,
            ),
          if (onMaintenance != null)
            SlidableAction(
              onPressed: (_) => onMaintenance!(),
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
              icon: Icons.build,
              label: 'maintenance'.tr,
            ),
        ],
      ),
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        children: [
          if (onActivate != null && car.status != CarStatus.active)
            SlidableAction(
              onPressed: (_) => onActivate!(),
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              icon: Icons.check_circle,
              label: 'activate'.tr,
            ),
          if (onDelete != null)
            SlidableAction(
              onPressed: (_) => onDelete!(),
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: 'delete'.tr,
            ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: isRTL
                          ? CrossAxisAlignment.end
                          : CrossAxisAlignment.start,
                      children: [
                        Row(
                          textDirection: isRTL
                              ? TextDirection.rtl
                              : TextDirection.ltr,
                          children: [
                            Text(
                              car.plateNumber+'-'+car.plateCharacters+' - ${car.carModel}}',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildStatusBadge(),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${car.carModel} - ${car.encoding}',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      car.plateNumber,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Divider(color: Colors.grey[200]),
              const SizedBox(height: 12),
              Row(
                textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildInfoItem(
                    Icons.location_on_outlined,
                    car.sectorName ?? 'unknown'.tr,
                    AppColors.info,
                  ),
                  _buildInfoItem(
                    Icons.person_outline,
                    car.sectorName ?? 'unassigned'.tr,
                    AppColors.warning,
                  ),
                  _buildInfoItem(
                    Icons.speed,
                    '${car.sectorName} km',
                    AppColors.success,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color badgeColor;
    String statusText;

    switch (car.status) {
      case CarStatus.active:
        badgeColor = AppColors.success;
        statusText = 'active'.tr;
        break;
      case CarStatus.maintenance:
      case CarStatus.inWorkshop:
        badgeColor = AppColors.warning;
        statusText = 'maintenance'.tr;
        break;
      case CarStatus.rejected:
        badgeColor = AppColors.error;
        statusText = 'rejected'.tr;
        break;
      case CarStatus.pending:
        badgeColor = AppColors.info;
        statusText = 'pending'.tr;
        break;
      default:
        badgeColor = Colors.grey;
        statusText = 'unknown'.tr;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: badgeColor,
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}
