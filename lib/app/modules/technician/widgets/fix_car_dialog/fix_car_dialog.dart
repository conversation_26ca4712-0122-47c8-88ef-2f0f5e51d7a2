import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../data/models/car_model.dart';
import '../../../../themes/app_colors.dart';
import '../../../inventory/controllers/inventory_controller.dart';
import '../../../inventory/models/product_model.dart';
import '../../controllers/technician_controller.dart';
import 'fix_car_header.dart';
import 'car_info_section.dart';
import 'notes_section.dart';
import 'products_section.dart';
import 'action_buttons.dart';

/// A responsive dialog widget for fixing cars in the technician module
/// This widget handles the car fixing process including notes and product selection
class FixCarDialog extends StatelessWidget {
  final Car car;
  final TechnicianController technicianController;

  // State managed locally within the dialog
  final TextEditingController _notesController = TextEditingController();
  // Using Get.find here is appropriate as the controller is expected to exist
  final InventoryController _inventoryController = Get.find<InventoryController>();
  final RxList<Map<String, dynamic>> _selectedProducts = <Map<String, dynamic>>[].obs; // Map stores ProductModel and RxInt quantity
  final RxBool _isLoadingProducts = true.obs;

  FixCarDialog({
    Key? key,
    required this.car,
    required this.technicianController,
  }) : super(key: key) {
    // Load products when the dialog is initialized
    _loadProducts();
  }

  void _loadProducts() async {
    try {
      await _inventoryController.refreshProducts();
    } catch (e) {
      // Handle error if products fail to load
      print("Error loading products: $e");
      Get.snackbar('Error', 'Failed to load products', snackPosition: SnackPosition.BOTTOM);
    } finally {
      _isLoadingProducts.value = false;
    }
  }

  // Helper methods for responsive dimensions - kept here as they affect the main dialog structure
  double _getMaxDialogWidth(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    // Fixed smaller width for all screen sizes or proportional
    if (screenWidth > 600) {
      return 600.w; // Example: Fixed max width for tablets/desktops
    } else {
      return screenWidth * 0.95; // Use most of the width on smaller screens
    }
  }

  double _getHorizontalPadding(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) {
      final double dialogWidth = _getMaxDialogWidth(context);
      return (screenWidth - dialogWidth) / 2; // Center the dialog
    } else {
      return 20.w; // Standard padding on small screens
    }
  }

  // Handles the submit action
  void _handleSubmit(BuildContext context) {
    if (_notesController.text.trim().isEmpty) {
      Get.snackbar(
        'error'.tr,
        'please_enter_notes'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.8),
        colorText: Colors.white,
      );
      return;
    }

    // Check if any selected product has quantity > 0
    bool hasValidQuantity = _selectedProducts.any(
            (item) => (item['quantity'] as RxInt).value > 0
    );

    if (_selectedProducts.isNotEmpty && !hasValidQuantity) {
      Get.snackbar(
        'error'.tr,
        'please_enter_quantity_for_selected_products'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.8),
        colorText: Colors.white,
      );
      return;
    }


    // Prepare product usage data - only include items with quantity > 0
    final List<Map<String, dynamic>> usedProducts = _selectedProducts
        .where((item) => (item['quantity'] as RxInt).value > 0)
        .map((item) {
      final product = item['product'] as ProductModel;
      final quantity = (item['quantity'] as RxInt).value;

      return {
        'productId': product.id,
        'productName': product.name, // Include name for history/display
        'quantity': quantity,
      };
    }).toList();

    // Update maintenance request with notes and used products
    technicianController.updateMaintenanceRequestWithProducts(
      car,
      _notesController.text.trim(),
      usedProducts,
    );

    // Dispose controllers/Rx variables
    _notesController.dispose();
    // Rx variables tied to the widget lifecycle don't strictly need dispose in StatelessWidget
    // shown by Get.dialog, as Get manages dialog disposal, but it's safe practice.
    _selectedProducts.close(); // Dispose RxList
    _isLoadingProducts.close(); // Dispose RxBool

    Get.back(); // Close the FixCarDialog
  }

  @override
  Widget build(BuildContext context) {
    final double maxWidth = _getMaxDialogWidth(context);
    final double maxHeight = MediaQuery.of(context).size.height * 0.85;
    final double horizontalPadding = _getHorizontalPadding(context);

    return Container(
      width: 200,
      // constraints: BoxConstraints(
      //   maxHeight: maxHeight,
      //   maxWidth: maxWidth,
      // ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          FixCarHeader(),

          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Car info
                  CarInfoSection(car: car),

                  SizedBox(height: 16.h),

                  // Notes section
                  NotesSection(notesController: _notesController),

                  SizedBox(height: 16.h),

                  // Products section
                  ProductsSection(
                    inventoryController: _inventoryController,
                    selectedProducts: _selectedProducts,
                    isLoadingProducts: _isLoadingProducts,
                  ),
                ],
              ),
            ),
          ),

          // Actions
          ActionButtons(
            onCancel: () {
              // Dispose controllers/Rx variables on cancel too
              _notesController.dispose();
              _selectedProducts.close();
              _isLoadingProducts.close();
              Get.back();
            },
            onSubmit: () => _handleSubmit(context),
          ),
        ],
      ),
    );
  }

// No dispose method needed in StatelessWidget, resource cleanup is done in handleSubmit/onCancel
}