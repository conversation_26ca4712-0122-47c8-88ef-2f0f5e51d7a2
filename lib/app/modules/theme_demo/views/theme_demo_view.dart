import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../controllers/theme_controller.dart';
import '../../../data/language_controller.dart';
import '../../../themes/app_colors.dart';
import '../../../widgets/custom_app_bar.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_card.dart';
import '../../../widgets/custom_switch.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/theme_settings_widget.dart';

class ThemeDemoView extends StatelessWidget {
  const ThemeDemoView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Theme Demo',
        variant: AppBarVariant.gradient,
        gradientColors: AppColors.primaryGradient,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Theme Settings Section
            Text(
              'Theme Settings',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            SizedBox(height: 16.h),
            
            const ThemeSettingsWidget(),
            
            SizedBox(height: 32.h),
            
            // Buttons Demo Section
            Text(
              'Buttons Demo',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            SizedBox(height: 16.h),
            
            _buildButtonsDemo(),
            
            SizedBox(height: 32.h),
            
            // Cards Demo Section
            Text(
              'Cards Demo',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            SizedBox(height: 16.h),
            
            _buildCardsDemo(),
            
            SizedBox(height: 32.h),
            
            // Text Fields Demo Section
            Text(
              'Text Fields Demo',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            SizedBox(height: 16.h),
            
            _buildTextFieldsDemo(),
            
            SizedBox(height: 32.h),
            
            // Switches Demo Section
            Text(
              'Switches Demo',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.getTextPrimary(Get.isDarkMode),
              ),
            ),
            SizedBox(height: 16.h),
            
            _buildSwitchesDemo(),
            
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  Widget _buildButtonsDemo() {
    return CustomCard(
      variant: CardVariant.elevated,
      child: Column(
        children: [
          // Primary Buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Primary',
                  variant: ButtonVariant.primary,
                  onPressed: () => Get.snackbar('Button', 'Primary button pressed'),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: CustomButton(
                  text: 'Secondary',
                  variant: ButtonVariant.secondary,
                  onPressed: () => Get.snackbar('Button', 'Secondary button pressed'),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // Outlined and Text Buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Outlined',
                  variant: ButtonVariant.outlined,
                  onPressed: () => Get.snackbar('Button', 'Outlined button pressed'),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: CustomButton(
                  text: 'Text',
                  variant: ButtonVariant.text,
                  onPressed: () => Get.snackbar('Button', 'Text button pressed'),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // Status Buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Success',
                  variant: ButtonVariant.success,
                  size: ButtonSize.small,
                  onPressed: () => Get.snackbar('Button', 'Success button pressed'),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: CustomButton(
                  text: 'Warning',
                  variant: ButtonVariant.warning,
                  size: ButtonSize.small,
                  onPressed: () => Get.snackbar('Button', 'Warning button pressed'),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: CustomButton(
                  text: 'Danger',
                  variant: ButtonVariant.danger,
                  size: ButtonSize.small,
                  onPressed: () => Get.snackbar('Button', 'Danger button pressed'),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // Gradient Button
          CustomButton(
            text: 'Gradient Button',
            variant: ButtonVariant.gradient,
            gradientColors: AppColors.primaryGradient,
            onPressed: () => Get.snackbar('Button', 'Gradient button pressed'),
          ),
        ],
      ),
    );
  }

  Widget _buildCardsDemo() {
    return Column(
      children: [
        // Status Cards Row
        Row(
          children: [
            Expanded(
              child: StatusCard(
                title: 'Total Cars',
                value: '150',
                icon: Icons.directions_car,
                gradientColors: AppColors.cardGradient1,
                onTap: () => Get.snackbar('Card', 'Total cars tapped'),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: StatusCard(
                title: 'Active',
                value: '120',
                icon: Icons.check_circle,
                gradientColors: AppColors.cardGradient2,
                onTap: () => Get.snackbar('Card', 'Active cars tapped'),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        
        // Info Cards
        InfoCard(
          title: 'Vehicle Information',
          subtitle: 'Tap to view details',
          leading: Icon(
            Icons.info_outline,
            color: AppColors.primary,
            size: 24.r,
          ),
          trailing: Icon(
            Icons.arrow_forward_ios,
            color: AppColors.getTextSecondary(Get.isDarkMode),
            size: 16.r,
          ),
          onTap: () => Get.snackbar('Card', 'Info card tapped'),
        ),
        
        SizedBox(height: 12.h),
        
        // Custom Card Variants
        Row(
          children: [
            Expanded(
              child: CustomCard(
                variant: CardVariant.outlined,
                child: Column(
                  children: [
                    Icon(
                      Icons.outlined_flag,
                      color: AppColors.primary,
                      size: 32.r,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Outlined',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: CustomCard(
                variant: CardVariant.filled,
                child: Column(
                  children: [
                    Icon(
                      Icons.format_color_fill,
                      color: AppColors.secondary,
                      size: 32.r,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Filled',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTextFieldsDemo() {
    return CustomCard(
      variant: CardVariant.elevated,
      child: Column(
        children: [
          CustomTextField(
            name: 'outlined_field',
            labelText: 'Outlined Field',
            hintText: 'Enter text here...',
            variant: TextFieldVariant.outlined,
            prefixIcon: Icon(Icons.person),
          ),
          SizedBox(height: 16.h),
          
          CustomTextField(
            name: 'filled_field',
            labelText: 'Filled Field',
            hintText: 'Enter text here...',
            variant: TextFieldVariant.filled,
            prefixIcon: Icon(Icons.email),
          ),
          SizedBox(height: 16.h),
          
          CustomTextField(
            name: 'underlined_field',
            labelText: 'Underlined Field',
            hintText: 'Enter text here...',
            variant: TextFieldVariant.underlined,
            prefixIcon: Icon(Icons.phone),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchesDemo() {
    return GetBuilder<ThemeController>(
      builder: (themeController) => CustomCard(
        variant: CardVariant.elevated,
        child: Column(
          children: [
            // Theme Switch
            SettingsSwitch(
              title: 'Dark Mode',
              description: 'Toggle between light and dark theme',
              icon: Icons.dark_mode,
              value: themeController.isDarkMode,
              onChanged: (value) {
                if (value) {
                  themeController.setDarkTheme();
                } else {
                  themeController.setLightTheme();
                }
              },
            ),
            
            Divider(),
            
            // Custom Switches
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      'Basic Switch',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    SizedBox(height: 8.h),
                    CustomSwitch(
                      value: true,
                      onChanged: (value) {},
                    ),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      'Labeled Switch',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    SizedBox(height: 8.h),
                    CustomSwitch(
                      value: false,
                      onChanged: (value) {},
                      showLabels: true,
                      activeLabel: 'ON',
                      inactiveLabel: 'OFF',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
