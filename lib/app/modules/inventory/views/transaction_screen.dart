 // transaction_screen.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../themes/app_colors.dart';
import '../controllers/transaction_controller.dart';
import '../controllers/inventory_controller.dart';
import '../models/product_model.dart';
import '../models/transaction_model.dart';
import '../widgets/quantity_selector.dart';


class TransactionScreen extends GetView<TransactionController> {
  const TransactionScreen({Key? key}) : super(key: key);

  // Helper to get inventory controller if needed for selection
  InventoryController get inventoryController => Get.find<InventoryController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Obx(() => Text(controller.transactionTitle)),
        backgroundColor: AppColors.primary,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Transaction type indicator
            _buildTransactionHeader(context),
            const SizedBox(height: 16),

            // --- Product Selection Area ---
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.border, width: 1),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Products',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add_circle_outline),
                    label: const Text('Select Products'),
                    onPressed: () {
                      _showProductSelection(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // --- Selected Products Section ---
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.border, width: 1),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.shopping_cart,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Selected Products',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const Spacer(),
                      Obx(() => Text(
                        '${controller.selectedProductIds.length} items',
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      )),
                    ],
                  ),
                  const Divider(height: 24),

                  // --- List of Selected Products ---
                  Obx(() {
                    if (controller.selectedProductIds.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Lottie.network(
                                'https://assets1.lottiefiles.com/packages/lf20_qh5z2mj1.json',
                                width: 120,
                                height: 120,
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                'No products selected yet',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppColors.textSecondary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.selectedProductIds.length,
                      separatorBuilder: (context, index) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final productId = controller.selectedProductIds[index];
                        final product = controller.getSelectedProductDetails(productId);
                        final currentQty = controller.productQuantities[productId] ?? 0;

                        return Container(
                          padding: const EdgeInsets.symmetric(vertical: 12.0),
                          child: Row(
                            children: [
                              // Product color indicator
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Center(
                                  child: Text(
                                    product?.name.substring(0, 1).toUpperCase() ?? '?',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),

                              // Product details
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product?.name ?? 'Unknown Product',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                    if (product?.sku != null)
                                      Text(
                                        product!.sku!,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                  ],
                                ),
                              ),

                              // Quantity selector
                              QuantitySelectorWidget(
                                initialQuantity: currentQty.abs(),
                                onQuantityChanged: (newQuantity) {
                                  controller.updateQuantity(productId, newQuantity);
                                },
                                incrementStep: 5, // Allow larger increments
                              ),

                              // Remove button
                              IconButton(
                                icon: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: AppColors.error.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: AppColors.error,
                                    size: 16,
                                  ),
                                ),
                                onPressed: () => controller.removeProduct(productId),
                                tooltip: 'remove_item'.tr,
                                constraints: const BoxConstraints(),
                              )
                            ],
                          ),
                        );
                      },
                    );
                  }),
                ],
              ),
            ),

            const Spacer(),

            // --- Submit Button ---
            Obx(() => ElevatedButton.icon(
              icon: controller.isProcessing.value
                  ? SizedBox(width: 20, height: 20, child: const CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                  : const Icon(Icons.check_circle),
              label: Text(
                controller.isProcessing.value
                    ? 'Processing...'
                    : 'Confirm ${controller.transactionType.name.capitalizeFirst}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onPressed: controller.isProcessing.value || controller.selectedProductIds.isEmpty
                  ? null
                  : () => controller.submitTransaction(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            )),

            // Display processing errors if any
            Obx(() {
              if (controller.error.value.isNotEmpty) {
                return Container(
                  margin: const EdgeInsets.only(top: 8.0),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.error.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: AppColors.error),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.error.value,
                          style: const TextStyle(color: AppColors.error),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }

  // Transaction type header with icon and description
  Widget _buildTransactionHeader(BuildContext context) {
    // Define transaction type specific data
    IconData headerIcon;
    Color headerColor;
    String description;

    switch (controller.transactionType) {
      case TransactionType.purchase:
        headerIcon = Icons.add_shopping_cart;
        headerColor = AppColors.success;
        description = 'Record items purchased from suppliers to add to inventory.';
        break;
      case TransactionType.sale:
        headerIcon = Icons.point_of_sale;
        headerColor = AppColors.info;
        description = 'Record items sold to customers to reduce inventory.';
        break;
      case TransactionType.usage:
        headerIcon = Icons.construction;
        headerColor = AppColors.warning;
        description = 'Record items used in operations or maintenance.';
        break;
      case TransactionType.adjustment:
        headerIcon = Icons.tune;
        headerColor = AppColors.accent;
        description = 'Adjust inventory levels due to loss, damage, or counting.';
        break;
      default:
        headerIcon = Icons.inventory;
        headerColor = AppColors.primary;
        description = 'Record inventory transaction.';
    }

    return Container(
      decoration: BoxDecoration(
        color: headerColor,
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              headerIcon,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.transactionTitle,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // --- Placeholder for Product Selection UI ---
  // This could be a BottomSheet, a Dialog, or navigating to a dedicated searchable list screen
  void _showProductSelection(BuildContext context) {
    // Option 1: Simple Bottom Sheet (Good for fewer items)
    Get.bottomSheet(
      Container(
        height: MediaQuery.of(context).size.height * 0.6,
        color: Colors.white, // Or Theme based color
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('Select Product', style: Theme.of(context).textTheme.titleLarge),
            ),
            Expanded(
              // Use Obx on inventoryController's products here
              child: Obx(() => ListView.builder(
                itemCount: inventoryController.products.length,
                itemBuilder: (ctx, index) {
                  final product = inventoryController.products[index];
                  // Check if already selected to disable/indicate
                  final isSelected = controller.productQuantities.containsKey(product.id);
                  return ListTile(
                    title: Text(product.name),
                    subtitle: Text('Qty: ${product.currentQuantity}'),
                    trailing: isSelected ? const Icon(Icons.check_circle, color: Colors.green) : const Icon(Icons.add_circle_outline),
                    onTap: isSelected ? null : () => controller.selectProduct(product), // Select and close bottom sheet
                  );
                },
              ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true, // Important for height
    );

    // Option 2: Navigate to a dedicated selection screen
    // Get.toNamed(AppRoutes.productSelectionScreen, arguments: { 'currentSelection': controller.selectedProductIds });
  }
}
