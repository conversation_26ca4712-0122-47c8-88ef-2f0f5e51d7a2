import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../../../data/models/sector_model.dart';
import '../../../data/models/user_model.dart';

class SectorController extends GetxController {
  final sectors = <Sector>[].obs;
  final managers = <UserModel>[].obs;
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadSectors();
    loadManagers();
    // createSector('السلام','N6H8BrWZugciW5rAIbQi');
    // createSector('أحد','hN331ZnEPsOa5hV4H77u');
    // createSector('بدر','dhEIulU14il9LpKorEtI');
    // createSector('ينبع','dqCvdkdrqWAVYF2AA7PO');
    // createSector('KBR','earoTvBIMlnOh89hotcs');
    // createSector('العلاء','sCez9SxJq0IlEgZ6v0lN');
    // createSector('حنكية','1loUCcV4NYXZx5SKC3VX');
    // createSector('مهد','qsUBMwnPGL7mnH7xfkGz');
    // createSector('الحجر','6q5HyuHKgQj38RMKIzhf');
    // createSector('MIN','XCnu53smVurnV7lnjxcp');
    // createSector('الحرم','7etDeD6KraU7POSV6wKW');

  }

  Future<void> loadManagers() async {
    try {
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: UserRole.manager.name)
          .get();

      managers.value = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
      
      print('Loaded ${managers.length} managers');
    } catch (e) {
      print('Error loading managers: $e');
      Get.snackbar('Error', 'Failed to load managers: $e');
    }
  }

  Future<void> loadSectors() async {
    try {
      isLoading.value = true;
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('sectors')
          .get();

      sectors.value = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Sector.fromJson({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      print('Error loading sectors: $e');
      Get.snackbar('Error', 'Failed to load sectors');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createSector(String name, String managerId) async {
    try {
      isLoading.value = true;
      final sectorData = {
        'name': name,
        'managerId': managerId,
        'createAt': Timestamp.now(),
        'managerHistory': [],
      };

      await FirebaseFirestore.instance
          .collection('sectors')
          .add(sectorData);

      await loadSectors();
      Get.snackbar('Success', 'Sector created successfully!');
    } catch (e) {
      print('Error creating sector: $e');
      Get.snackbar('Error', 'Failed to create sector');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateSector(String sectorId, {String? name}) async {
    try {
      isLoading.value = true;
      final updates = <String, dynamic>{
        if (name != null) 'name': name,
      };

      await FirebaseFirestore.instance
          .collection('sectors')
          .doc(sectorId)
          .update(updates);

      await loadSectors();
      Get.snackbar('Success', 'Sector updated successfully!');
    } catch (e) {
      print('Error updating sector: $e');
      Get.snackbar('Error', 'Failed to update sector');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteSector(String sectorId) async {
    try {
      isLoading.value = true;
      await FirebaseFirestore.instance
          .collection('sectors')
          .doc(sectorId)
          .delete();

      await loadSectors();
      Get.snackbar('Success', 'Sector deleted successfully!');
    } catch (e) {
      print('Error deleting sector: $e');
      Get.snackbar('Error', 'Failed to delete sector');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> changeManager(String sectorId, String newManagerId, {String? reason, String? notes}) async {
    try {
      isLoading.value = true;
      
      final sectorDoc = await FirebaseFirestore.instance
          .collection('sectors')
          .doc(sectorId)
          .get();
      
      final sector = Sector.fromJson({...sectorDoc.data()!, 'id': sectorDoc.id});
      
      final transaction = ManagerTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        previousManagerId: sector.managerId,
        newManagerId: newManagerId,
        date: DateTime.now(),
        reason: reason,
        notes: notes,
      );

      final currentHistory = List<Map<String, dynamic>>.from(sectorDoc.data()?['managerHistory'] ?? []);
      currentHistory.add(transaction.toJson());

      await FirebaseFirestore.instance
          .collection('sectors')
          .doc(sectorId)
          .update({
        'managerId': newManagerId,
        'managerHistory': currentHistory,
      });

      await loadSectors();
      Get.snackbar('Success', 'Sector manager changed successfully!');
    } catch (e) {
      print('Error changing sector manager: $e');
      Get.snackbar('Error', 'Failed to change sector manager');
    } finally {
      isLoading.value = false;
    }
  }

  UserModel? getManagerForSector(String managerId) {
    return managers.firstWhereOrNull((manager) => manager.id == managerId);
  }
}
