// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../routes/app_pages.dart';
//
// class AuthMiddleware extends GetMiddleware {
//   final List<String> allowedRoles;
//
//   AuthMiddleware([this.allowedRoles = const []]);
//
//   @override
//   RouteSettings? redirect(String? route) {
//     final authController = Get.find<AuthController>();
//
//     print('Current Route: $route');
//     print('Is Logged In: ${authController.isLoggedIn.value}');
//     print('User Role: ${authController.userRole.value}');
//     print('Allowed Roles: $allowedRoles');
//
//     // If trying to access login page and already logged in, redirect to appropriate page
//     if (route == Routes.LOGIN && authController.isLoggedIn.value) {
//       final targetRoute = authController.getCurrentRoute();
//       print('Redirecting from login to: $targetRoute');
//       return RouteSettings(name: targetRoute);
//     }
//
//     // If not logged in and trying to access protected route, redirect to login
//     if (!authController.isLoggedIn.value && route != Routes.LOGIN) {
//       print('Not logged in, redirecting to login');
//       return const RouteSettings(name: Routes.HOME);
//     }
//
//     // If logged in but doesn't have required role, redirect to appropriate page
//     if (authController.isLoggedIn.value &&
//         allowedRoles.isNotEmpty &&
//         !authController.hasRole(allowedRoles)) {
//       final targetRoute = authController.getCurrentRoute();
//       print('User does not have required role, redirecting to: $targetRoute');
//       return RouteSettings(name: targetRoute);
//     }
//
//     print('No redirect needed');
//     return null;
//   }
// }
