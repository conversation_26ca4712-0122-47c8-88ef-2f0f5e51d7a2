

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';

class UserService extends GetxService{
  static UserService get instance => Get.find();
  final FirebaseFirestore _firestore = Get.find();


  Future<String> getUserTokenById(String userId) async {
    final DocumentSnapshot snapshot = await _firestore.collection('users').doc(userId).get();
    return snapshot['token'];
  }
  Future<String> getUserTokenBySectorId(String userId) async {
    final DocumentSnapshot snapshot = await _firestore.collection('sectors').doc(userId).get();
    return await getUserTokenById(snapshot['managerId']);
  }

}