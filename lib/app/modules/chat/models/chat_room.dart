import 'package:cloud_firestore/cloud_firestore.dart';

class ChatRoom {
  final String id;
  final String customerId;
  final String driverId;
  final String lastMessage;
  final DateTime lastMessageTime;
  final bool isDriverOnline;
  final bool isCustomerOnline;
  final Map<String, bool> typing;
  final Map<String, int> unreadCount;

  ChatRoom({
    required this.id,
    required this.customerId,
    required this.driverId,
    required this.lastMessage,
    required this.lastMessageTime,
    this.isDriverOnline = false,
    this.isCustomerOnline = false,
    required this.typing,
    required this.unreadCount,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      driverId: json['driverId'] as String,
      lastMessage: json['lastMessage'] as String,
      lastMessageTime: (json['lastMessageTime'] as Timestamp).toDate(),
      isDriverOnline: json['isDriverOnline'] as bool? ?? false,
      isCustomerOnline: json['isCustomerOnline'] as bool? ?? false,
      typing: Map<String, bool>.from(json['typing'] as Map),
      unreadCount: Map<String, int>.from(json['unreadCount'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'driverId': driverId,
      'lastMessage': lastMessage,
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'isDriverOnline': isDriverOnline,
      'isCustomerOnline': isCustomerOnline,
      'typing': typing,
      'unreadCount': unreadCount,
    };
  }

  ChatRoom copyWith({
    String? id,
    String? customerId,
    String? driverId,
    String? lastMessage,
    DateTime? lastMessageTime,
    bool? isDriverOnline,
    bool? isCustomerOnline,
    Map<String, bool>? typing,
    Map<String, int>? unreadCount,
  }) {
    return ChatRoom(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      driverId: driverId ?? this.driverId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      isDriverOnline: isDriverOnline ?? this.isDriverOnline,
      isCustomerOnline: isCustomerOnline ?? this.isCustomerOnline,
      typing: typing ?? this.typing,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}
