import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/language_controller.dart';

class AppDrawer extends StatelessWidget {
  final String selectedRoute;
  final Function(String route)? onItemTap;

  const AppDrawer({Key? key, required this.selectedRoute, this.onItemTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final items = [
      {
        'icon': Icons.dashboard_rounded,
        'title': 'dashboard'.tr,
        'route': '/dashboard',
      },
      {
        'icon': Icons.inventory_2_rounded,
        'title': 'inventory'.tr,
        'route': '/inventory',
      },
      {
        'icon': Icons.settings_rounded,
        'title': 'settings'.tr,
        'route': '/settings',
      },
    ];

    return Drawer(
      child: Container(
        color: colorScheme.surface,
        child: Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOut,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(32),
                  bottomRight: Radius.circular(32),
                ),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.primary.withOpacity(0.15),
                    blurRadius: 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
              width: double.infinity,
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: Colors.white,
                    child: Icon(Icons.person, color: colorScheme.primary, size: 40),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '<EMAIL>', // Replace with actual user info if available
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.85),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ...items.map((item) {
              final isSelected = selectedRoute == item['route'];
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? colorScheme.primary.withOpacity(0.12)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(14),
                ),
                child: ListTile(
                  leading: Icon(
                    item['icon'] as IconData,
                    color: isSelected ? colorScheme.primary : colorScheme.onSurface.withOpacity(0.7),
                  ),
                  title: Text(
                    item['title'] as String,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? colorScheme.primary : colorScheme.onSurface,
                    ),
                  ),
                  trailing: isSelected
                      ? AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: 6,
                          height: 32,
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        )
                      : null,
                  onTap: () {
                    if (onItemTap != null) {
                      onItemTap!(item['route'] as String);
                    } else {
                      Navigator.pop(context);
                      if (ModalRoute.of(context)?.settings.name != item['route']) {
                        Get.offAllNamed(item['route'] as String);
                      }
                    }
                  },
                ),
              );
            }).toList(),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Text(
                '© 2024 Company',
                style: TextStyle(
                  color: colorScheme.onSurface.withOpacity(0.5),
                  fontSize: 13,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
