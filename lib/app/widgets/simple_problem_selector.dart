import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../data/models/problem_tag_model.dart';
import '../services/problem_tag_service.dart';
import '../themes/app_colors.dart';

class SimpleProblemSelector extends StatefulWidget {
  final List<String> initialSelectedTagIds;
  final Function(List<String>) onTagsChanged;
  final String hintText;

  const SimpleProblemSelector({
    Key? key,
    required this.initialSelectedTagIds,
    required this.onTagsChanged,
    this.hintText = 'اختر المشاكل الشائعة',
  }) : super(key: key);

  @override
  State<SimpleProblemSelector> createState() => _SimpleProblemSelectorState();
}

class _SimpleProblemSelectorState extends State<SimpleProblemSelector> {
  final ProblemTagService _tagService = Get.find<ProblemTagService>();
  String _selectedCategory = '';
  List<String> _selectedTagIds = [];
  bool _isLoading = true;
  List<ProblemTag> _allTags = [];
  List<String> _categories = [];
  
  final TextEditingController _newTagController = TextEditingController();
  final TextEditingController _newCategoryController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _selectedTagIds = List.from(widget.initialSelectedTagIds);
    _loadData();
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    // Load tags if not already loaded
    if (_tagService.tags.isEmpty) {
      await _tagService.loadTags();
    }
    
    setState(() {
      _allTags = List.from(_tagService.tags);
      _categories = _getAllCategories();
      _isLoading = false;
    });
  }
  
  List<String> _getAllCategories() {
    final Set<String> categories = {};
    for (final tag in _allTags) {
      if (tag.category.isNotEmpty) {
        categories.add(tag.category);
      }
    }
    return categories.toList()..sort();
  }
  
  List<ProblemTag> _getTagsForCategory(String category) {
    return _allTags.where((tag) => tag.category == category).toList();
  }
  
  @override
  void dispose() {
    _newTagController.dispose();
    _newCategoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected Tags Section
        _buildSelectedTagsSection(),
        
        // Categories Section
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_categories.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'لا توجد فئات متاحة',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          )
        else
          _buildCategoriesSection(),
        
        // Subcategories Section (only shown when a category is selected)
        if (_selectedCategory.isNotEmpty)
          _buildSubcategoriesSection()
        else
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'اختر فئة لعرض المشاكل المتعلقة بها',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildSelectedTagsSection() {
    if (_selectedTagIds.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.primary.withOpacity(0.2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.check_circle, color: AppColors.primary, size: 18),
                  const SizedBox(width: 8),
                  const Text(
                    'المشاكل المحددة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: AppColors.primary,
                    ),
                  ),
                  const Spacer(),
                  TextButton.icon(
                    icon: const Icon(Icons.clear_all, size: 16),
                    label: const Text('مسح الكل'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    onPressed: () {
                      setState(() {
                        _selectedTagIds.clear();
                      });
                      widget.onTagsChanged(_selectedTagIds);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedTagIds.map((tagId) {
                  final tag = _allTags.firstWhereOrNull((t) => t.id == tagId) ?? 
                      ProblemTag(
                        id: tagId,
                        name: tagId, // Use ID as name if tag not found
                        category: 'custom',
                        createdAt: DateTime.now(),
                      );
                  
                  return Chip(
                    label: Text(tag.name),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        _selectedTagIds.remove(tagId);
                      });
                      widget.onTagsChanged(_selectedTagIds);
                    },
                    backgroundColor: Colors.white,
                    side: BorderSide(color: AppColors.primary.withOpacity(0.3)),
                    labelStyle: const TextStyle(color: AppColors.primary),
                    deleteIconColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
  
  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header with icon
        Container(
          margin: const EdgeInsets.only(bottom: 12, top: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.category, color: AppColors.primary, size: 20),
              ),
              const SizedBox(width: 8),
              const Text(
                'اختر الفئة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              // Add new category button
              // ElevatedButton.icon(
              //   icon: const Icon(Icons.add, size: 16),
              //   label: const Text('فئة جديدة'),
              //   onPressed: _showAddCategoryDialog,
              //   style: ElevatedButton.styleFrom(
              //     backgroundColor: AppColors.primary,
              //     foregroundColor: Colors.white,
              //     elevation: 0,
              //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              //     textStyle: const TextStyle(fontSize: 12),
              //     shape: RoundedRectangleBorder(
              //       borderRadius: BorderRadius.circular(8),
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
        
        // Categories as horizontal scrollable chips in a card
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 40,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    final isSelected = _selectedCategory == category;
                    
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child: ChoiceChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = selected ? category : '';
                            });
                          },
                          backgroundColor: Colors.white,
                          selectedColor: AppColors.primary.withOpacity(0.2),
                          labelStyle: TextStyle(
                            color: isSelected ? AppColors.primary : Colors.black87,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                          avatar: isSelected 
                            ? const Icon(Icons.check_circle, size: 18, color: AppColors.primary)
                            : null,
                          elevation: isSelected ? 2 : 0,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Show category count
              if (_categories.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8, right: 8),
                  child: Text(
                    'عدد الفئات: ${_categories.length}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
      ],
    );
  }
  
  Widget _buildSubcategoriesSection() {
    final categoryTags = _getTagsForCategory(_selectedCategory);
    
    // Container for subcategories section with consistent styling
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.8),
                  AppColors.primary.withOpacity(0.6),
                ],
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.tag, color: Colors.white, size: 18),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'المشاكل في فئة "$_selectedCategory"',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // ElevatedButton.icon(
                //   icon: const Icon(Icons.add, size: 16),
                //   label: const Text('إضافة مشكلة'),
                //   onPressed: () => _showAddTagDialog(_selectedCategory),
                //   style: ElevatedButton.styleFrom(
                //     backgroundColor: Colors.white,
                //     foregroundColor: AppColors.primary,
                //     elevation: 0,
                //     padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                //     textStyle: const TextStyle(fontSize: 12),
                //     shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(8),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: categoryTags.isEmpty
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(width: double.infinity),
                      Icon(
                        Icons.info_outline,
                        color: Colors.grey.shade400,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مشاكل في فئة "$_selectedCategory"',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة مشكلة جديدة'),
                        onPressed: () => _showAddTagDialog(_selectedCategory),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Tags count
                      Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Text(
                          'عدد المشاكل: ${categoryTags.length}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      
                      // Tags as chips
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 12,
                          children: categoryTags.map((tag) {
                            final isSelected = _selectedTagIds.contains(tag.id);
                            
                            return FilterChip(
                              label: Text(tag.name),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _selectedTagIds.add(tag.id);
                                    // Increment usage count in the background
                                    _tagService.incrementUsageCount(tag.id);
                                  } else {
                                    _selectedTagIds.remove(tag.id);
                                  }
                                });
                                widget.onTagsChanged(_selectedTagIds);
                              },
                              backgroundColor: Colors.white,
                              selectedColor: AppColors.primary.withOpacity(0.2),
                              checkmarkColor: AppColors.primary,
                              labelStyle: TextStyle(
                                color: isSelected ? AppColors.primary : Colors.black87,
                                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              elevation: isSelected ? 1 : 0,
                              pressElevation: 2,
                              avatar: isSelected 
                                ? const Icon(Icons.check_circle, size: 16, color: AppColors.primary)
                                : null,
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
  
  void _showAddTagDialog(String category) {
    _newCategoryController.text = category;
    _newTagController.clear();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.add_circle, color: AppColors.primary),
            ),
            const SizedBox(width: 12),
            const Text('إضافة مشكلة جديدة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _newTagController,
              decoration: InputDecoration(
                labelText: 'اسم المشكلة',
                hintText: 'أدخل اسم المشكلة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary, width: 2),
                ),
                prefixIcon: const Icon(Icons.label, color: AppColors.primary),
              ),
              autofocus: true,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _addNewTag(value.trim(), category);
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newCategoryController,
              decoration: InputDecoration(
                labelText: 'الفئة',
                hintText: 'أدخل اسم الفئة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.category, color: AppColors.primary),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              enabled: false, // Category is pre-selected
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم إضافة المشكلة الجديدة وتحديدها تلقائياً',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey,
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('إضافة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            ),
            onPressed: () {
              if (_newTagController.text.trim().isNotEmpty) {
                _addNewTag(_newTagController.text.trim(), category);
              }
            },
          ),
        ],
      ),
    );
  }
  
  void _addNewTag(String tagName, String category) async {
    final newTag = ProblemTag(
      id: '', // Will be set by Firestore
      name: tagName,
      category: category,
      usageCount: 1, // Start with 1 since we're using it
      createdAt: DateTime.now(),
    );
    
    // Store context before async gap
    final navigatorContext = context;
    
    final tagId = await _tagService.addTag(newTag);
    
    if (tagId.isNotEmpty) {
      setState(() {
        _selectedTagIds.add(tagId);
        _allTags = List.from(_tagService.tags);
      });
      widget.onTagsChanged(_selectedTagIds);
    }
    
    // Check if widget is still mounted before using context
    if (navigatorContext.mounted) {
      Navigator.of(navigatorContext).pop();
    }
  }
  
  void _showAddCategoryDialog() {
    final TextEditingController categoryController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.category, color: AppColors.primary),
            ),
            const SizedBox(width: 12),
            const Text('إضافة فئة جديدة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: categoryController,
              decoration: InputDecoration(
                labelText: 'اسم الفئة',
                hintText: 'أدخل اسم الفئة الجديدة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary, width: 2),
                ),
                prefixIcon: const Icon(Icons.label, color: AppColors.primary),
              ),
              autofocus: true,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _addNewCategory(value.trim());
                }
              },
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم إضافة الفئة الجديدة وتحديدها تلقائياً',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey,
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('إضافة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            ),
            onPressed: () {
              if (categoryController.text.trim().isNotEmpty) {
                _addNewCategory(categoryController.text.trim());
              }
            },
          ),
        ],
      ),
    ).then((_) {
      categoryController.dispose();
    });
  }
  
  void _addNewCategory(String categoryName) {
    // First, check if the category already exists
    if (!_categories.contains(categoryName)) {
      // Create a dummy tag to establish the category
      final newTag = ProblemTag(
        id: '',
        name: 'مثال - يمكنك إضافة مشاكل جديدة',
        category: categoryName,
        usageCount: 0,
        createdAt: DateTime.now(),
      );
      
      // Add the tag to create the category
      _tagService.addTag(newTag).then((_) {
        // Refresh the UI
        setState(() {
          _allTags = List.from(_tagService.tags);
          _categories = _getAllCategories();
          _selectedCategory = categoryName;
        });
      });
    } else {
      // If category already exists, just select it
      setState(() {
        _selectedCategory = categoryName;
      });
    }
    
    // Close the dialog
    Navigator.of(context).pop();
  }
}
